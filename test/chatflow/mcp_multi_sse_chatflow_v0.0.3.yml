app:
  description: 'Test for MCP client as Dify Agent Strategy plugin.

    used "Everything MCP server"

    Resource and Prompt are working correctly.

    <PERSON><PERSON> also Succussed, except for "samplingLLM", "annotatedMessage"'
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: multi MCP server test
  use_icon_as_answer_icon: true
dependencies: []
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions: []
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - remote_url
      - local_file
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 1
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: agent
      id: 1741367649354-source-1742356283732-target
      source: '1741367649354'
      sourceHandle: source
      target: '1742356283732'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: agent
        targetType: answer
      id: 1742356283732-source-answer-target
      source: '1742356283732'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: Start
        type: start
        variables: []
      height: 53
      id: '1741367649354'
      position:
        x: 87.25729350925093
        y: 282
      positionAbsolute:
        x: 87.25729350925093
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        answer: '{{#1742356283732.text#}}'
        desc: ''
        selected: false
        title: Answer
        type: answer
        variables: []
      height: 101
      id: answer
      position:
        x: 629.2361265621643
        y: 282
      positionAbsolute:
        x: 629.2361265621643
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        author: 3Dify-developer
        desc: ''
        height: 194
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"font-size:
          16px;","text":"User prompt base","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":1,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"I''m developing MCP client as Dify Agent Strategy Plugin.","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"- Some features haven''t implemented and might not work as
          I expected.","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"- I found good MCP server for testing.","type":"text","version":1},{"type":"linebreak","version":1},{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"- You should ignore \"Current Time Tool\". ","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 569
      height: 194
      id: '1741624678131'
      position:
        x: 87.25729350925093
        y: 508.8200615915433
      positionAbsolute:
        x: 87.25729350925093
        y: 508.8200615915433
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 569
    - data:
        author: 3Dify-developer
        desc: ''
        height: 88
        selected: false
        showAuthor: false
        text: '{"root":{"children":[{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"font-size:
          16px;","text":"Sample MCP server for testing","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"link","version":1,"rel":"noreferrer","target":null,"title":null,"url":""}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":1,"textStyle":"font-size:
          16px;"},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"https://github.com/modelcontextprotocol/servers/tree/main/src/everything","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: cyan
        title: ''
        type: ''
        width: 449
      height: 88
      id: '1741626728667'
      position:
        x: 690.5542795067578
        y: 763.3257650174893
      positionAbsolute:
        x: 690.5542795067578
        y: 763.3257650174893
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 449
    - data:
        author: 3Dify-developer
        desc: ''
        height: 151
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"font-size:
          16px;","text":"MCP Tool Test prompt","type":"text","version":1},{"type":"linebreak","version":1},{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"Try mcp tools one-by-one","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":1,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"-  judge result in \"thought\" phase (I want to know at least
          Fail or Success)","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"- avoid \"sampleLLM\" which is not compatible feature yet.
          ","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: green
        title: ''
        type: ''
        width: 570
      height: 151
      id: '1741669149712'
      position:
        x: 87.25729350925093
        y: 718.2693074279789
      positionAbsolute:
        x: 87.25729350925093
        y: 718.2693074279789
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 570
    - data:
        author: 3Dify-developer
        desc: ''
        height: 140
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"font-size:
          16px;","text":"MCP Resource Test prompt","type":"text","version":1},{"type":"linebreak","version":1},{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"Try mcp resources one-by-one","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":1,"textStyle":"font-size:
          16px;"},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"-  judge result in \"thought\" phase (I want to know at least
          Fail or Success)","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: green
        title: ''
        type: ''
        width: 568
      height: 140
      id: '1741669231167'
      position:
        x: 88.65225939320432
        y: 879.6113451588606
      positionAbsolute:
        x: 88.65225939320432
        y: 879.6113451588606
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 568
    - data:
        author: 3Dify-developer
        desc: ''
        height: 128
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"font-size:
          16px;","text":"MCP Prompt Test prompt","type":"text","version":1},{"type":"linebreak","version":1},{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"Try mcp prompts one-by-one","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":1,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"-  judge result in \"thought\" phase (I want to know at least
          Fail or Success)","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: green
        title: ''
        type: ''
        width: 568
      height: 128
      id: '1741669352719'
      position:
        x: 87.25729350925093
        y: 1033.9889783833733
      positionAbsolute:
        x: 87.25729350925093
        y: 1033.9889783833733
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 568
    - data:
        author: 3Dify-developer
        desc: ''
        height: 264
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"config.json example ","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"(","type":"text","version":1},{"detail":0,"format":1,"mode":"normal","style":"font-size:
          16px;","text":"Cloud?","type":"text","version":1},{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":")","type":"text","version":1},{"type":"linebreak","version":1},{"detail":0,"format":0,"mode":"normal","style":"","text":"{","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"  \"mcpServers\":
          {","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"    \"everything\":
          {","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"      \"url\":
          \"http://127.0.0.1:8080/sse\"","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"    },","type":"text","version":1}],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"    \"simple-arxiv\":
          {","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"      \"url\":
          \"http://127.0.0.1:8008/sse\"","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"    }","type":"text","version":1}],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"  }","type":"text","version":1}],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"}","type":"text","version":1}],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: pink
        title: ''
        type: ''
        width: 283
      height: 264
      id: '1741671860068'
      position:
        x: 1027.6182377664975
        y: 988.1065026922056
      positionAbsolute:
        x: 1027.6182377664975
        y: 988.1065026922056
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 283
    - data:
        author: 3Dify-developer
        desc: ''
        height: 93
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"TOOL LIST should not blank. Choose some plugin.","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          14px;"},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"I recommend official Dify plugin like \"Current Time\".","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":"font-size:
          14px;"}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: yellow
        title: ''
        type: ''
        width: 363
      height: 93
      id: '1741766414431'
      position:
        x: 289.33163665746144
        y: 178.12817056680163
      positionAbsolute:
        x: 289.33163665746144
        y: 178.12817056680163
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 363
    - data:
        author: 3Dify-developer
        desc: ''
        height: 362
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"font-size:
          16px;","text":"Example query","type":"text","version":1},{"type":"linebreak","version":1},{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"I''m developing MCP client as Dify Agent Strategy Plugin.","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":1,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"- Some features haven''t implemented and might not work as
          I expected.","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"- I found good MCP server for testing.","type":"text","version":1},{"type":"linebreak","version":1},{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"- You should ignore \"Current Time Tool\". ","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"At first, tell me all tools you are available in ","type":"text","version":1},{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"\"thought\".","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"Try mcp tools one-by-one","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"-  judge result in \"thought\" phase (I want to know at least
          Fail or Success)","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"- avoid \"sampleLLM\" which is not compatible feature yet.
          ","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 502
      height: 362
      id: '1741772186537'
      position:
        x: 673.1166803440168
        y: 375.05515998236154
      positionAbsolute:
        x: 673.1166803440168
        y: 375.05515998236154
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 502
    - data:
        author: 3Dify-developer
        desc: ''
        height: 97
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"font-size:
          16px;","text":"Instruction","type":"text","version":1},{"type":"linebreak","version":1},{"detail":0,"format":0,"mode":"normal","style":"","text":"You
          are helpful LLM Agent","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":1,"textStyle":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: violet
        title: ''
        type: ''
        width: 240
      height: 97
      id: '1741802957289'
      position:
        x: 87.25729350925093
        y: 375.05515998236154
      positionAbsolute:
        x: 87.25729350925093
        y: 375.05515998236154
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 240
    - data:
        agent_parameters:
          config_json:
            type: constant
            value: "{\n  \"mcpServers\": {\n    \"everything\": {\n        \"url\"\
              : \"http://host.docker.internal:8080/sse\"\n    },\n    \"simple-arxiv\"\
              : {\n        \"url\": \"http://host.docker.internal:8008/sse\"\n   \
              \ }\n  }\n}"
          instruction:
            type: constant
            value: You are helpful LLM Agent
          maximum_iterations:
            type: constant
            value: 3
          model:
            type: constant
            value:
              completion_params: {}
              mode: chat
              model: gemini-2.0-flash-exp
              model_type: llm
              provider: langgenius/gemini/google
              type: model-selector
          query:
            type: constant
            value: '{{#sys.query#}}'
          tools:
            type: constant
            value:
            - enabled: true
              extra:
                description: ''
              parameters: {}
              provider_name: time
              schemas:
              - auto_generate: null
                default: '%Y-%m-%d %H:%M:%S'
                form: form
                human_description:
                  en_US: Time format in strftime standard.
                  ja_JP: Time format in strftime standard.
                  pt_BR: Time format in strftime standard.
                  zh_Hans: strftime 标准的时间格式。
                label:
                  en_US: Format
                  ja_JP: Format
                  pt_BR: Format
                  zh_Hans: 格式
                llm_description: null
                max: null
                min: null
                name: format
                options: []
                placeholder: null
                precision: null
                required: false
                scope: null
                template: null
                type: string
              - auto_generate: null
                default: UTC
                form: form
                human_description:
                  en_US: Timezone
                  ja_JP: Timezone
                  pt_BR: Timezone
                  zh_Hans: 时区
                label:
                  en_US: Timezone
                  ja_JP: Timezone
                  pt_BR: Timezone
                  zh_Hans: 时区
                llm_description: null
                max: null
                min: null
                name: timezone
                options:
                - label:
                    en_US: UTC
                    ja_JP: UTC
                    pt_BR: UTC
                    zh_Hans: UTC
                  value: UTC
                - label:
                    en_US: America/New_York
                    ja_JP: America/New_York
                    pt_BR: America/New_York
                    zh_Hans: 美洲/纽约
                  value: America/New_York
                - label:
                    en_US: America/Los_Angeles
                    ja_JP: America/Los_Angeles
                    pt_BR: America/Los_Angeles
                    zh_Hans: 美洲/洛杉矶
                  value: America/Los_Angeles
                - label:
                    en_US: America/Chicago
                    ja_JP: America/Chicago
                    pt_BR: America/Chicago
                    zh_Hans: 美洲/芝加哥
                  value: America/Chicago
                - label:
                    en_US: America/Sao_Paulo
                    ja_JP: America/Sao_Paulo
                    pt_BR: América/São Paulo
                    zh_Hans: 美洲/圣保罗
                  value: America/Sao_Paulo
                - label:
                    en_US: Asia/Shanghai
                    ja_JP: Asia/Shanghai
                    pt_BR: Asia/Shanghai
                    zh_Hans: 亚洲/上海
                  value: Asia/Shanghai
                - label:
                    en_US: Asia/Ho_Chi_Minh
                    ja_JP: Asia/Ho_Chi_Minh
                    pt_BR: Ásia/Ho Chi Minh
                    zh_Hans: 亚洲/胡志明市
                  value: Asia/Ho_Chi_Minh
                - label:
                    en_US: Asia/Tokyo
                    ja_JP: Asia/Tokyo
                    pt_BR: Asia/Tokyo
                    zh_Hans: 亚洲/东京
                  value: Asia/Tokyo
                - label:
                    en_US: Asia/Dubai
                    ja_JP: Asia/Dubai
                    pt_BR: Asia/Dubai
                    zh_Hans: 亚洲/迪拜
                  value: Asia/Dubai
                - label:
                    en_US: Asia/Kolkata
                    ja_JP: Asia/Kolkata
                    pt_BR: Asia/Kolkata
                    zh_Hans: 亚洲/加尔各答
                  value: Asia/Kolkata
                - label:
                    en_US: Asia/Seoul
                    ja_JP: Asia/Seoul
                    pt_BR: Asia/Seoul
                    zh_Hans: 亚洲/首尔
                  value: Asia/Seoul
                - label:
                    en_US: Asia/Singapore
                    ja_JP: Asia/Singapore
                    pt_BR: Asia/Singapore
                    zh_Hans: 亚洲/新加坡
                  value: Asia/Singapore
                - label:
                    en_US: Europe/London
                    ja_JP: Europe/London
                    pt_BR: Europe/London
                    zh_Hans: 欧洲/伦敦
                  value: Europe/London
                - label:
                    en_US: Europe/Berlin
                    ja_JP: Europe/Berlin
                    pt_BR: Europe/Berlin
                    zh_Hans: 欧洲/柏林
                  value: Europe/Berlin
                - label:
                    en_US: Europe/Moscow
                    ja_JP: Europe/Moscow
                    pt_BR: Europe/Moscow
                    zh_Hans: 欧洲/莫斯科
                  value: Europe/Moscow
                - label:
                    en_US: Australia/Sydney
                    ja_JP: Australia/Sydney
                    pt_BR: Australia/Sydney
                    zh_Hans: 澳大利亚/悉尼
                  value: Australia/Sydney
                - label:
                    en_US: Pacific/Auckland
                    ja_JP: Pacific/Auckland
                    pt_BR: Pacific/Auckland
                    zh_Hans: 太平洋/奥克兰
                  value: Pacific/Auckland
                - label:
                    en_US: Africa/Cairo
                    ja_JP: Africa/Cairo
                    pt_BR: Africa/Cairo
                    zh_Hans: 非洲/开罗
                  value: Africa/Cairo
                placeholder: null
                precision: null
                required: false
                scope: null
                template: null
                type: select
              settings:
                format:
                  value: '%Y-%m-%d %H:%M:%S'
                timezone:
                  value: UTC
              tool_label: Current Time
              tool_name: current_time
              type: builtin
        agent_strategy_label: mcpReAct
        agent_strategy_name: mcpReAct
        agent_strategy_provider_name: 3dify-project/mcp_client/agent
        desc: ''
        output_schema: null
        plugin_unique_identifier: 3dify-project/mcp_client:0.0.2@0a8f8d04a49549adc0fbe6118da99f846239aad74b5ede932e8bb40cec531491
        selected: false
        title: Agent
        type: agent
      height: 197
      id: '1742356283732'
      position:
        x: 361.24246556756697
        y: 282
      positionAbsolute:
        x: 361.24246556756697
        y: 282
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        author: 3Dify-developer
        desc: ''
        height: 264
        selected: false
        showAuthor: false
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"config.json example ","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":"(","type":"text","version":1},{"detail":0,"format":1,"mode":"normal","style":"font-size:
          16px;","text":"Docker Compose self deploy","type":"text","version":1},{"detail":0,"format":0,"mode":"normal","style":"font-size:
          16px;","text":")","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"{","type":"text","version":1}],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"  \"mcpServers\":
          {","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"    \"everything\":
          {","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"        \"url\":
          \"http://host.docker.internal:8080/sse\"","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"    },","type":"text","version":1}],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"    \"simple-arxiv\":
          {","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"        \"url\":
          \"http://host.docker.internal:8008/sse\"","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"    }","type":"text","version":1}],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"  }","type":"text","version":1}],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"}","type":"text","version":1}],"direction":null,"format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: pink
        title: ''
        type: ''
        width: 315
      height: 264
      id: '1742367595361'
      position:
        x: 690.5542795067578
        y: 988.1065026922056
      positionAbsolute:
        x: 690.5542795067578
        y: 988.1065026922056
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 315
    - data:
        author: 3Dify-developer
        desc: ''
        height: 97
        selected: false
        showAuthor: false
        text: '{"root":{"children":[{"children":[{"detail":0,"format":1,"mode":"normal","style":"font-size:
          16px;","text":"How to convert \"stdio\" to \"SSE\" MCP server","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":1,"textStyle":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"README
          of my GitHub repository","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""},{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"https://github.com/3dify-project/dify-mcp-client","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"link","version":1,"rel":"noreferrer","target":null,"title":null,"url":"https://github.com/3dify-project/dify-mcp-client"}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0,"textStyle":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: cyan
        title: ''
        type: ''
        width: 447
      height: 97
      id: '1742452980902'
      position:
        x: 690.5542795067578
        y: 863.8984826162116
      positionAbsolute:
        x: 690.5542795067578
        y: 863.8984826162116
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 447
    viewport:
      x: 73.56256265549337
      y: -104.78783867265224
      zoom: 0.8487039665217165
