import json
import re
from collections.abc import Generator
from typing import Union

from dify_plugin.entities.model.llm import LLMResultChunk
from dify_plugin.interfaces.agent import AgentScratchpadUnit


class CotAgentOutputParser:
    @classmethod
    def handle_react_stream_output(
        cls, llm_response: Generator[LLMResultChunk, None, None], usage_dict: dict
    ) -> Generator[Union[str, AgentScratchpadUnit.Action], None, None]:
        def parse_action(json_str):
            try:
                action = json.loads(json_str, strict=False)
                action_name = None
                action_input = None

                # cohere always returns a list
                if isinstance(action, list) and len(action) == 1:
                    action = action[0]

                # 优先处理标准格式: {"action": "tool_name", "action_input": {...}}
                if "action" in action and "action_input" in action:
                    action_name = action["action"]
                    action_input = action["action_input"]
                # 处理带 parameters 的格式: {"action": "tool_name", "parameters": {...}}
                elif "action" in action and "parameters" in action:
                    action_name = action["action"]
                    action_input = action["parameters"]
                # 处理只有工具名和参数的格式，没有显式的 "action" 字段
                # 假设第一个字段是工具名，第二个字段是参数
                elif len(action) >= 2:
                    items = list(action.items())
                    # 查找可能的参数字段（包含 input, parameters, args 等关键字）
                    param_fields = ["action_input", "parameters", "params", "args", "input"]
                    
                    for key, value in items:
                        if any(param_key in key.lower() for param_key in param_fields):
                            action_input = value
                            # 找到参数字段后，将其他字段作为工具名
                            for other_key, other_value in items:
                                if other_key != key:
                                    action_name = other_value
                                    break
                            break
                    
                    # 如果没有找到明确的参数字段，使用传统逻辑
                    if action_name is None or action_input is None:
                        for key, value in action.items():
                            if "input" in key.lower():
                                action_input = value
                            else:
                                action_name = value
                # 处理单一字段的情况（可能只有工具名）
                elif len(action) == 1:
                    key, value = list(action.items())[0]
                    if "input" in key.lower() or "param" in key.lower():
                        action_input = value
                        action_name = "unknown_action"
                    else:
                        action_name = value
                        action_input = {}

                print(f"[PARSER] Parsed JSON: {action}")
                print(f"[PARSER] Extracted action_name: {action_name}")
                print(f"[PARSER] Extracted action_input: {action_input}")

                if action_name is not None and action_input is not None:
                    return AgentScratchpadUnit.Action(
                        action_name=action_name,
                        action_input=action_input,
                    )
                else:
                    return json_str or ""
            except Exception as e:
                print(f"[PARSER] Error parsing JSON: {e}")
                return json_str or ""
        
        def filter_think_tags(content: str) -> str:
            """Remove <think>...</think> tags from content"""
            # Remove <think>...</think> blocks (including multiline)
            content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
            return content

        def extra_json_from_code_block(
            code_block,
        ) -> Generator[Union[str, AgentScratchpadUnit.Action], None, None]:
            code_blocks = re.findall(r"```(.*?)```", code_block, re.DOTALL)
            if not code_blocks:
                return
            for block in code_blocks:
                json_text = re.sub(
                    r"^[a-zA-Z]+\n", "", block.strip(), flags=re.MULTILINE
                )
                yield parse_action(json_text)

        code_block_cache = ""
        code_block_delimiter_count = 0
        in_code_block = False
        json_cache = ""
        json_quote_count = 0
        in_json = False
        got_json = False

        action_cache = ""
        action_str = "action:"
        action_idx = 0

        thought_cache = ""
        thought_str = "thought:"
        thought_idx = 0

        last_character = ""
        
        # Track <think> tags to filter them out
        think_tag_cache = ""
        in_think_tag = False
        think_start_pattern = "<think>"
        think_end_pattern = "</think>"
        think_start_idx = 0
        think_end_idx = 0

        for response in llm_response:
            if response.delta.usage:
                usage_dict["usage"] = response.delta.usage
            response_content = response.delta.message.content
            if not isinstance(response_content, str):
                continue

            # Filter out <think>...</think> content
            response_content = filter_think_tags(response_content)
            
            # Skip empty content after filtering
            if not response_content:
                continue

            # stream
            index = 0
            while index < len(response_content):
                steps = 1
                delta = response_content[index : index + steps]
                yield_delta = False

                if delta == "`":
                    last_character = delta
                    code_block_cache += delta
                    code_block_delimiter_count += 1
                else:
                    if not in_code_block:
                        if code_block_delimiter_count > 0:
                            last_character = delta
                            yield code_block_cache
                        code_block_cache = ""
                    else:
                        last_character = delta
                        code_block_cache += delta
                    code_block_delimiter_count = 0

                if not in_code_block and not in_json:
                    if delta.lower() == action_str[action_idx] and action_idx == 0:
                        if last_character not in {"\n", " ", ""}:
                            yield_delta = True
                        else:
                            last_character = delta
                            action_cache += delta
                            action_idx += 1
                            if action_idx == len(action_str):
                                action_cache = ""
                                action_idx = 0
                            index += steps
                            continue
                    elif delta.lower() == action_str[action_idx] and action_idx > 0:
                        last_character = delta
                        action_cache += delta
                        action_idx += 1
                        if action_idx == len(action_str):
                            action_cache = ""
                            action_idx = 0
                        index += steps
                        continue
                    else:
                        if action_cache:
                            last_character = delta
                            yield action_cache
                            action_cache = ""
                            action_idx = 0

                    if delta.lower() == thought_str[thought_idx] and thought_idx == 0:
                        if last_character not in {"\n", " ", ""}:
                            yield_delta = True
                        else:
                            last_character = delta
                            thought_cache += delta
                            thought_idx += 1
                            if thought_idx == len(thought_str):
                                thought_cache = ""
                                thought_idx = 0
                            index += steps
                            continue
                    elif delta.lower() == thought_str[thought_idx] and thought_idx > 0:
                        last_character = delta
                        thought_cache += delta
                        thought_idx += 1
                        if thought_idx == len(thought_str):
                            thought_cache = ""
                            thought_idx = 0
                        index += steps
                        continue
                    else:
                        if thought_cache:
                            last_character = delta
                            yield thought_cache
                            thought_cache = ""
                            thought_idx = 0

                    if yield_delta:
                        index += steps
                        last_character = delta
                        yield delta
                        continue

                if code_block_delimiter_count == 3:
                    if in_code_block:
                        last_character = delta
                        yield from extra_json_from_code_block(code_block_cache)
                        code_block_cache = ""

                    in_code_block = not in_code_block
                    code_block_delimiter_count = 0

                if not in_code_block:
                    # handle single json
                    if delta == "{":
                        json_quote_count += 1
                        in_json = True
                        last_character = delta
                        json_cache += delta
                    elif delta == "}":
                        last_character = delta
                        json_cache += delta
                        if json_quote_count > 0:
                            json_quote_count -= 1
                            if json_quote_count == 0:
                                in_json = False
                                got_json = True
                                index += steps
                                continue
                    else:
                        if in_json:
                            last_character = delta
                            json_cache += delta

                    if got_json:
                        got_json = False
                        last_character = delta
                        yield parse_action(json_cache)
                        json_cache = ""
                        json_quote_count = 0
                        in_json = False

                if not in_code_block and not in_json:
                    last_character = delta
                    yield delta.replace("`", "")

                index += steps

        if code_block_cache:
            yield code_block_cache

        if json_cache:
            yield parse_action(json_cache)
            
        # 处理截断的JSON - 如果存在未闭合的JSON，尝试补全并解析
        if in_json and json_cache and not got_json:
            print(f"[PARSER] Detected incomplete JSON, attempting to parse: {json_cache}")
            # 尝试补全JSON闭合括号
            incomplete_json = json_cache.strip()
            if incomplete_json.endswith(','):
                incomplete_json = incomplete_json[:-1]  # 移除末尾逗号
                
            # 计算需要补全的闭合括号数量
            open_braces = incomplete_json.count('{')
            close_braces = incomplete_json.count('}')
            needed_braces = open_braces - close_braces
            
            if needed_braces > 0:
                completed_json = incomplete_json + '}' * needed_braces
                print(f"[PARSER] Attempting to parse completed JSON: {completed_json}")
                yield parse_action(completed_json)
