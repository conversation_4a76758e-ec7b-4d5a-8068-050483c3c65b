查询Agent 根据用户的query，查询统计数据

query可能没有指定表，key字段，查询字段，时间范围，时间单位

需要agent根据query，查询到对应的表，key字段，查询字段，时间范围，时间单位

RAG的办法



User -> 
PlanAgent -> 如果满足(分析时间序列模式：)则下一步  -> 使用二分查找优化定位： -> 如果找到则下一步  -> 使用在线解码工具解码数据包 -> 如果满足则生成报告，否则上报本次不满足条件
^                                                          ^                                                          ^
|                                                          |                                                          |
QueryAgent                                              QueryAgent                                              DecodeAgent



@Untitled-5.ini  参考这个文档，你调用目前已经有的mcp服务，@科来接口文档.md ，参考API文档，生成query，和写入类似格式的训练数据

需要实际调用mcp工具就生成了，我是要你实际的生成数据
比如你不知道链路id，就调用对应获取配置
不知道表名称，就获取对应的工具 
不知道表有哪些key，就获取对应的表字段 
请重新执行获取 
链路一有数据，数据范围是2025/06/17-2025/06/18 