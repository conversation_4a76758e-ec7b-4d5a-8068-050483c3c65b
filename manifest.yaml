version: 0.0.5
type: plugin
author: 3dify-project
name: mcp_client
label:
  en_US: mcp_client
  ja_JP: mcp_client
  zh_Hans: mcp_client
  pt_BR: mcp_client
description:
  en_US: Claude MCP Client as Dify plugi (not MCP server). Use Node.js pre-installed container like memedayo/dify-plugin-daemon when using UI-TARS-SDK.
  ja_JP: Claude MCP Client as Dify plugi (not MCP server). Use Node.js pre-installed container like memedayo/dify-plugin-daemon when using UI-TARS-SDK.
  zh_Hans: Claude MCP Client as Dify plugi (not MCP server). Use Node.js pre-installed container like memedayo/dify-plugin-daemon when using UI-TARS-SDK.
  pt_BR: Claude MCP Client as Dify plugi (not MCP server). Use Node.js pre-installed container like memedayo/dify-plugin-daemon when using UI-TARS-SDK.
icon: icon.svg
resource:
  memory: 268435456
  permission:
    tool:
      enabled: true
    model:
      enabled: true
      llm: true
      text_embedding: false
      rerank: false
      tts: false
      speech2text: false
      moderation: false
    endpoint:
      enabled: true
    app:
      enabled: true
    storage:
      enabled: true
      size: 1048576
plugins:
  agent_strategies:
    - provider/agent.yaml
meta:
  version: 0.0.5
  arch:
    - amd64
    - arm64
  runner:
    language: python
    version: "3.12"
    entrypoint: main
created_at: 2025-03-04T16:50:45.7131549+09:00
privacy: PRIVACY.md
verified: false
