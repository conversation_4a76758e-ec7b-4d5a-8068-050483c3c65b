{"meta": {"session_type": "待诊断（由步骤2确定）", "analysis_focus": "TCP重传率100%异常分析", "key_filtering_strategy": "五元组精准过滤（服务器IP/端口 + 客户端IP/端口）", "total_steps": 9, "key_filtering_enhanced": true, "validation_time": "2025-06-18T14:59:24.618191"}, "steps": [{"step_id": 1, "tool": "setup_api_connection", "description": "建立MCP API连接", "parameters": {"machine_ip": "*************", "netlink_id": 2}, "expected_outcome": "API连接认证成功", "conditional_next": "step_2"}, {"step_id": 2, "tool": "query_statistics_table", "description": "查询TCP会话基础信息", "parameters": {"table_name": "tcp_flow", "fields": ["flow_start_time", "flow_end_time", "flow_duration"], "filters": "server_ip_addr=*********** && server_port=80 && client_ip_addr=*********** && client_port=10800", "timeunit": 0, "key": "默认KEY"}, "expected_outcome": "获取会话持续时间和起止时间点", "conditional_next": {"condition": "flow_duration <= 2", "true": "step_3_short", "false": "step_3_long"}}, {"step_id": "step_3_short", "tool": "get_detailed_packet_decode", "description": "短期会话在线解码（持续≤2秒）", "parameters": {"packet_filters": "server_ip_addr=*********** && server_port=80 && client_ip_addr=*********** && client_port=10800", "decode_level": "full", "time_range": "##step_2.output.flow_start_time## 至 ##step_2.output.flow_end_time##"}, "expected_outcome": "检查SEQ字段是否存在跳变（如1000000→4294967295）", "conditional_next": {"condition": "detected_seq_jump", "true": "step_4_confirm", "false": "end_failure"}}, {"step_id": "step_3_long", "tool": "query_statistics_table", "description": "长期会话初始查询（持续>2秒）", "parameters": {"table_name": "tcp_flow", "fields": ["bucket_start_time", "bucket_end_time", "tcp_retransmission_rate"], "filters": "server_ip_addr=*********** && server_port=80 && client_ip_addr=*********** && client_port=10800", "time_range": "##step_2.output.flow_start_time## 至 ##step_2.output.flow_end_time##", "group_by": "time_bucket(10m)"}, "expected_outcome": "获取10分钟桶重传率，定位首个100%重传率桶", "conditional_next": {"condition": "found_abnormal_bucket", "true": "step_4_refine", "false": "end_failure"}}, {"step_id": "step_4_refine", "tool": "query_statistics_table", "description": "分层缩小时间桶定位异常点", "parameters": {"table_name": "tcp_flow", "fields": ["bucket_start_time", "bucket_end_time", "tcp_retransmission_rate"], "filters": "server_ip_addr=*********** && server_port=80 && client_ip_addr=*********** && client_port=10800", "time_range": "##step_3_long.output.abnormal_bucket_time_range##", "group_by": "time_bucket(1m)"}, "expected_outcome": "获取1分钟桶重传率，精确定位异常时间段", "conditional_next": {"condition": "found_abnormal_bucket", "true": "step_5_decode", "false": "end_failure"}}, {"step_id": "step_5_decode", "tool": "get_detailed_packet_decode", "description": "异常时间段详细包解析", "parameters": {"packet_filters": "server_ip_addr=*********** && server_port=80 && client_ip_addr=*********** && client_port=10800", "decode_level": "full", "time_range": "##step_4_refine.output.abnormal_bucket_time_range##"}, "expected_outcome": "检查SEQ字段是否存在跳变（如1000000→4294967295）", "conditional_next": "step_4_confirm"}, {"step_id": "step_4_confirm", "tool": "local_analysis", "description": "序列号跳变验证", "parameters": {"input_data": "##previous_step.output##"}, "expected_outcome": "确认序列号跳变导致丢包（设备序列号溢出）", "conditional_next": "end_success"}, {"step_id": "end_success", "tool": "report_generator", "description": "生成诊断报告", "parameters": {"conclusion": "序列号跳变导致丢包（设备序列号溢出）"}, "expected_outcome": "输出技术分析报告", "conditional_next": null}, {"step_id": "end_failure", "tool": "report_generator", "description": "生成诊断报告", "parameters": {"conclusion": "未检测到序列号跳变，需检查统计算法"}, "expected_outcome": "输出失败分析报告", "conditional_next": null}], "branches": {"short_session_flow": [1, 2, "step_3_short", "step_4_confirm", "end_success"], "long_session_flow": [1, 2, "step_3_long", "step_4_refine", "step_5_decode", "step_4_confirm", "end_success"]}}