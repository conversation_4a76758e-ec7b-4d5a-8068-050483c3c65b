# LLM函数传入方式使用说明

## 概述

现在系统支持通过函数参数的方式传入LLM调用函数，这样设计的优势：

1. **灵活性**：可以传入任何LLM实现，不依赖特定的session结构
2. **可扩展性**：以后可以轻松切换不同的LLM提供商
3. **可测试性**：可以传入模拟函数进行测试
4. **解耦合**：经验管理器不需要知道LLM的具体实现

## 使用方法

### 1. 在mcpReAct策略中使用

```python
# 在 strategies/mcpReAct.py 中
def llm_func(prompt: str) -> str:
    return session.model.llm.invoke(prompt)

await self.experience_manager.save_experience(
    query=query,
    final_answer=final_answer,
    execution_time=total_duration,
    token_usage=total_tokens,
    tools_used=tools_used,
    success=success,
    iterations=detailed_iterations,
    llm_func=llm_func  # 传入LLM函数
)
```

### 2. 在测试中使用模拟函数

```python
def mock_llm_func(prompt: str) -> str:
    """模拟LLM响应"""
    return """```json
[
  {
    "step": 1,
    "description": "登录到***************的API服务器",
    "action_json": "{'action': 'stats-query-mcp_mcp_tool_setup_api_connection', 'action_input': {'url': 'https://***************:8080/'}}"
  }
]
```"""

await em.save_experience(
    query="测试查询",
    final_answer="测试答案",
    execution_time=10.0,
    token_usage=100,
    tools_used=["tool1"],
    success=True,
    iterations=test_iterations,
    llm_func=mock_llm_func
)
```

### 3. 支持不同的LLM提供商

```python
# OpenAI
def openai_llm_func(prompt: str) -> str:
    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[{"role": "user", "content": prompt}]
    )
    return response.choices[0].message.content

# 智谱AI
def zhipu_llm_func(prompt: str) -> str:
    response = zhipu_client.chat.completions.create(
        model="glm-4",
        messages=[{"role": "user", "content": prompt}]
    )
    return response.choices[0].message.content

# 本地LLM
def local_llm_func(prompt: str) -> str:
    return local_model.generate(prompt)
```

## 函数签名要求

LLM函数必须符合以下签名：

```python
def llm_func(prompt: str) -> str:
    """
    Args:
        prompt: 输入的提示词
        
    Returns:
        LLM的文本响应
    """
    pass
```

## 工作流程

1. **保存经验时**：
   ```python
   # 如果提供了llm_func且有迭代步骤
   if llm_func and iterations:
       # 构建提示词
       prompt = self._build_step_summary_prompt(query, iterations)
       
       # 调用LLM函数
       response = llm_func(prompt)
       
       # 解析JSON响应
       step_summaries = self._parse_llm_response(response)
       
       # 存储到metadata中
       metadata["llm_step_summaries"] = step_summaries
   ```

2. **生成few-shot示例时**：
   ```python
   # 检查是否有存储的LLM总结
   llm_summaries = experience.metadata.get("llm_step_summaries", [])
   
   if llm_summaries:
       # 使用存储的LLM总结
       for summary in llm_summaries:
           example += f"{summary['step']}. {summary['description']}\n"
           example += f"{summary['action_json']}\n"
   else:
       # 回退到传统方法
       # ...
   ```

## 配置选项

在 `experience_config.json` 中：

```json
{
  "experience_manager": {
    "use_llm_for_step_summary": true  // 启用LLM步骤总结
  }
}
```

## 测试命令

```bash
# 测试存储时LLM总结功能
python test_llm_summarizer.py storage

# 对比传统方法和LLM方法
python test_llm_summarizer.py compare
```

## 输出示例

### 传统方法输出：
```
1. 登录API服务器
2. 查询链路配置获取链路ID信息  
3. 查询summary表的统计数据
```

### LLM方法输出：
```
1. 登录到***************的API服务器
2. 查询netlink配置获取链路一的ID信息
3. 查询summary表获取指定时间段的统计数据
```

可以看到LLM方法生成的描述更加具体和准确。

## 错误处理

系统具有完善的错误处理机制：

1. **LLM函数未提供**：跳过步骤总结，使用传统方法
2. **LLM调用失败**：记录错误日志，回退到传统方法
3. **JSON解析失败**：尝试多种解析方式，失败时回退到传统方法

## 扩展性

这种设计使得系统可以轻松扩展：

1. **支持异步LLM函数**：可以修改为接受async函数
2. **支持流式响应**：可以修改为支持流式LLM调用
3. **支持批量处理**：可以修改为批量处理多个查询
4. **支持自定义解析**：可以传入自定义的响应解析函数

## 最佳实践

1. **错误处理**：LLM函数应该包含适当的错误处理
2. **超时设置**：设置合理的超时时间
3. **重试机制**：对于网络错误实现重试
4. **日志记录**：记录LLM调用的详细信息
5. **成本控制**：监控LLM调用的成本和频率
