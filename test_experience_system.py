#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
经验管理系统测试脚本
"""

import asyncio
import json
from experience_manager import ExperienceManager, QueryExperience, IterationStep, ToolExecution


async def test_experience_system():
    """测试经验管理系统"""
    print("=" * 60)
    print("测试经验管理系统")
    print("=" * 60)
    
    # 初始化经验管理器
    try:
        em = ExperienceManager()
        print("✓ 经验管理器初始化成功")
    except Exception as e:
        print(f"✗ 经验管理器初始化失败: {e}")
        return
    
    # 创建测试经验数据
    test_experience = create_test_experience()
    
    # 测试保存经验
    print("\n测试保存经验...")
    success = await em.save_experience(
        query=test_experience.query,
        final_answer=test_experience.final_answer,
        execution_time=test_experience.execution_time,
        token_usage=test_experience.token_usage,
        tools_used=test_experience.tools_used,
        success=test_experience.success,
        iterations=test_experience.iterations
    )
    
    if success:
        print("✓ 经验保存成功")
    else:
        print("✗ 经验保存失败")
    
    # 测试查找相似经验
    print("\n测试查找相似经验...")
    similar_query = "查询192.168.1.100上的网络统计数据"
    similar_experiences = await em.find_similar_experiences(similar_query)
    
    if similar_experiences:
        print(f"✓ 找到 {len(similar_experiences)} 个相似经验")
        for i, (exp, similarity) in enumerate(similar_experiences):
            print(f"  {i+1}. 相似度: {similarity:.3f}, 查询: {exp.query[:50]}...")
    else:
        print("✗ 未找到相似经验")
    
    # 测试生成few-shot示例
    print("\n测试生成few-shot示例...")
    if similar_experiences:
        exp, similarity = similar_experiences[0]
        few_shot_example = em.generate_detailed_few_shot_example(exp, similarity)
        print("✓ Few-shot示例生成成功:")
        print("-" * 40)
        print(few_shot_example)
        print("-" * 40)
    else:
        print("✗ 无法生成few-shot示例（没有相似经验）")
    
    # 测试执行指导生成
    print("\n测试执行指导生成...")
    if similar_experiences:
        guidance = em.generate_execution_guidance(similar_query, similar_experiences)
        print("✓ 执行指导生成成功:")
        print("-" * 40)
        print(guidance[:500] + "..." if len(guidance) > 500 else guidance)
        print("-" * 40)
    else:
        print("✗ 无法生成执行指导（没有相似经验）")
    
    # 测试经验摘要
    print("\n测试经验摘要...")
    summary = em.get_experience_summary()
    print("✓ 经验摘要:")
    print(f"  总经验数: {summary['total_experiences']}")
    print(f"  成功率: {summary['success_rate']:.2%}")
    print(f"  平均执行时间: {summary['avg_execution_time']:.2f}s")
    
    # 测试模式分析
    print("\n测试模式分析...")
    patterns = em.analyze_query_patterns()
    print("✓ 模式分析完成:")
    print(f"  平均迭代次数: {patterns['average_iterations']:.1f}")
    print(f"  常见工具序列数: {len(patterns['common_tool_sequences'])}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


def create_test_experience() -> QueryExperience:
    """创建测试经验数据"""
    # 创建工具执行记录
    tool_executions = [
        ToolExecution(
            tool_name="stats-query-mcp_mcp_tool_setup_api_connection",
            parameters={"url": "https://192.168.163.209:8080/"},
            response='{"success": true, "message": "API连接设置成功"}',
            execution_time=2.1,
            success=True,
            timestamp="2025-07-17 10:00:00"
        ),
        ToolExecution(
            tool_name="stats-query-mcp_mcp_tool_get_config",
            parameters={"config_type": "netlink", "netlink_id": 64},
            response='{"success": true, "xml": "<netlink><link id=\\"1\\" name=\\"链路一\\"/>"}',
            execution_time=1.5,
            success=True,
            timestamp="2025-07-17 10:00:02"
        ),
        ToolExecution(
            tool_name="stats-query-mcp_mcp_tool_query_statistics_table",
            parameters={
                "table": "summary",
                "begintime": "2025-07-17 00:00:00",
                "endtime": "2025-07-17 23:59:59",
                "fields": "total_byte,total_packet,total_bitps",
                "keys": "time",
                "netlink": 1
            },
            response='{"success": true, "data": "total_byte,total_packet,total_bitps\\n923644955558,**********,85522681.070180"}',
            execution_time=15.2,
            success=True,
            timestamp="2025-07-17 10:00:05"
        )
    ]
    
    # 创建迭代步骤
    iterations = [
        IterationStep(
            iteration=1,
            thought="我需要先连接到API服务器",
            action="stats-query-mcp_mcp_tool_setup_api_connection",
            action_input={"url": "https://192.168.163.209:8080/"},
            observation="API连接成功",
            tool_executions=[tool_executions[0]],
            duration=2.1
        ),
        IterationStep(
            iteration=2,
            thought="现在需要获取链路配置信息",
            action="stats-query-mcp_mcp_tool_get_config",
            action_input={"config_type": "netlink", "netlink_id": 64},
            observation="获取到链路配置，链路一的ID是1",
            tool_executions=[tool_executions[1]],
            duration=1.5
        ),
        IterationStep(
            iteration=3,
            thought="现在可以查询统计数据了",
            action="stats-query-mcp_mcp_tool_query_statistics_table",
            action_input={
                "table": "summary",
                "begintime": "2025-07-17 00:00:00",
                "endtime": "2025-07-17 23:59:59",
                "fields": "total_byte,total_packet,total_bitps",
                "keys": "time",
                "netlink": 1
            },
            observation="查询成功，获取到统计数据",
            tool_executions=[tool_executions[2]],
            duration=15.2
        )
    ]
    
    # 创建查询经验
    return QueryExperience(
        query="查询192.168.163.209 上链路名称为链路一的2025-07-17一天的概要统计",
        query_hash="test_hash_123",
        final_answer='{"success": true, "error_code": 0, "message": "查询成功", "data": "total_byte,total_packet,total_bitps\\n923644955558,**********,85522681.070180"}',
        execution_time=21.3,
        token_usage=1250,
        tools_used=[
            "stats-query-mcp_mcp_tool_setup_api_connection",
            "stats-query-mcp_mcp_tool_get_config", 
            "stats-query-mcp_mcp_tool_query_statistics_table"
        ],
        success=True,
        timestamp="2025-07-17 10:00:00",
        iterations=iterations,
        total_iterations=3,
        embedding=None,
        metadata={"model": "gpt-4", "provider": "openai"}
    )


if __name__ == "__main__":
    asyncio.run(test_experience_system())
