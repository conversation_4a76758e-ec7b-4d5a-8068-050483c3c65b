{"meta": {"generated_at": "2025-06-18T11:37:18.970350", "alert_info": {"server_ip": "*************", "link_id": 2, "trigger_time": "2025-06-16 14:00:00", "alert_name": "TCP重传率100%警报", "condition": "TCP重传率为100% 大于 90%"}, "total_steps": 4, "has_branches": true, "diagnostic_strategy": "hybrid", "total_branches": 2, "estimated_total_duration": "40-90秒"}, "steps": [{"step_id": 1, "step_type": "sequential", "tool": "setup_api_connection", "purpose": "设置统计API连接", "parameters": {"url": "https://***************:8080/"}, "reasoning": "建立与网络监控API的连接，获取诊断所需数据", "required": true, "estimated_duration": "2-5秒"}, {"step_id": 2, "step_type": "sequential", "tool": "query_statistics_table", "purpose": "确定会话持续时间和重传情况", "parameters": {"table": "service_access", "begintime": "2025-06-16 13:50:00", "endtime": "2025-06-16 14:00:00", "fields": ["server_ip_addr", "total_byte", "total_packet", "protocol", "session_start_time", "session_end_time"], "keys": ["server_ip_addr"], "timeunit": 1000, "filter_condition": "server_ip_addr=*************", "netlink": 2, "topcount": 600}, "reasoning": "通过秒级统计确定会话类型，这是选择后续诊断路径的关键判断点", "required": true, "estimated_duration": "3-8秒", "decision_point": {"condition": "session_duration", "branches": ["short_session", "long_session"]}}, {"step_id": 4, "step_type": "sequential", "tool": "get_detailed_packet_decode", "purpose": "跳包确认和根因分析", "parameters": {"begin_time": "2025-06-16 13:59:59", "end_time": "2025-06-16 14:00:00", "server_ip": "*************", "protocol": "TCP", "link_id": 2, "max_packets": 200, "decode_options": "full", "analysis_depth": "deep"}, "reasoning": "最终通过详细数据包解码确认跳包特征和根本原因，生成诊断结论", "required": true, "estimated_duration": "20-35秒"}], "branches": {"short_session": {"condition": "session_duration <= 2秒", "description": "短期会话诊断路径：重点检查序列号跳变", "steps": [{"step_id": "3A", "step_type": "conditional", "branch": "short_session", "tool": "get_detailed_packet_decode", "purpose": "短期会话序列号跳变检测", "parameters": {"begin_time": "2025-06-16 13:59:59", "end_time": "2025-06-16 14:00:00", "server_ip": "*************", "protocol": "TCP", "link_id": 2, "max_packets": 100, "decode_options": "detailed", "focus_fields": ["seq_num", "ack_num", "flags"]}, "reasoning": "短期会话需要详细解码第1秒的数据包，检查序列号是否异常跳变（如从正常值跳到4294967295）", "success_criteria": "检测到序列号跳变 > 1000000", "estimated_duration": "10-15秒"}]}, "long_session": {"condition": "session_duration > 2秒", "description": "长期会话诊断路径：时间桶分析+二分查找", "steps": [{"step_id": "3B1", "step_type": "conditional", "branch": "long_session", "tool": "query_statistics_table", "purpose": "长期会话时间桶分析", "parameters": {"table": "ip_flow", "begintime": "2025-06-16 13:00:00", "endtime": "2025-06-16 14:00:00", "fields": ["ip_endpoint1", "total_byte", "total_packet", "tcp_syn_packet", "tcp_retransmission_rate"], "keys": ["ip_endpoint1"], "timeunit": 60000, "filter_condition": "ip_endpoint1=*************", "netlink": 2, "topcount": 60}, "reasoning": "长期会话需要分钟级时间桶分析，识别连续重传模式，为二分查找提供基础", "estimated_duration": "5-10秒"}, {"step_id": "3B2", "step_type": "conditional", "branch": "long_session", "tool": "query_statistics_table", "purpose": "二分查找定位异常开始时间点", "parameters": {"table": "summary", "begintime": "2025-06-16 12:00:00", "endtime": "2025-06-16 14:00:00", "fields": ["time", "total_byte", "total_utilization", "tcp_retransmission_rate"], "keys": ["time"], "timeunit": 1000, "filter_condition": "", "netlink": 2, "topcount": 7200}, "reasoning": "使用二分查找算法逐步缩小时间桶（10分钟→5分钟→1分钟→10秒→1秒），精确定位跳包开始时间点", "algorithm": "binary_search", "estimated_duration": "15-30秒"}]}}}