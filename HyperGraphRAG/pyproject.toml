[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "hypergraphrag"
version = "1.0.6"
authors = [
    {name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"},
]
description = "HyperGraphRAG: A knowledge graph enhanced retrieval-augmented generation system"
readme = "README.md"
license = {file = "LICENSE"}
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "torch==2.3.0",
    "accelerate",
    "aioboto3",
    "aiohttp",
    "graspologic",
    "hnswlib",
    "nano-vectordb",
    "neo4j",
    "networkx",
    "ollama",
    "openai",
    "oracledb",
    "pymilvus",
    "pymongo",
    "pymysql",
    "pyvis",
    "sqlalchemy",
    "tenacity",
    "tiktoken",
    "transformers",
    "xxhash",
    "jsonlines",
    "nltk",
    "PyPDF2",
]

[project.urls]
Homepage = "https://github.com/HKUDS/HyperGraphRAG"
Repository = "https://github.com/HKUDS/HyperGraphRAG"
Issues = "https://github.com/HKUDS/HyperGraphRAG/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["hypergraphrag*"]

[tool.setuptools.package-data]
hypergraphrag = ["*.json", "*.txt", "*.md"] 