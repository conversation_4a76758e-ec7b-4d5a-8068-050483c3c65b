{"doc-d82c40f7535b227488145538691ccad8": {"content": "## 跳包\n    问题现象：\n    长时间出现重传率100%，下载数据包后分析是正常的\n\n    排查步骤：\n    1. 首先确定会话持续时间，区分短期会话（≤2秒）和长期会话（>2秒）\n    2. 短期会话场景：使用第1秒的`在线解码`判断seq号跳变\n    3. 长期会话场景：使用`查询统计表工具`，二分查找定位第一个出现100%重传前的时间点（秒）\n\n    诊断结果：\n    如果发现序列号跳变，确认为跳包；否则可能是统计算法问题\n    \n\n\n    ## 案例1：短期TCP会话序列号跳变\n    问题现象：\n    服务器 ip:***********, 服务器端口:80, 客户端ip:***********, 客户端端口:10800, 在2025-06-16 14:00:00触发TCP重传率100%告警\n    会话持续时间仅2秒，第2秒触发告警\n\n    排查步骤：\n    1. 查询TCP会话基本信息（tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和会话结束时间）\n    2. 发现会话在13:59:58-14:00:00期间，持续2秒\n    3. 使用在线解码分析13:59:59-14:00:00的数据包\n    4"}, "doc-f70ac9a747055e50ed2781430908f6a1": {"content": ". 发现第1秒最后一个包seq=1000000，正常，第2秒开始出现seq=4294967295的远大于正常seq的异常大包，如果出现则为序列号跳变导致的跳包，否则不是这个问题导致的\n\n    诊断结果：\n    如果存在序列号跳变，则确认为序列号跳变导致的跳包。设备在处理序列号时出现溢出，导致后续包被认为是重传。\n    \n\n\n    ## 案例2：长期会话连续重传\n    问题现象：\n    服务器 ip:***********, 服务器端口:80, 客户端ip:***********, 客户端端口:10800, 在2025-06-16 15:30:00触发告警\n    会话已持续30分钟，从15:20开始连续10分钟重传率100%\n\n    排查步骤：\n    1. 查询TCP会话基本信息（tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和会话结束时间）\n    2. 发现TCP会话持续30分钟，持续时间为2025-06-16 15:00:00-2025-06-16 15:30:00\n    3"}, "doc-1cbbf3fdea6e68d53615746b6ac981c7": {"content": ". 使用二分查找定位到异常时间点，比如这个是30分钟持续，则先指定时间桶为10分钟，查询会话开始时间到会话结束时间，检查tcp_retransmission_rate是否不为100%，找不到则退出判断，找到则继续下一步，在第最后一个不为100%时间范围继续按照刚才步骤排查，直到时间桶指定为1秒\n    举例说明：查询出来的数据可能为：\n    2025-06-16 15:00:00-2025-06-16 15:10:00 重传率10%\n    2025-06-16 15:10:00-2025-06-16 15:20:00 重传率95%\n    2025-06-16 15:20:00-2025-06-16 15:30:00 重传率100%\n    则需要继续在2025-06-16 15:10:00-2025-06-16 15:20:00这个时间范围，指定时间桶为5分钟，继续排查（5分钟，1分钟，10秒，1秒），直到1s桶\n    4. 找到15:19:45为重传率不为100%的时间点，则继续诊断\n    5. 对15:19:45进行详细包解码\n    6. 检查Seq是否存在跳变，如果存在则确认为序列号跳变导致的跳包，否则不是这个问题导致的\n\n    诊断结果：\n    如果存在序列号跳变，则确认为序列号跳变导致的跳包。设备在处理序列号时出现溢出，导致后续包被认为是重传。"}}