<?xml version='1.0' encoding='utf-8'?>
<graphml xmlns="http://graphml.graphdrawing.org/xmlns" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://graphml.graphdrawing.org/xmlns http://graphml.graphdrawing.org/xmlns/1.0/graphml.xsd">
  <key id="d6" for="edge" attr.name="source_id" attr.type="string" />
  <key id="d5" for="edge" attr.name="weight" attr.type="double" />
  <key id="d4" for="node" attr.name="description" attr.type="string" />
  <key id="d3" for="node" attr.name="entity_type" attr.type="string" />
  <key id="d2" for="node" attr.name="source_id" attr.type="string" />
  <key id="d1" for="node" attr.name="weight" attr.type="double" />
  <key id="d0" for="node" attr.name="role" attr.type="string" />
  <graph edgedefault="undirected">
    <node id="&lt;hyperedge&gt;&quot;TCP会话诊断需检查tcp_flow表获取会话起止时间&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">8.0</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;如果发现序列号跳变，确认为跳包；否则可能是统计算法问题&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">24.0</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;使用二分查找定位异常时间点：30分钟持续检查中，先指定10分钟时间桶查询会话起止时间，检查tcp_retransmission_rate是否不为100%，未找到则退出，找到则继续在最后一个非100%时间范围逐步缩小时间桶排查&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">9.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;使用二分查找定位异常时间点：以30分钟持续为例，先指定10分钟时间桶检查TCP重传率是否非100%，未找到则退出，找到则继续在最后一个非100%时段逐步缩小时间桶至1秒&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">9.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;发现第1秒最后一个包seq=1000000正常，第2秒开始出现seq=4294967295的异常大包，如果出现则为序列号跳变导致的跳包，否则不是这个问题导致的&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">36.0</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;持续30分钟会话中出现连续10分钟100%重传率触发告警&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">9.0</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;服务器ip:***********,服务器端口:80,客户端ip:***********,客户端端口:10800,在2025-06-16 14:00:00触发TCP重传率100%告警；会话持续时间仅2秒，第2秒触发告警&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">28.0</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;示例数据：三个连续时间桶（15:00-15:10重传率10%、15:10-15:20重传率95%、15:20-15:30重传率100%）&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">8.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;示例数据：三个连续时间桶（2025-06-16 15:00-15:10重传率10%、15:10-15:20重传率95%、15:20-15:30重传率100%）&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">8.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;如果存在序列号跳变，则确认为序列号跳变导致的跳包；设备在处理序列号时出现溢出，导致后续包被认为是重传&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">30.0</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;诊断需确认会话实际持续时间是否匹配告警时段&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">8.0</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;查询TCP会话基本信息（tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和会话结束时间）；发现会话在13:59:58-14:00:00期间，持续2秒；使用在线解码分析13:59:59-14:00:00的数据包&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">21.0</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;针对重传率95%的时间段（15:10-15:20）进一步划分时间桶（5分钟、1分钟、10秒、1秒）进行排查&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">8.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;针对重传率95%时段（15:10-15:20）进一步划分时间桶（5分钟/1分钟/10秒/1秒）排查&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">8.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">9.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;长期会话连续重传问题现象：服务器IP:***********，服务器端口:80，客户端IP:***********，客户端端口:10800，在2025-06-16 15:30:00触发告警，会话持续30分钟，从15:20开始连续10分钟重传率100%&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">24.0</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;示例数据分段：2025-06-16 15:10:00-15:20:00时段重传率95%，需继续以5分钟/1分钟/10秒/1秒桶排查&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">8.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;定位到15:19:45为重传率未达100%的具体时间点&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">7.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;定位15:19:45为非100%重传率时间点后进入详细诊断流程&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">7.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，只查询会话开始时间和结束时间，发现会话持续30分钟&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">14.0</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;定位15:19:45为非100%重传率时间点后，进行详细包解码并检查Seq跳变&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">8.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;对15:19:45时间点进行详细数据包解码&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">7.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;对15:19:45时间点执行详细数据包解码操作&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">7.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;首先确定会话持续时间，区分短期会话（≤2秒）和长期会话（&gt;2秒）；短期会话场景：使用第1秒的在线解码判断seq号跳变；长期会话场景：使用查询统计表工具，二分查找定位第一个出现100%重传前的时间点（秒）&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">27.0</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和结束时间，发现会话持续30分钟&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">7.0</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;若存在Seq跳变则确认为序列号跳变导致的跳包，原因为设备处理序列号时溢出&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">8.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&lt;knowledge_segment&gt;">
      <data key="d0">hyperedge</data>
      <data key="d1">2.0</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8&lt;SEP&gt;chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;检查数据包序列号（Seq）是否出现跳变，以此确认是否为序列号跳变导致的丢包&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">8.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;检查数据包Seq字段是否存在跳变现象以确认序列号异常&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">8.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;设备序列号溢出导致后续包被误判为重传包&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">10.0</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">9.0</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&lt;hyperedge&gt;&quot;长时间出现重传率100%，下载数据包后分析是正常的&quot;">
      <data key="d0">hyperedge</data>
      <data key="d1">30.0</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&quot;二分查找&quot;">
      <data key="d0">entity</data>
      <data key="d3">"ALGORITHM"</data>
      <data key="d4">"在长期会话中高效定位问题时间点的搜索算法"&lt;SEP&gt;"用于高效定位网络异常时间点的搜索算法，通过分阶段缩小时间范围精确定位故障时刻"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8&lt;SEP&gt;chunk-********************************</data>
    </node>
    <node id="&quot;会话持续时间&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"TCP会话从开始到结束的总时长"&lt;SEP&gt;"TCP会话从开始到结束的总时长，用于区分诊断方法"&lt;SEP&gt;"TCP连接从建立到关闭的总时间跨度"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8&lt;SEP&gt;chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;100%重传率&quot;">
      <data key="d0">entity</data>
      <data key="d3">"NETWORK_METRIC"</data>
      <data key="d4">"全部数据包需要重传的极端网络指标"</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;TCP会话&quot;">
      <data key="d0">entity</data>
      <data key="d3">"PROTOCOL"</data>
      <data key="d4">"传输控制协议会话，用于可靠数据传输"&lt;SEP&gt;"传输控制协议会话，用于可靠的数据传输"</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;SEQ&quot;">
      <data key="d0">entity</data>
      <data key="d3">"PROTOCOL"</data>
      <data key="d4">"TCP协议序列号字段，其跳变现象是诊断丢包的关键观察点"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;10分钟&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"初始时间桶大小，用于初步划分诊断时段"&lt;SEP&gt;"初始时间桶粒度，用于第一轮异常时段粗筛"&lt;SEP&gt;"连续高重传率的持续时间"&lt;SEP&gt;"连续高重传率的持续时间，指示持续性网络问题"</data>
      <data key="d2">chunk-********************************&lt;SEP&gt;chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;包解码&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TOOL"</data>
      <data key="d4">"深度解析数据包内容的技术，用于检查序列号(Seq)等关键字段"&lt;SEP&gt;"深度解析数据包原始内容的技术，用于检查Seq字段等关键信息"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;重传率95%&quot;">
      <data key="d0">entity</data>
      <data key="d3">"NETWORK_METRIC"</data>
      <data key="d4">"临界异常指标值，指示潜在丢包问题并触发次级时间桶分析"&lt;SEP&gt;"临界高重传率指标，指示潜在网络异常但尚未完全故障"&lt;SEP&gt;"示例中观测到的TCP重传率具体值95%，指示高重传率异常"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;TCP会话基本信息&quot;">
      <data key="d0">entity</data>
      <data key="d3">"PROTOCOL"</data>
      <data key="d4">"包含TCP连接元数据的基础信息"&lt;SEP&gt;"查询TCP会话基本属性的诊断方法"&lt;SEP&gt;"查询TCP会话基本属性的诊断方法，包括开始和结束时间"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8&lt;SEP&gt;chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;100%重传&quot;">
      <data key="d0">entity</data>
      <data key="d3">"NETWORK_METRIC"</data>
      <data key="d4">"所有数据包均需重传的极端网络指标"&lt;SEP&gt;"所有数据包均需重传的极端网络指标，表示严重传输故障"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&quot;2秒&quot;">
      <data key="d0">entity</data>
      <data key="d3">"THRESHOLD"</data>
      <data key="d4">"区分短期会话与长期会话的关键时间阈值"&lt;SEP&gt;"区分短期会话的时间阈值"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&quot;连续重传&quot;">
      <data key="d0">entity</data>
      <data key="d3">"SYMPTOM"</data>
      <data key="d4">"长时间持续发生的包重传现象，指示严重网络故障"</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;告警&quot;">
      <data key="d0">entity</data>
      <data key="d3">"SYMPTOM"</data>
      <data key="d4">"网络问题触发的警报事件"&lt;SEP&gt;"网络问题触发的警报事件，表示检测到异常情况"</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&lt;ENTITY_NAME&gt;">
      <data key="d0">entity</data>
      <data key="d3">&lt;ENTITY_TYPE&gt;</data>
      <data key="d4">&lt;entity_description&gt;</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;会话开始时间&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"TCP会话开始的时间点"&lt;SEP&gt;"TCP会话起始时刻，时间桶查询的关键参数"&lt;SEP&gt;"TCP会话起始的时间点，用于诊断会话持续时间"&lt;SEP&gt;"TCP连接建立时刻，时间桶查询的起始边界参数"</data>
      <data key="d2">chunk-********************************&lt;SEP&gt;chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;SEQ跳变&quot;">
      <data key="d0">entity</data>
      <data key="d3">"SYMPTOM"</data>
      <data key="d4">"TCP序列号异常跳跃现象，导致接收端丢弃后续数据包的核心故障特征"&lt;SEP&gt;"TCP序列号异常跳跃现象，导致设备误判后续数据包为重复传输"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;5分钟&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"次级时间桶单位，在95%重传率时段内进一步缩小诊断范围"&lt;SEP&gt;"次级诊断时间桶单位，用于缩小异常定位范围"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;TCP_FLOW表&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TOOL"</data>
      <data key="d4">"存储TCP会话信息的数据库表工具"&lt;SEP&gt;"存储TCP会话元数据的数据库表，用于查询连接持续时间等关键参数"&lt;SEP&gt;"存储TCP流数据的数据库表，用于查询会话信息"&lt;SEP&gt;"用于存储和查询TCP流数据的数据库表"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8&lt;SEP&gt;chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;时间点（秒）&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"以秒为单位的精确故障发生时刻"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&quot;告警触发&quot;">
      <data key="d0">entity</data>
      <data key="d3">"SYMPTOM"</data>
      <data key="d4">"系统自动检测到异常指标后发出的警报事件"</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;SEQ=1000000&quot;">
      <data key="d0">entity</data>
      <data key="d3">"NETWORK_METRIC"</data>
      <data key="d4">"第1秒检测到的正常TCP序列号基准值"</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;第1秒&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"诊断中的起始时间参考点，用于比较序列号正常状态"&lt;SEP&gt;"诊断中的起始时间点"</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;会话结束时间&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"TCP会话终止时刻，时间桶查询的关键参数"&lt;SEP&gt;"TCP会话终止的时间点，用于计算会话长度"&lt;SEP&gt;"TCP会话结束的时间点"&lt;SEP&gt;"TCP连接终止时刻，时间桶查询的终止边界参数"</data>
      <data key="d2">chunk-********************************&lt;SEP&gt;chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;下载数据包后分析&quot;">
      <data key="d0">entity</data>
      <data key="d3">"DIAGNOSTIC_METHOD"</data>
      <data key="d4">"通过下载完整数据包进行深度检查的基础诊断方法"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&quot;序列号跳变导致的跳包&quot;">
      <data key="d0">entity</data>
      <data key="d3">"DIAGNOSTIC_METHOD"</data>
      <data key="d4">"最终诊断结论：因序列号异常跳跃引发的丢包问题"&lt;SEP&gt;"最终诊断结论：因序列号异常跳跃引发的数据包丢弃问题"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;1分钟&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"精细化时间桶单位，逐步逼近异常时刻"&lt;SEP&gt;"精细化时间桶单位，逼近异常时间点的中间步骤"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;TIMEUNIT=0&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"时间单位参数设置为0表示原始时间精度分析"&lt;SEP&gt;"表示时间统计单位为秒的参数配置"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8&lt;SEP&gt;chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;序列号跳变&quot;">
      <data key="d0">entity</data>
      <data key="d3">"SYMPTOM"</data>
      <data key="d4">"TCP序列号出现异常的大幅跳跃，通常导致数据包丢失"&lt;SEP&gt;"TCP序列号发生异常突变导致数据包被错误识别为重传包的故障现象"&lt;SEP&gt;"TCP序列号异常大幅跳跃，导致数据包丢失"&lt;SEP&gt;"TCP序列号异常跳跃现象，表明数据包突发性丢失"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8&lt;SEP&gt;chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;在线解码&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TOOL"</data>
      <data key="d4">"实时解析数据包内容以检测序列号异常的工具"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&quot;SEQ=4294967295&quot;">
      <data key="d0">entity</data>
      <data key="d3">"SYMPTOM"</data>
      <data key="d4">"指示序列号跳变的异常大序列号（接近32位最大值）"</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;第2秒&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"诊断中的时间参考点，用于检测序列号异常跳变"&lt;SEP&gt;"诊断中的时间点，用于检测异常序列号"</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;TCP_RETRANSMISSION_RATE&quot;">
      <data key="d0">entity</data>
      <data key="d3">"NETWORK_METRIC"</data>
      <data key="d4">"TCP重传率指标，反映网络传输可靠性，100%表示所有包均需重传的极端故障"&lt;SEP&gt;"TCP重传率核心指标，100%值表示完全丢包的极端故障状态"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;重传率100%&quot;">
      <data key="d0">entity</data>
      <data key="d3">"NETWORK_METRIC"</data>
      <data key="d4">"完全故障指标值，表示该时段所有数据包均需重传"&lt;SEP&gt;"所有数据包均需重传的极端网络指标，表示严重传输故障"&lt;SEP&gt;"所有数据包都需要重传的极端网络度量，表示严重丢包问题"&lt;SEP&gt;"所有数据包都需要重传的极端网络度量，表示严重的包丢失问题"&lt;SEP&gt;"示例中观测到的TCP重传率具体值100%，表示完全故障状态"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8&lt;SEP&gt;chunk-********************************&lt;SEP&gt;chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;序列号溢出&quot;">
      <data key="d0">entity</data>
      <data key="d3">"SYMPTOM"</data>
      <data key="d4">"TCP序列号计数器超过最大值后归零引发的识别错误"&lt;SEP&gt;"设备在处理序列号时发生的溢出错误，导致后续包被误判为重传包"&lt;SEP&gt;"设备处理TCP序列号时发生的数值溢出错误，引发后续数据包识别混乱"&lt;SEP&gt;"设备处理TCP序列号时发生的数值越界错误，引发后续包识别混乱的根本原因"&lt;SEP&gt;"设备处理序列号时发生的溢出错误，导致后续包被误认为重传包"</data>
      <data key="d2">chunk-********************************&lt;SEP&gt;chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;10秒&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"高精度时间桶单位，用于异常时刻的最后阶段定位"&lt;SEP&gt;"高精度时间桶单位，用于最终异常点定位"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;长期会话&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"持续时间大于2秒的TCP会话"&lt;SEP&gt;"持续时间超过2秒的TCP会话，采用二分法定位问题"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&quot;跳包&quot;">
      <data key="d0">entity</data>
      <data key="d3">"SYMPTOM"</data>
      <data key="d4">"数据包在传输过程中丢失的网络故障现象"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&quot;SEQ号跳变&quot;">
      <data key="d0">entity</data>
      <data key="d3">"SYMPTOM"</data>
      <data key="d4">"TCP序列号异常跳跃现象，表明数据包突发性丢失"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&quot;正常序列号&quot;">
      <data key="d0">entity</data>
      <data key="d3">"NETWORK_METRIC"</data>
      <data key="d4">"作为比对基准的标准TCP序列号范围"</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;异常大包&quot;">
      <data key="d0">entity</data>
      <data key="d3">"SYMPTOM"</data>
      <data key="d4">"序列号远大于正常值的包，指示序列号跳变"&lt;SEP&gt;"序列号远大于正常值的异常数据包，指示序列号跳变问题"</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;100%&quot;">
      <data key="d0">entity</data>
      <data key="d3">"THRESHOLD"</data>
      <data key="d4">"重传率极端阈值，作为二分查找的终止条件，触发诊断流程转向"&lt;SEP&gt;"重传率达到100%的阈值，表示完全丢包状态，作为二分查找的退出条件之一"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;30分钟&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"TCP会话的总持续时间"&lt;SEP&gt;"TCP会话的总持续时间，用于长期会话诊断"&lt;SEP&gt;"初始诊断时间跨度阈值，用于定义持续性问题检测的范围"&lt;SEP&gt;"总诊断时间范围，作为异常检测的初始时间跨度"</data>
      <data key="d2">chunk-********************************&lt;SEP&gt;chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;1秒&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"最小时间桶单位，精确定位到异常发生的具体时刻"&lt;SEP&gt;"最小时间桶单位，精确定位异常发生的瞬间"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;短期会话&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"持续时间小于等于2秒的TCP会话"&lt;SEP&gt;"持续时间小于等于2秒的TCP会话，需要特殊诊断方法"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&quot;统计算法问题&quot;">
      <data key="d0">entity</data>
      <data key="d3">"ALGORITHM"</data>
      <data key="d4">"指重传率计算逻辑错误导致的误判情况"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&quot;查询统计表工具&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TOOL"</data>
      <data key="d4">"用于查询网络会话统计数据的专用工具"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&quot;默认KEY&quot;">
      <data key="d0">entity</data>
      <data key="d3">"ANALYSIS_TECHNIQUE"</data>
      <data key="d4">"数据库查询使用的默认索引字段"</data>
      <data key="d2">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </node>
    <node id="&quot;重传率10%&quot;">
      <data key="d0">entity</data>
      <data key="d3">"NETWORK_METRIC"</data>
      <data key="d4">"健康网络状态指标值，示例中正常时段的观测数据"&lt;SEP&gt;"示例中观测到的TCP重传率具体值10%，表示网络状况良好"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;时间桶&quot;">
      <data key="d0">entity</data>
      <data key="d3">"ANALYSIS_TECHNIQUE"</data>
      <data key="d4">"动态时间分段技术，通过逐步缩小时间窗口（10分钟→1秒）实现渐进式问题定位"&lt;SEP&gt;"时间分段单位，用于划分诊断时段（如10分钟/1秒），实现渐进式问题定位"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;15:19:45&quot;">
      <data key="d0">entity</data>
      <data key="d3">"TIME_CONCEPT"</data>
      <data key="d4">"通过二分查找最终确定的异常时刻，触发详细包解码"&lt;SEP&gt;"通过二分查找确定的精确异常时刻，作为进一步诊断的起点"</data>
      <data key="d2">chunk-********************************</data>
    </node>
    <node id="&quot;案例1：短期TCP会话序列号跳变&quot;">
      <data key="d0">entity</data>
      <data key="d3">"CASE_STUDY"</data>
      <data key="d4">"一个具体案例，展示短期TCP会话中序列号跳变导致的跳包问题"&lt;SEP&gt;"具体案例展示短期TCP会话中序列号跳变导致的跳包问题"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <node id="&quot;TCP重传率100%告警&quot;">
      <data key="d0">entity</data>
      <data key="d3">"NETWORK_METRIC"</data>
      <data key="d4">"触发告警的极端重传率事件，表明全量数据包丢失"</data>
      <data key="d2">chunk-d82c40f7535b227488145538691ccad8</data>
    </node>
    <edge source="&lt;hyperedge&gt;&quot;TCP会话诊断需检查tcp_flow表获取会话起止时间&quot;" target="&quot;TCP_FLOW表&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;TCP会话诊断需检查tcp_flow表获取会话起止时间&quot;" target="&quot;TIMEUNIT=0&quot;">
      <data key="d5">75.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;TCP会话诊断需检查tcp_flow表获取会话起止时间&quot;" target="&quot;默认KEY&quot;">
      <data key="d5">70.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;如果发现序列号跳变，确认为跳包；否则可能是统计算法问题&quot;" target="&quot;序列号跳变&quot;">
      <data key="d5">270.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;如果发现序列号跳变，确认为跳包；否则可能是统计算法问题&quot;" target="&quot;跳包&quot;">
      <data key="d5">285.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;如果发现序列号跳变，确认为跳包；否则可能是统计算法问题&quot;" target="&quot;统计算法问题&quot;">
      <data key="d5">255.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;发现第1秒最后一个包seq=1000000正常，第2秒开始出现seq=4294967295的异常大包，如果出现则为序列号跳变导致的跳包，否则不是这个问题导致的&quot;" target="&quot;第1秒&quot;">
      <data key="d5">240.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;发现第1秒最后一个包seq=1000000正常，第2秒开始出现seq=4294967295的异常大包，如果出现则为序列号跳变导致的跳包，否则不是这个问题导致的&quot;" target="&quot;序列号跳变&quot;">
      <data key="d5">285.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;发现第1秒最后一个包seq=1000000正常，第2秒开始出现seq=4294967295的异常大包，如果出现则为序列号跳变导致的跳包，否则不是这个问题导致的&quot;" target="&quot;第2秒&quot;">
      <data key="d5">240.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;发现第1秒最后一个包seq=1000000正常，第2秒开始出现seq=4294967295的异常大包，如果出现则为序列号跳变导致的跳包，否则不是这个问题导致的&quot;" target="&quot;异常大包&quot;">
      <data key="d5">255.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;持续30分钟会话中出现连续10分钟100%重传率触发告警&quot;" target="&quot;100%重传率&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;持续30分钟会话中出现连续10分钟100%重传率触发告警&quot;" target="&quot;连续重传&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;持续30分钟会话中出现连续10分钟100%重传率触发告警&quot;" target="&quot;告警触发&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;服务器ip:***********,服务器端口:80,客户端ip:***********,客户端端口:10800,在2025-06-16 14:00:00触发TCP重传率100%告警；会话持续时间仅2秒，第2秒触发告警&quot;" target="&quot;会话持续时间&quot;">
      <data key="d5">270.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;服务器ip:***********,服务器端口:80,客户端ip:***********,客户端端口:10800,在2025-06-16 14:00:00触发TCP重传率100%告警；会话持续时间仅2秒，第2秒触发告警&quot;" target="&quot;2秒&quot;">
      <data key="d5">255.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;服务器ip:***********,服务器端口:80,客户端ip:***********,客户端端口:10800,在2025-06-16 14:00:00触发TCP重传率100%告警；会话持续时间仅2秒，第2秒触发告警&quot;" target="&quot;案例1：短期TCP会话序列号跳变&quot;">
      <data key="d5">160.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;服务器ip:***********,服务器端口:80,客户端ip:***********,客户端端口:10800,在2025-06-16 14:00:00触发TCP重传率100%告警；会话持续时间仅2秒，第2秒触发告警&quot;" target="&quot;TCP重传率100%告警&quot;">
      <data key="d5">285.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;如果存在序列号跳变，则确认为序列号跳变导致的跳包；设备在处理序列号时出现溢出，导致后续包被认为是重传&quot;" target="&quot;序列号跳变&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;如果存在序列号跳变，则确认为序列号跳变导致的跳包；设备在处理序列号时出现溢出，导致后续包被认为是重传&quot;" target="&quot;序列号溢出&quot;">
      <data key="d5">270.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断需确认会话实际持续时间是否匹配告警时段&quot;" target="&quot;会话持续时间&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;查询TCP会话基本信息（tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和会话结束时间）；发现会话在13:59:58-14:00:00期间，持续2秒；使用在线解码分析13:59:59-14:00:00的数据包&quot;" target="&quot;TCP会话基本信息&quot;">
      <data key="d5">255.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;查询TCP会话基本信息（tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和会话结束时间）；发现会话在13:59:58-14:00:00期间，持续2秒；使用在线解码分析13:59:59-14:00:00的数据包&quot;" target="&quot;在线解码&quot;">
      <data key="d5">255.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;查询TCP会话基本信息（tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和会话结束时间）；发现会话在13:59:58-14:00:00期间，持续2秒；使用在线解码分析13:59:59-14:00:00的数据包&quot;" target="&quot;TCP_FLOW表&quot;">
      <data key="d5">240.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;查询TCP会话基本信息（tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和会话结束时间）；发现会话在13:59:58-14:00:00期间，持续2秒；使用在线解码分析13:59:59-14:00:00的数据包&quot;" target="&quot;TIMEUNIT=0&quot;">
      <data key="d5">210.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;SEQ&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;10分钟&quot;">
      <data key="d5">80.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;包解码&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;重传率95%&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;会话开始时间&quot;">
      <data key="d5">80.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;SEQ跳变&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;5分钟&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;会话结束时间&quot;">
      <data key="d5">80.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;序列号跳变导致的跳包&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;1分钟&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;TCP_RETRANSMISSION_RATE&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;重传率100%&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;序列号溢出&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;10秒&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;100%&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;30分钟&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;1秒&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;二分查找&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;重传率10%&quot;">
      <data key="d5">80.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;时间桶&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：Seq跳变导致丢包，根本原因为设备序列号溢出引发后续包重传误判&quot;" target="&quot;15:19:45&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;长期会话连续重传问题现象：服务器IP:***********，服务器端口:80，客户端IP:***********，客户端端口:10800，在2025-06-16 15:30:00触发告警，会话持续30分钟，从15:20开始连续10分钟重传率100%&quot;" target="&quot;TCP会话&quot;">
      <data key="d5">270.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;长期会话连续重传问题现象：服务器IP:***********，服务器端口:80，客户端IP:***********，客户端端口:10800，在2025-06-16 15:30:00触发告警，会话持续30分钟，从15:20开始连续10分钟重传率100%&quot;" target="&quot;10分钟&quot;">
      <data key="d5">240.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;长期会话连续重传问题现象：服务器IP:***********，服务器端口:80，客户端IP:***********，客户端端口:10800，在2025-06-16 15:30:00触发告警，会话持续30分钟，从15:20开始连续10分钟重传率100%&quot;" target="&quot;告警&quot;">
      <data key="d5">255.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;长期会话连续重传问题现象：服务器IP:***********，服务器端口:80，客户端IP:***********，客户端端口:10800，在2025-06-16 15:30:00触发告警，会话持续30分钟，从15:20开始连续10分钟重传率100%&quot;" target="&quot;重传率100%&quot;">
      <data key="d5">285.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;长期会话连续重传问题现象：服务器IP:***********，服务器端口:80，客户端IP:***********，客户端端口:10800，在2025-06-16 15:30:00触发告警，会话持续30分钟，从15:20开始连续10分钟重传率100%&quot;" target="&quot;30分钟&quot;">
      <data key="d5">240.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;示例数据分段：2025-06-16 15:10:00-15:20:00时段重传率95%，需继续以5分钟/1分钟/10秒/1秒桶排查&quot;" target="&quot;重传率95%&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;示例数据分段：2025-06-16 15:10:00-15:20:00时段重传率95%，需继续以5分钟/1分钟/10秒/1秒桶排查&quot;" target="&quot;5分钟&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;示例数据分段：2025-06-16 15:10:00-15:20:00时段重传率95%，需继续以5分钟/1分钟/10秒/1秒桶排查&quot;" target="&quot;1分钟&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;示例数据分段：2025-06-16 15:10:00-15:20:00时段重传率95%，需继续以5分钟/1分钟/10秒/1秒桶排查&quot;" target="&quot;10秒&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;示例数据分段：2025-06-16 15:10:00-15:20:00时段重传率95%，需继续以5分钟/1分钟/10秒/1秒桶排查&quot;" target="&quot;1秒&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，只查询会话开始时间和结束时间，发现会话持续30分钟&quot;" target="&quot;TCP会话基本信息&quot;">
      <data key="d5">170.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，只查询会话开始时间和结束时间，发现会话持续30分钟&quot;" target="&quot;会话开始时间&quot;">
      <data key="d5">150.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，只查询会话开始时间和结束时间，发现会话持续30分钟&quot;" target="&quot;TCP_FLOW表&quot;">
      <data key="d5">160.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，只查询会话开始时间和结束时间，发现会话持续30分钟&quot;" target="&quot;会话结束时间&quot;">
      <data key="d5">150.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;定位15:19:45为非100%重传率时间点后，进行详细包解码并检查Seq跳变&quot;" target="&quot;包解码&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;定位15:19:45为非100%重传率时间点后，进行详细包解码并检查Seq跳变&quot;" target="&quot;SEQ跳变&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;定位15:19:45为非100%重传率时间点后，进行详细包解码并检查Seq跳变&quot;" target="&quot;15:19:45&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;首先确定会话持续时间，区分短期会话（≤2秒）和长期会话（&gt;2秒）；短期会话场景：使用第1秒的在线解码判断seq号跳变；长期会话场景：使用查询统计表工具，二分查找定位第一个出现100%重传前的时间点（秒）&quot;" target="&quot;会话持续时间&quot;">
      <data key="d5">270.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;首先确定会话持续时间，区分短期会话（≤2秒）和长期会话（&gt;2秒）；短期会话场景：使用第1秒的在线解码判断seq号跳变；长期会话场景：使用查询统计表工具，二分查找定位第一个出现100%重传前的时间点（秒）&quot;" target="&quot;2秒&quot;">
      <data key="d5">255.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;首先确定会话持续时间，区分短期会话（≤2秒）和长期会话（&gt;2秒）；短期会话场景：使用第1秒的在线解码判断seq号跳变；长期会话场景：使用查询统计表工具，二分查找定位第一个出现100%重传前的时间点（秒）&quot;" target="&quot;100%重传&quot;">
      <data key="d5">285.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;首先确定会话持续时间，区分短期会话（≤2秒）和长期会话（&gt;2秒）；短期会话场景：使用第1秒的在线解码判断seq号跳变；长期会话场景：使用查询统计表工具，二分查找定位第一个出现100%重传前的时间点（秒）&quot;" target="&quot;在线解码&quot;">
      <data key="d5">255.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;首先确定会话持续时间，区分短期会话（≤2秒）和长期会话（&gt;2秒）；短期会话场景：使用第1秒的在线解码判断seq号跳变；长期会话场景：使用查询统计表工具，二分查找定位第一个出现100%重传前的时间点（秒）&quot;" target="&quot;时间点（秒）&quot;">
      <data key="d5">240.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;首先确定会话持续时间，区分短期会话（≤2秒）和长期会话（&gt;2秒）；短期会话场景：使用第1秒的在线解码判断seq号跳变；长期会话场景：使用查询统计表工具，二分查找定位第一个出现100%重传前的时间点（秒）&quot;" target="&quot;SEQ号跳变&quot;">
      <data key="d5">270.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;首先确定会话持续时间，区分短期会话（≤2秒）和长期会话（&gt;2秒）；短期会话场景：使用第1秒的在线解码判断seq号跳变；长期会话场景：使用查询统计表工具，二分查找定位第一个出现100%重传前的时间点（秒）&quot;" target="&quot;长期会话&quot;">
      <data key="d5">170.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;首先确定会话持续时间，区分短期会话（≤2秒）和长期会话（&gt;2秒）；短期会话场景：使用第1秒的在线解码判断seq号跳变；长期会话场景：使用查询统计表工具，二分查找定位第一个出现100%重传前的时间点（秒）&quot;" target="&quot;查询统计表工具&quot;">
      <data key="d5">240.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;首先确定会话持续时间，区分短期会话（≤2秒）和长期会话（&gt;2秒）；短期会话场景：使用第1秒的在线解码判断seq号跳变；长期会话场景：使用查询统计表工具，二分查找定位第一个出现100%重传前的时间点（秒）&quot;" target="&quot;短期会话&quot;">
      <data key="d5">170.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;首先确定会话持续时间，区分短期会话（≤2秒）和长期会话（&gt;2秒）；短期会话场景：使用第1秒的在线解码判断seq号跳变；长期会话场景：使用查询统计表工具，二分查找定位第一个出现100%重传前的时间点（秒）&quot;" target="&quot;二分查找&quot;">
      <data key="d5">255.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和结束时间，发现会话持续30分钟&quot;" target="&quot;TCP会话基本信息&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和结束时间，发现会话持续30分钟&quot;" target="&quot;会话开始时间&quot;">
      <data key="d5">75.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和结束时间，发现会话持续30分钟&quot;" target="&quot;TCP_FLOW表&quot;">
      <data key="d5">80.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和结束时间，发现会话持续30分钟&quot;" target="&quot;SEQ=1000000&quot;">
      <data key="d5">80.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和结束时间，发现会话持续30分钟&quot;" target="&quot;会话结束时间&quot;">
      <data key="d5">75.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和结束时间，发现会话持续30分钟&quot;" target="&quot;序列号跳变&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和结束时间，发现会话持续30分钟&quot;" target="&quot;SEQ=4294967295&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;排查步骤：查询TCP会话基本信息，使用tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和结束时间，发现会话持续30分钟&quot;" target="&quot;正常序列号&quot;">
      <data key="d5">75.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;若存在Seq跳变则确认为序列号跳变导致的跳包，原因为设备处理序列号时溢出&quot;" target="&quot;序列号跳变导致的跳包&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;若存在Seq跳变则确认为序列号跳变导致的跳包，原因为设备处理序列号时溢出&quot;" target="&quot;序列号溢出&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&lt;knowledge_segment&gt;" target="&lt;ENTITY_NAME&gt;">
      <data key="d5">50.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;设备序列号溢出导致后续包被误判为重传包&quot;" target="&quot;序列号溢出&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-f70ac9a747055e50ed2781430908f6a1</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;10分钟&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;包解码&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;重传率95%&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;会话开始时间&quot;">
      <data key="d5">80.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;SEQ跳变&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;5分钟&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;会话结束时间&quot;">
      <data key="d5">80.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;序列号跳变导致的跳包&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;1分钟&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;TCP_RETRANSMISSION_RATE&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;重传率100%&quot;">
      <data key="d5">95.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;序列号溢出&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;10秒&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;100%&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;30分钟&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;1秒&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;重传率10%&quot;">
      <data key="d5">80.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;时间桶&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;诊断结论：若存在Seq跳变则确认为序列号跳变引起的丢包，根本原因为设备序列号溢出导致后续包被误判为重传&quot;" target="&quot;15:19:45&quot;">
      <data key="d5">90.0</data>
      <data key="d6">chunk-********************************</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;长时间出现重传率100%，下载数据包后分析是正常的&quot;" target="&quot;TCP会话基本信息&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;长时间出现重传率100%，下载数据包后分析是正常的&quot;" target="&quot;TCP_FLOW表&quot;">
      <data key="d5">80.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;长时间出现重传率100%，下载数据包后分析是正常的&quot;" target="&quot;下载数据包后分析&quot;">
      <data key="d5">320.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;长时间出现重传率100%，下载数据包后分析是正常的&quot;" target="&quot;重传率100%&quot;">
      <data key="d5">380.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;长时间出现重传率100%，下载数据包后分析是正常的&quot;" target="&quot;长期会话&quot;">
      <data key="d5">170.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;长时间出现重传率100%，下载数据包后分析是正常的&quot;" target="&quot;短期会话&quot;">
      <data key="d5">85.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
    <edge source="&lt;hyperedge&gt;&quot;长时间出现重传率100%，下载数据包后分析是正常的&quot;" target="&quot;案例1：短期TCP会话序列号跳变&quot;">
      <data key="d5">80.0</data>
      <data key="d6">chunk-d82c40f7535b227488145538691ccad8</data>
    </edge>
  </graph>
</graphml>
