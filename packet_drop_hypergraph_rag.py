#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跳包诊断专用HyperGraphRAG系统
基于HyperGraphRAG构建的跳包诊断知识图谱
"""

import os
import sys
import asyncio
import json
from typing import List, Dict, Any
from dataclasses import dataclass, field

import numpy as np

from HyperGraphRAG.hypergraphrag.utils import (
    safe_unicode_decode, wrap_embedding_func_with_attrs, logger, set_logger)

# 添加HyperGraphRAG路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'HyperGraphRAG'))

from HyperGraphRAG.hypergraphrag.hypergraphrag import HyperGraphRAG, QueryParam
from HyperGraphRAG.hypergraphrag.llm import gpt_4o_mini_complete, openai_embedding
from packet_drop_knowledge_corpus import get_chunked_knowledge
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
from openai import AsyncOpenAI, RateLimitError, APIConnectionError, Timeout
from dotenv import load_dotenv
load_dotenv("./.env_hyper")

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    retry=retry_if_exception_type((RateLimitError, APIConnectionError, Timeout)),
)
async def openai_complete_if_cache(
    prompt,
    model="deepseek-ai/DeepSeek-R1-0528",
    system_prompt=None,
    history_messages=[],
    base_url="http://yy.colasoft.cn:3000/v1/",
    api_key=None,
    **kwargs,
) -> str:
    if api_key:
        os.environ["OPENAI_API_KEY"] = api_key

    openai_async_client = (
        AsyncOpenAI() if base_url is None else AsyncOpenAI(base_url=base_url)
    )
    kwargs.pop("hashing_kv", None)
    kwargs.pop("keyword_extraction", None)
    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    messages.extend(history_messages)
    messages.append({"role": "user", "content": prompt})

    # 添加日志输出
    logger.debug("===== Query Input to LLM =====")
    logger.debug(f"Query: {prompt}")
    logger.debug(f"System prompt: {system_prompt}")
    logger.debug("Full context:")
    if "response_format" in kwargs:
        response = await openai_async_client.beta.chat.completions.parse(
            model=model, messages=messages, **kwargs
        )
    else:
        response = await openai_async_client.chat.completions.create(
            model=model, messages=messages, **kwargs
        )

    if hasattr(response, "__aiter__"):

        async def inner():
            async for chunk in response:
                content = chunk.choices[0].delta.content
                if content is None:
                    continue
                if r"\u" in content:
                    content = safe_unicode_decode(content.encode("utf-8"))
                yield content

        return inner()
    else:
        content = response.choices[0].message.content
        if r"\u" in content:
            content = safe_unicode_decode(content.encode("utf-8"))
        return content


@wrap_embedding_func_with_attrs(embedding_dim=2048, max_token_size=8192)
@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=60),
    retry=retry_if_exception_type((RateLimitError, APIConnectionError, Timeout)),
)
async def zhipu_embedding(
    texts: list[str], model: str = "embedding-3", api_key: str = None, **kwargs
) -> np.ndarray:
    # dynamically load ZhipuAI
    try:
        from zhipuai import ZhipuAI
    except ImportError:
        raise ImportError("Please install zhipuai before initialize zhipuai backend.")
    if api_key:
        client = ZhipuAI(api_key=api_key)
    else:
        # please set ZHIPUAI_API_KEY in your environment
        # os.environ["ZHIPUAI_API_KEY"]
        client = ZhipuAI()

    # Convert single text to list if needed
    if isinstance(texts, str):
        texts = [texts]

    embeddings = []
    for text in texts:
        try:
            response = client.embeddings.create(model=model, input=[text], **kwargs)
            embeddings.append(response.data[0].embedding)
        except Exception as e:
            raise Exception(f"Error calling ChatGLM Embedding API: {str(e)}")

    return np.array(embeddings)
class PacketDropHyperGraphRAG:
    """跳包诊断专用HyperGraphRAG系统"""
    
    def __init__(self, working_dir: str = "packet_drop_rag_cache"):
        """
        初始化跳包诊断HyperGraphRAG系统
        
        Args:
            working_dir: 工作目录
        """
        self.working_dir = working_dir
        self.hypergraph_rag = None
        self._initialize_hypergraph_rag()
    
    def _initialize_hypergraph_rag(self):
        """初始化HyperGraphRAG实例"""
        
        # 定制的跳包诊断提示词
        custom_prompts = self._get_packet_drop_prompts()
        
        # 配置HyperGraphRAG
        self.hypergraph_rag = HyperGraphRAG(
            working_dir=self.working_dir,
            chunk_token_size=800,  # 适合跳包诊断文档的分块大小
            chunk_overlap_token_size=100,
            entity_extract_max_gleaning=2,  # 提取更多实体
            embedding_func=zhipu_embedding,
            llm_model_func=openai_complete_if_cache,
            llm_model_name="deepseek-ai/DeepSeek-R1-0528",
            llm_model_max_token_size=131072,
            llm_model_max_async=10,
            enable_llm_cache=True,
            log_level="INFO"
        )
        
        # 更新提示词
        self._update_extraction_prompts()
    
    def _get_packet_drop_prompts(self):
        """获取跳包诊断专用的提示词"""
        return {
            "entity_types": [
                "diagnostic_method", "network_metric", "tool", "symptom", 
                "protocol", "algorithm", "time_concept", "threshold", 
                "analysis_technique", "case_study"
            ],
            
            "entity_extraction_context": """
            你正在分析网络跳包诊断的技术文档。请特别关注以下类型的实体：

            - diagnostic_method: 诊断方法（如短期会话诊断、长期会话诊断、二分查找等）
            - network_metric: 网络指标（如重传率、序列号、时延等）
            - tool: 诊断工具（如统计查询、在线解码、MCP工具等）
            - symptom: 异常症状（如跳包、序列号跳变、连接超时等）
            - protocol: 网络协议（如TCP、UDP、IP等）
            - algorithm: 算法技术（如二分查找、时间桶分析等）
            - time_concept: 时间概念（如短期会话、长期会话、时间桶等）
            - threshold: 阈值参数（如90%、100%、2秒等）
            - analysis_technique: 分析技术（如时间序列分析、模式识别等）
            - case_study: 案例研究（具体的诊断案例）

            请识别这些实体之间的诊断关系、应用关系、依赖关系等。
            """
        }
    
    def _update_extraction_prompts(self):
        """更新实体提取提示词"""
        from HyperGraphRAG.hypergraphrag.prompt import PROMPTS
        
        # 更新实体类型
        PROMPTS["DEFAULT_ENTITY_TYPES"] = [
            "diagnostic_method", "network_metric", "tool", "symptom", 
            "protocol", "algorithm", "time_concept", "threshold", 
            "analysis_technique", "case_study"
        ]
        
        # 定制的实体提取提示词
        PROMPTS["entity_extraction"] = """-Goal-
Given a network packet drop diagnostic document, identify all entities related to network diagnostics and all relationships among the identified entities.
Use Chinese as output language for technical terms, English for entity names.

-Steps-
1. Divide the text into several complete diagnostic knowledge segments. For each knowledge segment, extract the following information:
-- knowledge_segment: A sentence that describes the diagnostic context or procedure.
-- completeness_score: A score from 0 to 10 indicating the completeness of the diagnostic knowledge.
Format each knowledge segment as ("hyper-relation"{tuple_delimiter}<knowledge_segment>{tuple_delimiter}<completeness_score>)

2. Identify all diagnostic entities in each knowledge segment. For each identified entity, extract the following information:
- entity_name: Name of the entity, keep original technical terms.
- entity_type: Type from [diagnostic_method, network_metric, tool, symptom, protocol, algorithm, time_concept, threshold, analysis_technique, case_study].
- entity_description: Comprehensive description of the entity's role in packet drop diagnosis.
- key_score: A score from 0 to 100 indicating the importance of the entity in packet drop diagnosis.
Format each entity as ("entity"{tuple_delimiter}<entity_name>{tuple_delimiter}<entity_type>{tuple_delimiter}<entity_description>{tuple_delimiter}<key_score>)

3. Return output in Chinese as a single list of all the entities and relationships identified in steps 1 and 2. Use **{record_delimiter}** as the list delimiter.

4. When finished, output {completion_delimiter}

######################
-Examples-
######################
{examples}

#############################
-Real Data-
######################
Text: {input_text}
######################
Output:
"""
        
        # 添加跳包诊断专用示例
        packet_drop_example = """Example:

Text:
短期会话跳包诊断方法：当TCP会话持续时间较短（通常少于2秒）且出现100%重传率时，需要特殊的诊断方法。短期会话中的跳包诊断重点关注序列号跳变。如果在第1秒出现异常大的序列号，而第2秒触发重传率100%的警报，这通常表明存在序列号跳变导致的跳包。诊断流程：1）检查会话持续时间，2）查看第1秒的数据包，3）使用在线解码分析序列号是否异常跳变。
################
Output:
("hyper-relation"{tuple_delimiter}"短期会话跳包诊断需要特殊方法，重点关注序列号跳变检测"{tuple_delimiter}9){record_delimiter}
("entity"{tuple_delimiter}"短期会话跳包诊断"{tuple_delimiter}"diagnostic_method"{tuple_delimiter}"针对持续时间短于2秒的TCP会话的跳包诊断方法，重点分析序列号跳变"{tuple_delimiter}95){record_delimiter}
("entity"{tuple_delimiter}"TCP会话"{tuple_delimiter}"protocol"{tuple_delimiter}"传输控制协议会话，用于可靠的数据传输"{tuple_delimiter}90){record_delimiter}
("entity"{tuple_delimiter}"2秒"{tuple_delimiter}"time_concept"{tuple_delimiter}"短期会话的时间阈值，用于区分短期和长期会话"{tuple_delimiter}85){record_delimiter}
("entity"{tuple_delimiter}"100%重传率"{tuple_delimiter}"network_metric"{tuple_delimiter}"所有数据包都需要重传的极端情况，表示严重的网络问题"{tuple_delimiter}95){record_delimiter}
("entity"{tuple_delimiter}"序列号跳变"{tuple_delimiter}"symptom"{tuple_delimiter}"TCP序列号出现异常的大幅跳跃，通常导致跳包"{tuple_delimiter}90){record_delimiter}
("entity"{tuple_delimiter}"在线解码"{tuple_delimiter}"tool"{tuple_delimiter}"实时分析数据包内容的技术，用于详细检查序列号等信息"{tuple_delimiter}85){record_delimiter}
("hyper-relation"{tuple_delimiter}"跳包诊断流程包括检查会话时长、查看数据包、进行在线解码分析"{tuple_delimiter}8){record_delimiter}
#############################"""
        
        PROMPTS["entity_extraction_examples"].insert(0, packet_drop_example)
    
    async def insert_packet_drop_knowledge(self):
        """插入跳包诊断知识到HyperGraphRAG"""
        print("正在插入跳包诊断知识到HyperGraphRAG...")
        
        # 获取分块后的知识语料
        knowledge_chunks = get_chunked_knowledge(chunk_size=600)
        
        print(f"准备插入 {len(knowledge_chunks)} 个知识片段")
        
        # 批量插入知识
        await self.hypergraph_rag.ainsert(knowledge_chunks)
        
        print("跳包诊断知识插入完成")
    
    async def query_packet_drop_knowledge(self, query: str, mode: str = "hybrid") -> str:
        """
        查询跳包诊断知识
        
        Args:
            query: 查询问题
            mode: 查询模式 (local/global/hybrid)
        
        Returns:
            查询结果
        """
        query_param = QueryParam(
            mode=mode,
            top_k=20,  # 增加召回数量
            max_token_for_global_context=3000,
            max_token_for_local_context=3000,
            response_type="详细的技术分析报告"
        )
        
        result = await self.hypergraph_rag.aquery(query, query_param)
        return result
    
    async def get_diagnostic_mcp_chain(self, alert_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        基于警报信息生成MCP调用链
        
        Args:
            alert_info: 警报信息
        
        Returns:
            MCP调用链
        """
        # 构建诊断查询
        query = f"""
        {json.dumps(alert_info)}
        请提供完整的诊断流程和所需的MCP工具调用序列。
        
        一个可能的示例格式为：
        {{
          "meta": {{
            "session_type": "long_session", 
            "analysis_focus": "TCP连接异常分析",
            "key_filtering_strategy": "使用关键字段进行精确过滤",
            "total_steps": 5
          }},
          "steps": [
            {{
              "step_id": 1,
              "tool": "setup_api_connection",
              "description": "建立API连接",
              "parameters": {{
                "machine_ip": "目标机器IP",
                "netlink_id": "网络链路ID"
              }},
              "expected_outcome": "连接建立成功",
              "conditional_next": "step_2"
            }},
            {{
              "step_id": 2,
              "tool": "query_statistics_table",
              "description": "查询会话基础信息",
              "parameters": {{
                "table_name": "tcp_flow",
                "fields": ["flow_start_time", "flow_end_time", "flow_duration"],
                "filters": "client_ip_addr=具体IP&&server_ip_addr=具体IP",
                "limit": 10
              }},
              "expected_outcome": "获取会话时长，判断短期/长期会话",
              "conditional_next": {{
                "condition": "duration <= 2",
                "true": "step_3_short",
                "false": "step_3_long"
              }}
            }},
            {{
              "step_id": "step_3_timeout",
              "tool": "query_statistics_table",
              "description": "连接超时分析",
              "parameters": {{
                "table_name": "tcp_flow",
                "fields": ["tcp_retransmission_rate"],
                "filters": "##执行当这一步获取##",
                "time_range": "##执行当这一步获取##"
              }},
              "expected_outcome": "定位超时原因",
              "conditional_next": "step_4"
            }},
            {{
              "step_id": "step_3_loss",
              "tool": "query_statistics_table", 
              "description": "丢包分析",
              "parameters": {{
                "table_name": "相关表",
                "fields": ["丢包相关字段"],
                "filters": "丢包条件过滤"
              }},
              "expected_outcome": "分析丢包模式",
              "conditional_next": "step_4"
            }},
            {{
              "step_id": 4,
              "tool": "get_detailed_packet_decode",
              "description": "详细包解析",
              "parameters": {{
                "packet_filters": "包过滤条件",
                "decode_level": "解码层级",
                "time_range": "##执行当这一步获取##"
              }},
              "expected_outcome": "获取详细信息",
              "conditional_next": "end"
            }}
          ],
          "branches": {{
            "timeout_flow": [
              1,
              2,
              "step_3_timeout",
              4
            ],
            "packet_loss_flow": [
              1,
              2, 
              "step_3_loss",
              4
            ]
          }}
        }}
        
        请根据具体的警报信息生成对应的诊断流程。
        """
        
        # 查询HyperGraphRAG获取诊断知识
        diagnostic_guidance = await self.query_packet_drop_knowledge(query, mode="hybrid")
        
        # 解析诊断指导，生成MCP调用链
        mcp_chain = await self._parse_diagnostic_to_mcp_chain(diagnostic_guidance, alert_info)
        
        return mcp_chain
    
    async def _parse_diagnostic_to_mcp_chain(self, diagnostic_guidance: str, alert_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        通过LLM智能生成MCP调用链，完全去除硬编码
        
        Args:
            diagnostic_guidance: HyperGraphRAG返回的诊断指导
            alert_info: 警报信息
            
        Returns:
            LLM生成的完整MCP调用链字典
        """
        from datetime import datetime
        
        # 获取可用的MCP工具描述
        available_tools = self._get_available_mcp_tools()
        
        # 构建LLM提示词
        llm_prompt = self._build_mcp_generation_prompt(
            diagnostic_guidance, alert_info, available_tools
        )
        
        # 使用LLM生成调用链
        try:
            llm_response = await self._generate_mcp_chain_via_llm(llm_prompt, alert_info)
            return llm_response
            
        except Exception as e:
            print(f"LLM生成调用链失败: {e}")
            # 使用最简化的备用方案
            return self._get_minimal_fallback_chain(alert_info)
    
    def _get_available_mcp_tools(self) -> Dict[str, Any]:
        """获取可用的MCP工具及其描述信息"""
        return {
            "setup_api_connection": {
                "description": "建立与科来设备的API连接",
                "parameters": {
                    "machine_ip": "机器IP地址，用于API连接登录",
                    "netlink_id": "网络链路ID"
                },
                "usage": "必须首先调用此工具建立连接，为后续查询做准备"
            },
            "query_statistics_table": {
                "description": "查询科来统计表数据，支持精确的key过滤和条件筛选",
                "parameters": {
                    "table_name": "统计表名称",
                    "fields": "要查询的字段列表",
                    "filters": "过滤条件，支持精确的key字段过滤",
                    "limit": "返回记录数限制"
                },
                "key_based_filtering": {
                    "tcp_flow": {
                        "keys": ["client_ip_addr", "server_ip_addr", "client_port", "server_port"],
                        "description": "TCP会话表使用4元组作为key，可精确指定特定连接",
                        "filter_examples": [
                            "client_ip_addr=*************&&server_ip_addr=********",
                            "client_ip_addr=*************&&server_ip_addr=********&&server_port=80",
                            "client_ip_addr=*************&&server_ip_addr=********&&client_port=12345&&server_port=80"
                        ],
                        "important_fields": [
                            "flow_start_time", "flow_end_time", 
                            "tcp_retransmission_rate", "client_tcp_retransmission_packet", 
                            "server_tcp_retransmission_packet"
                        ]
                    },
                    "tcp_server_port": {
                        "keys": ["server_ip_addr", "server_port", "application_id", "protocol"],
                        "description": "TCP服务端口表，按服务器IP、端口分组",
                        "filter_examples": [
                            "server_ip_addr=********&&server_port=80",
                            "server_ip_addr=********&&protocol=6"
                        ]
                    },
                    "ip_flow": {
                        "keys": ["ip_endpoint1", "ip_endpoint2"],
                        "description": "IP流表，按两个IP端点分组",
                        "filter_examples": [
                            "ip_endpoint1=*************&&ip_endpoint2=********"
                        ]
                    },
                    "internal_ip_addr": {
                        "keys": ["ip_addr"],
                        "description": "内网IP地址表，按IP地址分组",
                        "filter_examples": [
                            "ip_addr=*************"
                        ]
                    },
                    "external_ip_addr": {
                        "keys": ["ip_addr"],
                        "description": "外网IP地址表，按IP地址分组",
                        "filter_examples": [
                            "ip_addr=*******"
                        ]
                    }
                },
                "filter_syntax": {
                    "operators": ["=", "!=", ">=", "<=", ">", "<"],
                    "logical": ["&&", "||"],
                    "best_practices": [
                        "尽可能使用key字段进行过滤，提高查询效率",
                        "对于TCP会话，优先使用4元组(client_ip_addr, server_ip_addr, client_port, server_port)过滤",
                        "使用&&连接多个条件进行精确匹配",
                        "只在filter中使用实际存在的统计表字段",
                        "不要在filter中使用alert_time等非表字段"
                    ]
                },
                "usage": "查询网络统计数据，分析流量、连接状态、性能指标等"
            },
            "get_detailed_packet_decode": {
                "description": "获取指定数据包的详细解码信息",
                "parameters": {
                    "packet_filters": "数据包过滤条件",
                    "decode_level": "解码层级",
                    "time_range": "时间范围"
                },
                "usage": "深入分析特定数据包的协议层信息，用于故障定位"
            }
        }
    
    def _build_mcp_generation_prompt(self, diagnostic_guidance: str, alert_info: Dict[str, Any], 
                                   available_tools: Dict[str, Any]) -> str:
        """构建用于LLM生成MCP链的提示词"""
        
        # 从alert_info中提取关键信息
        machine_ip = alert_info.get('machine_ip', '*************')
        netlink_id = alert_info.get('netlink_id', alert_info.get('link_id', ''))
        server_ip_addr = alert_info.get('server_ip_addr')
        client_ip_addr = alert_info.get('client_ip_addr')
        server_port = alert_info.get('server_port', '')
        client_port = alert_info.get('client_port', '')
        protocol = alert_info.get('protocol', 'TCP')
        duration = alert_info.get('duration', 0)
        
        # 构建4元组过滤条件示例
        tcp_4tuple_filter = ""
        if client_ip_addr and server_ip_addr:
            tcp_4tuple_filter = f"client_ip_addr={client_ip_addr}&&server_ip_addr={server_ip_addr}"
            if client_port:
                tcp_4tuple_filter += f"&&client_port={client_port}"
            if server_port:
                tcp_4tuple_filter += f"&&server_port={server_port}"
        
        prompt = f"""你是一个网络诊断专家，需要根据诊断指导和告警信息生成结构化的MCP工具调用链。

# 告警信息
- 机器IP (用于API连接): {machine_ip}
- 网络链路ID: {netlink_id}
- 服务器IP地址: {server_ip_addr}
- 客户端IP地址: {client_ip_addr}
- 服务器端口: {server_port}
- 客户端端口: {client_port}
- 协议: {protocol}
- 会话持续时间: {duration}秒

# 诊断指导
{diagnostic_guidance}

# 可用工具详情
{json.dumps(available_tools, indent=2, ensure_ascii=False)}

# 关键要求：精确的key字段过滤

## 1. TCP会话4元组过滤 (最重要)
对于TCP相关查询，必须尽可能使用4元组进行精确过滤：
- table: "tcp_flow"
- keys: ["client_ip_addr", "server_ip_addr", "client_port", "server_port"]
- 示例过滤条件: "{tcp_4tuple_filter}"

## 2. 其他表的key过滤
- tcp_server_port: server_ip_addr={server_ip_addr}&&server_port={server_port}
- ip_flow: ip_endpoint1={client_ip_addr}&&ip_endpoint2={server_ip_addr}
- internal_ip_addr: ip_addr={client_ip_addr}
- external_ip_addr: ip_addr={server_ip_addr}

## 3. 重要字段名规范
- TCP会话时间字段：flow_start_time, flow_end_time（不是session_start_time）
- TCP重传率字段：tcp_retransmission_rate
- TCP重传包数字段：client_tcp_retransmission_packet, server_tcp_retransmission_packet

## 4. 过滤条件构建规则
- 使用 = 进行精确匹配
- 只使用实际存在的统计表字段
- 不要使用alert_time等非表字段
- 不要在parameters中添加不存在的参数如time_buckets

## 5. 查询优化策略
- 优先查询最相关的表（如tcp_flow用于TCP会话分析）
- 使用key字段进行精确过滤，避免全表扫描
- 根据问题类型选择合适的字段进行查询
- 对于长期会话分析：逐步缩小时间范围而不是使用不存在的time_buckets参数

## 6. 会话类型分析策略
- 短期会话（≤2秒）：直接分析流开始和结束时的数据包序列号
- 长期会话（>2秒）：分段查询不同时间点的重传率，逐步定位异常时间段

# 输出要求
生成一个结构化的JSON，包含：

1. **meta**: 诊断元信息
   - session_type: "short_session" (≤2秒) 或 "long_session" (>2秒)
   - analysis_focus: 分析重点
   - key_filtering_strategy: key字段过滤策略

2. **steps**: 工具调用步骤数组，每个步骤包含：
   - step_id: 步骤编号
   - tool: 工具名称
   - description: 步骤描述  
   - parameters: 工具参数（使用精确的key过滤，不包含不存在的字段）
   - expected_outcome: 预期结果
   - conditional_next: 条件分支

3. **branches**: 分支逻辑（基于会话类型和查询结果）

4. **动态参数提示**: 对于无法在生成时确定的参数值，请使用"##执行当这一步获取##"格式，表示该参数需要从前面步骤的执行结果中动态获取。

请确保所有查询都使用了适当的key字段过滤，特别是TCP会话的4元组过滤。
请确保不包含任何不存在的参数如time_buckets。

请生成诊断MCP工具调用链："""

        return prompt
    
    async def _generate_mcp_chain_via_llm(self, prompt: str, alert_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用LLM生成MCP调用链
        
        Args:
            prompt: LLM提示词
            alert_info: 警报信息
            
        Returns:
            LLM生成的调用链
        """
        try:
            # 调用LLM生成调用链
            llm_response = await openai_complete_if_cache(
                prompt=prompt,
                system_prompt="你是一个专业的网络诊断专家，能够生成准确的MCP工具调用链。请确保输出的JSON格式完全正确。",
                model="deepseek-ai/DeepSeek-R1-0528",
                temperature=0.1,  # 低温度确保输出稳定
                response_format={"type": "json_object"}
            )
            
            # 解析LLM响应
            import json
            from datetime import datetime
            
            if isinstance(llm_response, str):
                try:
                    mcp_chain = json.loads(llm_response)
                except json.JSONDecodeError as e:
                    print(f"LLM返回的JSON格式错误: {e}")
                    print(f"原始响应: {llm_response[:500]}...")
                    return self._get_minimal_fallback_chain(alert_info)
            else:
                mcp_chain = llm_response
            
            print(mcp_chain)
            # 验证和补全必要字段
            mcp_chain = self._validate_and_enhance_llm_chain(mcp_chain, alert_info)
            
            return mcp_chain
            
        except Exception as e:
            print(f"LLM调用失败: {e}")
            return self._get_minimal_fallback_chain(alert_info)
    
    def _validate_and_enhance_llm_chain(self, mcp_chain: Dict[str, Any], 
                                      alert_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证和增强LLM生成的MCP调用链，特别是key字段过滤
        
        Args:
            mcp_chain: LLM生成的MCP调用链
            alert_info: 告警信息
            
        Returns:
            验证并增强后的MCP调用链
        """
        from datetime import datetime
        
        try:
            # 基础结构验证
            if not isinstance(mcp_chain, dict):
                raise ValueError("MCP链必须是字典格式")
            
            if 'steps' not in mcp_chain:
                raise ValueError("MCP链必须包含steps字段")
            
            # 提取关键信息用于验证
            server_ip_addr = alert_info.get('server_ip_addr')
            client_ip_addr = alert_info.get('client_ip_addr')
            server_port = alert_info.get('server_port', '')
            client_port = alert_info.get('client_port', '')
            netlink_id = alert_info.get('netlink_id', alert_info.get('link_id', ''))
            
            # 验证和修复每个步骤
            for step in mcp_chain.get('steps', []):
                # 修复setup_api_connection步骤
                if step.get('tool') == 'setup_api_connection':
                    params = step.get('parameters', {})
                    # 移除username和password参数
                    if 'username' in params:
                        del params['username']
                    if 'password' in params:
                        del params['password']
                    # 确保使用netlink_id而不是link_id
                    if 'link_id' in params:
                        params['netlink_id'] = params.pop('link_id')
                    if netlink_id and 'netlink_id' not in params:
                        params['netlink_id'] = netlink_id
                
                # 修复query_statistics_table步骤
                elif step.get('tool') == 'query_statistics_table':
                    table_name = step.get('parameters', {}).get('table_name', '')
                    filters = step.get('parameters', {}).get('filters', '')
                    params = step.get('parameters', {})
                    
                    # 移除不存在的参数
                    if 'time_buckets' in params:
                        del params['time_buckets']
                    if 'target_rate' in params:
                        del params['target_rate']
                    if 'sort_field' in params:
                        del params['sort_field']
                    
                    # 验证TCP流表的4元组过滤
                    if table_name == 'tcp_flow':
                        if not filters and client_ip_addr and server_ip_addr:
                            # 自动生成4元组过滤条件
                            tcp_filter = f"client_ip_addr={client_ip_addr}&&server_ip_addr={server_ip_addr}"
                            if client_port:
                                tcp_filter += f"&&client_port={client_port}"
                            if server_port:
                                tcp_filter += f"&&server_port={server_port}"
                            step['parameters']['filters'] = tcp_filter
                            print(f"自动添加TCP 4元组过滤: {tcp_filter}")
                        
                        # 移除错误的时间过滤条件
                        if filters and 'alert_time' in filters:
                            step['parameters']['filters'] = filters.replace('alert_time', 'flow_start_time')
                        if filters and 'time_range' in filters:
                            # 移除time_range过滤，因为这不是标准的过滤字段
                            parts = filters.split('&&')
                            clean_parts = [p for p in parts if 'time_range' not in p]
                            step['parameters']['filters'] = '&&'.join(clean_parts)
                    
                    # 验证服务器端口表的过滤
                    elif table_name == 'tcp_server_port':
                        if not filters and server_ip_addr:
                            server_filter = f"server_ip_addr={server_ip_addr}"
                            if server_port:
                                server_filter += f"&&server_port={server_port}"
                            step['parameters']['filters'] = server_filter
                            print(f"自动添加服务器端口过滤: {server_filter}")
                    
                    # 验证IP流表的过滤
                    elif table_name == 'ip_flow':
                        if not filters and client_ip_addr and server_ip_addr:
                            ip_filter = f"ip_endpoint1={client_ip_addr}&&ip_endpoint2={server_ip_addr}"
                            step['parameters']['filters'] = ip_filter
                            print(f"自动添加IP流过滤: {ip_filter}")
                    
                    # 确保关键字段包含在查询中（只使用实际存在的字段）
                    if 'fields' not in step.get('parameters', {}):
                        if table_name == 'tcp_flow':
                            step['parameters']['fields'] = [
                                "client_ip_addr", "server_ip_addr", "client_port", "server_port",
                                "flow_start_time", "flow_end_time", "tcp_seq", "tcp_ack",
                                "total_byte", "total_packet", "flow_duration", "tcp_status",
                                "client_tcp_retransmission_packet", "server_tcp_retransmission_packet"
                            ]
                        elif table_name == 'tcp_server_port':
                            step['parameters']['fields'] = [
                                "server_ip_addr", "server_port", "total_byte", "total_packet",
                                "visit_count", "tcp_syn_packet", "tcp_rst_packet"
                            ]
            
            # 确保meta信息包含过滤策略和total_steps
            if 'meta' not in mcp_chain:
                mcp_chain['meta'] = {}
            
            # 添加total_steps字段
            mcp_chain['meta']['total_steps'] = len(mcp_chain.get('steps', []))
            mcp_chain['meta']['key_filtering_enhanced'] = True
            mcp_chain['meta']['validation_time'] = datetime.now().isoformat()
            
            # 添加过滤策略说明
            if 'key_filtering_strategy' not in mcp_chain['meta']:
                mcp_chain['meta']['key_filtering_strategy'] = "使用TCP会话4元组(client_ip_addr, server_ip_addr, client_port, server_port)进行精确过滤"
            
            return mcp_chain
            
        except Exception as e:
            print(f"验证MCP链时出错: {e}")
            # 返回基础的备用链
            return self._get_minimal_fallback_chain(alert_info)
    
    def _get_minimal_fallback_chain(self, alert_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取最简化的备用调用链
        
        Args:
            alert_info: 警报信息
            
        Returns:
            最简调用链
        """
        from datetime import datetime
        
        machine_ip = alert_info.get('machine_ip', '*************')
        netlink_id = alert_info.get('netlink_id', alert_info.get('link_id', ''))
        server_ip_addr = alert_info.get('server_ip_addr')
        client_ip_addr = alert_info.get('client_ip_addr')
        server_port = alert_info.get('server_port', '')
        client_port = alert_info.get('client_port', '')
        
        # 构建TCP 4元组过滤条件
        tcp_filter = ""
        if client_ip_addr and server_ip_addr:
            tcp_filter = f"client_ip_addr={client_ip_addr}&&server_ip_addr={server_ip_addr}"
            if client_port:
                tcp_filter += f"&&client_port={client_port}"
            if server_port:
                tcp_filter += f"&&server_port={server_port}"
        
        fallback_chain = {
            "meta": {
                "session_type": "unknown",
                "analysis_focus": "基础跳包诊断",
                "key_filtering_strategy": "使用TCP会话4元组进行精确过滤",
                "total_steps": 3,
                "fallback_mode": True,
                "generation_time": datetime.now().isoformat()
            },
            "steps": [
                {
                    "step_id": 1,
                    "tool": "setup_api_connection",
                    "description": "建立与科来设备的API连接",
                    "parameters": {
                        "machine_ip": machine_ip,
                        "netlink_id": netlink_id
                    },
                    "expected_outcome": "API连接成功建立",
                    "conditional_next": "无条件执行下一步"
                },
                {
                    "step_id": 2,
                    "tool": "query_statistics_table",
                    "description": "查询TCP会话基础信息",
                    "parameters": {
                        "table_name": "tcp_flow",
                        "fields": [
                            "client_ip_addr", "server_ip_addr", "client_port", "server_port",
                            "flow_start_time", "flow_end_time", "tcp_seq", "tcp_ack",
                            "total_byte", "total_packet", "flow_duration",
                            "client_tcp_retransmission_packet", "server_tcp_retransmission_packet"
                        ],
                        "filters": tcp_filter,
                        "limit": 10
                    },
                    "expected_outcome": "获取TCP会话的基础统计信息",
                    "conditional_next": "根据结果决定后续分析"
                },
                {
                    "step_id": 3,
                    "tool": "query_statistics_table",
                    "description": "查询服务器端口统计信息",
                    "parameters": {
                        "table_name": "tcp_server_port",
                        "fields": [
                            "server_ip_addr", "server_port", "total_byte", "total_packet",
                            "visit_count", "tcp_syn_packet", "tcp_rst_packet"
                        ],
                        "filters": f"server_ip_addr={server_ip_addr}" + (f"&&server_port={server_port}" if server_port else ""),
                        "limit": 5
                    },
                    "expected_outcome": "获取服务器端口的统计信息",
                    "conditional_next": "end"
                }
            ],
            "branches": {}
        }
        
        return fallback_chain
    
    def export_mcp_chain_json(self, mcp_chain: Dict[str, Any], output_file: str = None) -> str:
        """
        导出MCP调用链为JSON格式
        
        Args:
            mcp_chain: MCP调用链字典
            output_file: 输出文件路径（可选）
            
        Returns:
            JSON字符串
        """
        import json
        from datetime import datetime
        
        # 美化输出格式
        json_output = json.dumps(mcp_chain, indent=2, ensure_ascii=False)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_output)
            print(f"MCP调用链已导出到: {output_file}")
            
        return json_output
    
    def print_mcp_chain_summary(self, mcp_chain: Dict[str, Any]):
        """
        打印MCP调用链摘要信息
        
        Args:
            mcp_chain: MCP调用链字典
        """
        meta = mcp_chain.get("meta", {})
        print(f"\n=== MCP调用链摘要 ===")
        print(f"会话类型: {meta.get('session_type', '未知')}")
        print(f"分析重点: {meta.get('analysis_focus', '未指定')}")
        print(f"总步骤数: {meta.get('total_steps', len(mcp_chain.get('steps', [])))}")
        print(f"过滤策略: {meta.get('key_filtering_strategy', '默认过滤')}")
        
        if 'fallback_mode' in meta:
            print(f"备用模式: {meta['fallback_mode']}")
        
        print(f"\n=== 顺序执行步骤 ===")
        for step in mcp_chain.get("steps", []):
            print(f"  步骤{step.get('step_id', '?')}: {step.get('tool', '未知工具')} - {step.get('description', '无描述')}")
            
        print(f"\n=== 条件分支 ===")
        branches = mcp_chain.get("branches", {})
        if branches:
            for branch_name, branch_info in branches.items():
                if isinstance(branch_info, dict):
                    print(f"  分支 [{branch_name}]: {branch_info.get('condition', '无条件')}")
                    branch_steps = branch_info.get('steps', [])
                    if isinstance(branch_steps, list):
                        for step in branch_steps:
                            if isinstance(step, dict):
                                print(f"    步骤{step.get('step_id', '?')}: {step.get('tool', '未知工具')} - {step.get('description', '无描述')}")
                            else:
                                print(f"    步骤{step}")
                else:
                    print(f"  分支 [{branch_name}]: {branch_info}")
        else:
            print("  无条件分支")

# 使用示例和测试函数
async def test_packet_drop_hypergraph_rag():
    """测试跳包诊断HyperGraphRAG系统"""
    
    print("=== 初始化跳包诊断HyperGraphRAG系统 ===")
    rag_system = PacketDropHyperGraphRAG()
    
    print("\n=== 插入跳包诊断知识 ===")
    await rag_system.insert_packet_drop_knowledge()
    
    print("\n=== 测试知识查询 ===")
    test_queries = [
        "如何诊断短期会话的跳包问题？",
        "TCP重传率100%应该如何分析？",
        "序列号跳变的检测方法是什么？",
        "长期会话跳包需要使用什么工具？"
    ]
    
    for query in test_queries:
        print(f"\n查询: {query}")
        result = await rag_system.query_packet_drop_knowledge(query)
        print(f"结果: {result}")  # 截取前200字符
        
    return
    
    print("\n=== 测试MCP调用链生成 ===")
    alert_info = {
        "machine_ip": "*************",
        "netlink_id": 2,
        "trigger_time": "2025-06-16 14:00:00",
        "alert_name": "TCP会话统计警报",
        "server_ip_addr":"***********",
        "server_port":80,
        "client_ip_addr":"***********",
        "client_port":10800,
        "condition": "TCP重传率为100% 大于 90%"
    }
    
    mcp_chain = await rag_system.get_diagnostic_mcp_chain(alert_info)
    print(f"生成的MCP调用链包含 {mcp_chain['meta']['total_steps']} 个步骤:")
    
    # 打印调用链摘要
    rag_system.print_mcp_chain_summary(mcp_chain)
    
    # 导出JSON格式
    json_output = rag_system.export_mcp_chain_json(mcp_chain, "diagnostic_mcp_chain.json")
    print(f"\n=== JSON格式输出 ===")
    print(json_output[:500] + "..." if len(json_output) > 500 else json_output)
    
    return rag_system, mcp_chain

if __name__ == "__main__":
    # 运行测试
    rag_system, mcp_chain = asyncio.run(test_packet_drop_hypergraph_rag()) 