#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
经验分析工具 - 用于分析和管理查询经验数据
"""

import json
import asyncio
from typing import Dict, List, Any
from experience_manager import ExperienceManager


class ExperienceAnalyzer:
    """经验分析器"""
    
    def __init__(self, config_path: str = "experience_config.json"):
        """初始化分析器
        
        Args:
            config_path: 配置文件路径
        """
        self.experience_manager = ExperienceManager(config_path)
    
    def print_summary(self):
        """打印经验摘要"""
        summary = self.experience_manager.get_experience_summary()
        
        print("=" * 60)
        print("经验数据摘要")
        print("=" * 60)
        print(f"总经验数量: {summary['total_experiences']}")
        print(f"成功经验数量: {summary['successful_experiences']}")
        print(f"成功率: {summary['success_rate']:.2%}")
        print(f"平均执行时间: {summary['avg_execution_time']:.2f}秒")
        print(f"平均Token使用量: {summary['avg_token_usage']:.0f}")
        
        print("\n最常用工具:")
        for tool, count in summary['most_used_tools']:
            print(f"  - {tool}: {count}次")
    
    def print_pattern_analysis(self):
        """打印模式分析"""
        patterns = self.experience_manager.analyze_query_patterns()
        
        print("\n" + "=" * 60)
        print("查询模式分析")
        print("=" * 60)
        print(f"平均执行时间: {patterns['average_execution_time']:.2f}秒")
        print(f"平均迭代次数: {patterns['average_iterations']:.1f}")
        
        print("\n常见工具序列:")
        for seq_info in patterns['common_tool_sequences']:
            tools = seq_info['sequence']
            count = seq_info['count']
            print(f"  {count}次: {' -> '.join([self._simplify_tool_name(tool) for tool in tools])}")
        
        insights = patterns['performance_insights']
        print(f"\n性能洞察:")
        print(f"  最快查询: {insights['fastest_query_time']:.2f}秒")
        print(f"  最慢查询: {insights['slowest_query_time']:.2f}秒")
        print(f"  最少迭代: {insights['most_efficient_iterations']}")
        print(f"  最多迭代: {insights['least_efficient_iterations']}")
    
    def _simplify_tool_name(self, tool_name: str) -> str:
        """简化工具名称显示"""
        if "mcp_tool_" in tool_name:
            return tool_name.split("mcp_tool_")[-1]
        return tool_name
    
    async def find_similar_queries(self, query: str, top_k: int = 5):
        """查找相似查询"""
        similar = await self.experience_manager.find_similar_experiences(query, top_k)
        
        print(f"\n查询: {query}")
        print("=" * 60)
        print("相似查询:")
        
        if not similar:
            print("  未找到相似查询")
            return
        
        for i, (exp, similarity) in enumerate(similar, 1):
            print(f"\n{i}. 相似度: {similarity:.3f}")
            print(f"   查询: {exp.query}")
            print(f"   执行时间: {exp.execution_time:.2f}秒")
            print(f"   迭代次数: {exp.total_iterations}")
            print(f"   使用工具: {', '.join([self._simplify_tool_name(tool) for tool in exp.tools_used])}")
    
    def generate_few_shot_example(self, query: str):
        """生成few-shot示例"""
        async def _generate():
            similar = await self.experience_manager.find_similar_experiences(query, 1)
            if similar:
                exp, similarity = similar[0]
                example = self.experience_manager.generate_detailed_few_shot_example(exp, similarity)
                print(f"\n基于查询生成的Few-shot示例:")
                print("=" * 60)
                print(example)
            else:
                print("未找到相似经验，无法生成示例")
        
        asyncio.run(_generate())
    
    def export_analysis_report(self, output_file: str = "experience_analysis_report.json"):
        """导出分析报告"""
        summary = self.experience_manager.get_experience_summary()
        patterns = self.experience_manager.analyze_query_patterns()
        
        report = {
            "generated_at": self.experience_manager.experiences[-1].timestamp if self.experience_manager.experiences else "",
            "summary": summary,
            "patterns": patterns,
            "recommendations": [
                "定期清理低质量经验数据",
                "优化常用工具序列",
                "关注执行时间异常的查询",
                "提高相似度阈值以获得更精确的匹配"
            ]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n分析报告已导出到: {output_file}")


def main():
    """主函数"""
    import sys
    
    analyzer = ExperienceAnalyzer()
    
    if len(sys.argv) < 2:
        print("用法:")
        print("  python experience_analyzer.py summary          # 显示摘要")
        print("  python experience_analyzer.py patterns         # 显示模式分析")
        print("  python experience_analyzer.py similar <query>  # 查找相似查询")
        print("  python experience_analyzer.py example <query>  # 生成few-shot示例")
        print("  python experience_analyzer.py export           # 导出分析报告")
        return
    
    command = sys.argv[1]
    
    if command == "summary":
        analyzer.print_summary()
    elif command == "patterns":
        analyzer.print_pattern_analysis()
    elif command == "similar" and len(sys.argv) > 2:
        query = " ".join(sys.argv[2:])
        asyncio.run(analyzer.find_similar_queries(query))
    elif command == "example" and len(sys.argv) > 2:
        query = " ".join(sys.argv[2:])
        analyzer.generate_few_shot_example(query)
    elif command == "export":
        analyzer.export_analysis_report()
    else:
        print("无效的命令或缺少参数")


if __name__ == "__main__":
    main()
