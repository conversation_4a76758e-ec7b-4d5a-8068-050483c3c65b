{'meta': {'session_type': 'short_session', 'analysis_focus': 'TCP序列号跳变检测', 'key_filtering_strategy': '使用TCP会话4元组(client_ip_addr, server_ip_addr, client_port, server_port)进行精确过滤'
    }, 'steps': [
        {'step_id': 1, 'tool': 'setup_api_connection', 'description': '建立与科来设备的API连接', 'parameters': {'machine_ip': '*************', 'username': '<需提供>', 'password': '<需提供>', 'netlink_id': '<需提供>'
            }, 'expected_outcome': 'API连接成功建立', 'conditional_next': '无条件执行下一步'
        },
        {'step_id': 2, 'tool': 'query_statistics_table', 'description': '查询TCP会话基础信息，获取会话起止时间', 'parameters': {'table_name': 'tcp_flow', 'fields': 'session_start_time, session_end_time', 'filters': "server_ip_addr=*********** && alert_time='2025-06-16 14:00:00' && link_id=2", 'sort_field': 'session_start_time', 'limit': 1
            }, 'expected_outcome': '返回会话持续时间为2秒（预期值）', 'conditional_next': {'duration <= 2': 'step_3', 'duration > 2': 'step_4'
            }
        },
        {'step_id': 3, 'tool': 'query_statistics_table', 'description': '短期会话分析：查询告警前1秒数据包序列号', 'parameters': {'table_name': 'tcp_flow', 'fields': 'tcp_seq', 'filters': "server_ip_addr=*********** && client_ip_addr=*********** && server_port=80 && client_port=10800 && time_range='2025-06-16 13:59:59 TO 2025-06-16 14:00:00'", 'sort_field': 'timestamp', 'limit': 10
            }, 'expected_outcome': '获取第1秒末和第2秒初的TCP序列号', 'conditional_next': {'检测到Seq跳变（如1000000→4294967295）': 'step_5', '无Seq跳变': 'step_6'
            }
        },
        {'step_id': 4, 'tool': 'query_statistics_table', 'description': '长期会话分析：二分查找定位异常时间桶', 'parameters': {'table_name': 'tcp_flow', 'fields': 'retransmission_rate', 'filters': 'server_ip_addr=*********** && client_ip_addr=*********** && server_port=80 && client_port=10800', 'time_buckets': ['10m', '5m', '1m', '10s', '1s'
                ], 'target_rate': 100
            }, 'expected_outcome': '定位到首个100%重传率的1秒时间桶', 'conditional_next': 'step_3'
        },
        {'step_id': 5, 'tool': 'generate_report', 'description': '确认序列号跳变根因', 'parameters': {'diagnosis': '序列号跳变导致的跳包', 'root_cause': '设备序列号溢出引发后续包重传误判'
            }, 'expected_outcome': '生成诊断报告', 'conditional_next': 'end'
        },
        {'step_id': 6, 'tool': 'query_statistics_table', 'description': '检查统计算法异常', 'parameters': {'table_name': 'system_metrics', 'fields': 'timestamp, cpu_usage, memory_usage', 'filters': "metric_type='packet_processing' && time_range='2025-06-16 13:55:00 TO 2025-06-16 14:05:00'"
            }, 'expected_outcome': '确认是否因资源过载导致统计误差', 'conditional_next': 'end'
        }
    ], 'branches': {'short_session_flow': [
            2,
            3,
            5
        ], 'long_session_flow': [
            2,
            4,
            3,
            5
        ], 'no_seq_jump': [
            2,
            3,
            6
        ]
    }
}