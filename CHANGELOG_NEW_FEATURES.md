# 统计数据查询服务 MCP 工具更新日志

## 2025-01-18 新增功能

基于科来接口文档，为 `stats_mcp_server_official.py` 新增了以下 MCP 工具功能：

### 1. 配置管理

#### get_config(config_type, netlink_id)
- **功能**: 获取系统配置信息（基于6.1配置获取接口）
- **参数**:
  - `config_type`: 配置类型（如 application, adapter, netlink 等）
  - `netlink_id`: 链路ID（系统配置固定为64）
- **返回**: 包含XML格式配置内容的JSON

#### set_config(config_type, xml_config, netlink_id, child_type)
- **功能**: 设置系统配置（基于7.2配置接口说明）
- **参数**:
  - `config_type`: 配置类型
  - `xml_config`: XML格式的配置内容
  - `netlink_id`: 链路ID
  - `child_type`: 子类型（通常留空）
- **返回**: 配置设置结果

### 2. 统计表管理

#### list_statistics_tables()
- **功能**: 枚举系统中支持的统计表（基于8.2枚举统计表接口）
- **返回**: 统计表列表，包含表名和描述

#### list_table_fields(table_name)
- **功能**: 枚举指定统计表的字段信息（基于8.3枚举统计表字段接口）
- **参数**:
  - `table_name`: 统计表名称
- **返回**: 字段列表，包含字段名、类型、是否为key等信息

### 3. 数据包处理

#### download_packets(netlink_id, begin_time, end_time, filter_condition, output_format)
- **功能**: 下载指定时间段的数据包（基于10.2下载数据包接口）
- **参数**:
  - `netlink_id`: 链路ID
  - `begin_time`: 开始时间
  - `end_time`: 结束时间
  - `filter_condition`: 过滤条件
  - `output_format`: 输出格式（summary/detail）
- **返回**: 数据包信息

#### get_packet_summary_decode(netlink_id, begin_time, end_time, filter_condition, max_bytes, max_packets)
- **功能**: 获取数据包概要解码信息（基于11.2数据包概要解码接口）
- **参数**:
  - `netlink_id`: 链路ID
  - `begin_time`: 开始时间
  - `end_time`: 结束时间
  - `filter_condition`: 过滤条件
  - `max_bytes`: 总字节数限制
  - `max_packets`: 总数据包限制
- **返回**: 数据包概要解码信息

### 4. 辅助工具

#### get_config_types()
- **功能**: 获取支持的配置类型说明
- **返回**: 配置类型的详细说明

#### get_supported_filters()
- **功能**: 获取支持的过滤器对象和使用说明
- **返回**: 过滤器的详细说明和示例

## 使用示例

```python
# 1. 连接API
setup_api_connection(url='https://192.168.163.209:8080/')

# 2. 获取配置
result = get_config(config_type='application', netlink_id=1)

# 3. 枚举统计表
tables = list_statistics_tables()

# 4. 查看表字段
fields = list_table_fields(table_name='tcp_flow')

# 5. 下载数据包
packets = download_packets(
    netlink_id=1,
    begin_time='2025-01-15 10:00:00',
    end_time='2025-01-15 10:05:00',
    filter_condition='port=80'
)

# 6. 获取数据包解码
decode_info = get_packet_summary_decode(
    netlink_id=1,
    begin_time='2025-01-15 10:00:00',
    end_time='2025-01-15 10:05:00',
    filter_condition='ip_addr=*************'
)
```

## 注意事项

1. 所有新增功能都需要先调用 `setup_api_connection()` 建立连接
2. 时间格式统一为 `YYYY-MM-DD HH:MM:SS`
3. 配置设置时需要提供有效的XML格式内容
4. 过滤条件支持多种格式，具体参考 `get_supported_filters()`

## 技术细节

- 新增功能基于科来网络回溯分析系统API文档 v6.1 版本
- 所有功能都通过 FastMCP 框架实现为 MCP 工具
- 支持错误处理和详细的返回信息 