#{"query": "{{#sys.query#}}", "mode": "hyper", "workdir": "caches/enhanced_bugs_demo_optimized","only_context":"true"}
# 将arg1（query）填进去

import json

def main(arg1):
    # 创建配置模板，将arg1（query）替换掉占位符
    config = {
        "query": arg1,
        "mode": "hyper", 
        "workdir": "caches/enhanced_bugs_demo_optimized",
        "only_context": "true"
    }
    
    # 将config格式化为JSON字符串
    config_str = json.dumps(config)
    
    return {
        "result": config_str
    }