#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跳包诊断系统集成测试
验证HyperGraphRAG知识召回、MCP调用链生成和Autogen执行的完整流程
"""

import asyncio
import json
import time
from typing import Dict, Any, List

def test_knowledge_corpus():
    """测试1: 验证知识语料库"""
    print("🧪 测试1: 知识语料库验证")
    
    try:
        from packet_drop_knowledge_corpus import get_knowledge_corpus, get_chunked_knowledge
        
        corpus = get_knowledge_corpus()
        print(f"✅ 知识语料库包含 {len(corpus)} 个文档")
        
        chunks = get_chunked_knowledge(chunk_size=500)
        print(f"✅ 分块后包含 {len(chunks)} 个知识片段")
        
        # 检查知识覆盖度
        key_concepts = ["短期会话", "长期会话", "序列号跳变", "重传率", "在线解码", "二分查找"]
        coverage = {}
        
        for concept in key_concepts:
            concept_count = sum(1 for doc in corpus if concept in doc)
            coverage[concept] = concept_count
            print(f"  {concept}: 出现在 {concept_count} 个文档中")
        
        print("✅ 知识语料库测试通过\n")
        return True
        
    except Exception as e:
        print(f"❌ 知识语料库测试失败: {e}\n")
        return False

def test_mcp_tools():
    """测试2: 验证MCP工具"""
    print("🧪 测试2: MCP工具验证")
    
    try:
        from autogen_mcp_executor import MCPToolExecutor
        
        async def test_mcp_async():
            executor = MCPToolExecutor()
            
            # 测试API连接
            result1 = await executor.execute_tool("setup_api_connection", {
                "url": "https://192.168.163.209:8080/"
            })
            assert result1["success"], "API连接失败"
            print("✅ API连接工具测试通过")
            
            # 测试统计查询
            result2 = await executor.execute_tool("query_statistics_table", {
                "table": "service_access",
                "begintime": "2025-06-16 13:50:00",
                "endtime": "2025-06-16 14:00:00",
                "timeunit": 1000,
                "netlink": 2
            })
            assert result2["success"], "统计查询失败"
            print("✅ 统计查询工具测试通过")
            
            # 测试数据包解码
            result3 = await executor.execute_tool("get_detailed_packet_decode", {
                "begin_time": "2025-06-16 13:59:59",
                "end_time": "2025-06-16 14:00:00",
                "server_ip": "*************",
                "decode_options": "detailed"
            })
            assert result3["success"], "数据包解码失败"
            print("✅ 数据包解码工具测试通过")
            
            # 验证执行日志
            assert len(executor.execution_log) == 3, "执行日志数量不正确"
            print("✅ MCP执行日志记录正常")
            
            return True
        
        result = asyncio.run(test_mcp_async())
        print("✅ MCP工具测试通过\n")
        return result
        
    except Exception as e:
        print(f"❌ MCP工具测试失败: {e}\n")
        return False

def test_diagnostic_chain_generation():
    """测试3: 验证诊断链生成"""
    print("🧪 测试3: 诊断链生成验证")
    
    try:
        from autogen_mcp_executor import get_llm_generated_mcp_chain, get_minimal_fallback_chain
        
        # 测试不同类型的警报
        test_alerts = [
            {
                "server_ip": "*************",
                "link_id": 2,
                "trigger_time": "2025-06-16 14:00:00",
                "alert_name": "TCP会话统计警报",
                "condition": "TCP重传率为100% 大于 90%"
            },
            {
                "server_ip": "**********",
                "link_id": 1,
                "trigger_time": "2025-06-16 15:30:00",
                "alert_name": "长期连接警报",
                "condition": "连续重传超过阈值"
            }
        ]
        
        async def test_chain_generation_async():
            results = []
            
            for i, alert in enumerate(test_alerts, 1):
                print(f"  测试警报 {i}: {alert['server_ip']}")
                
                try:
                    # 尝试使用LLM生成
                    chain = await get_llm_generated_mcp_chain(alert)
                    print(f"    ✅ LLM生成成功")
                except Exception as e:
                    print(f"    ⚠️ LLM生成失败: {e}")
                    # 使用最简备用方案
                    chain = get_minimal_fallback_chain(alert)
                    print(f"    ✅ 备用方案生成成功")
                
                # 验证新JSON格式结构
                assert "meta" in chain, "缺少元数据"
                assert "steps" in chain, "缺少步骤列表" 
                assert "branches" in chain, "缺少分支信息"
                
                # 验证元数据
                meta = chain["meta"]
                assert "total_steps" in meta, "缺少总步骤数"
                assert "diagnostic_strategy" in meta, "缺少诊断策略"
                
                # 验证步骤结构
                steps = chain["steps"]
                assert len(steps) >= 2, f"调用链步骤太少: {len(steps)}"
                assert steps[0]["tool"] == "setup_api_connection", "首步应该是API连接"
                
                # 验证分支结构（如果有）
                branches = chain["branches"]
                for branch_name, branch_info in branches.items():
                    assert "condition" in branch_info, f"分支 {branch_name} 缺少条件"
                    assert "description" in branch_info, f"分支 {branch_name} 缺少描述"
                    assert "steps" in branch_info, f"分支 {branch_name} 缺少步骤"
                    
                    # 验证分支步骤
                    for step in branch_info["steps"]:
                        assert "step_id" in step, "分支步骤缺少ID"
                        assert "step_type" in step, "分支步骤缺少类型"
                        assert step["step_type"] == "conditional", "分支步骤应该是条件类型"
                        assert "branch" in step, "分支步骤缺少分支标识"
                        assert step["branch"] == branch_name, "分支步骤的分支标识不匹配"
                
                print(f"    ✅ 生成了 {meta['total_steps']} 步调用链，包含 {len(branches)} 个分支")
                print(f"    📊 诊断策略: {meta['diagnostic_strategy']}")
                print(f"    ⏱️ 预估耗时: {meta.get('estimated_total_duration', 'N/A')}")
                
                # 打印分支信息
                for branch_name, branch_info in branches.items():
                    print(f"    🌿 分支 [{branch_name}]: {branch_info['condition']}")
                    print(f"       📝 {branch_info['description']}")
                    print(f"       🔧 包含 {len(branch_info['steps'])} 个条件步骤")
                
                results.append({
                    "alert": alert,
                    "chain": chain,
                    "success": True
                })
            
            return results
        
        # 运行异步测试
        results = asyncio.run(test_chain_generation_async())
        
        print("✅ 诊断链生成测试通过（LLM智能生成）\n")
        return results
        
    except Exception as e:
        print(f"❌ 诊断链生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_end_to_end_diagnosis():
    """测试4: 端到端诊断流程"""
    print("🧪 测试4: 端到端诊断流程验证")
    
    try:
        from autogen_mcp_executor import AutogenMCPExecutor, get_llm_generated_mcp_chain, get_minimal_fallback_chain
        
        async def test_e2e_async():
            # 准备测试数据
            alert_info = {
                "server_ip": "*************",
                "link_id": 2,
                "trigger_time": "2025-06-16 14:00:00",
                "alert_name": "TCP会话统计警报",
                "condition": "TCP重传率为100% 大于 90%"
            }
            
            # 生成MCP调用链
            try:
                mcp_chain = await get_llm_generated_mcp_chain(alert_info)
                print(f"  ✅ LLM生成调用链成功")
            except Exception as e:
                print(f"  ⚠️ LLM生成失败，使用备用方案: {e}")
                mcp_chain = get_minimal_fallback_chain(alert_info)
            
            print(f"  生成调用链: {mcp_chain['meta']['total_steps']} 个步骤")
            
            # 执行诊断
            executor = AutogenMCPExecutor()
            start_time = time.time()
            
            result = await executor.execute_mcp_chain(mcp_chain, alert_info)
            
            execution_time = time.time() - start_time
            print(f"  执行时间: {execution_time:.2f} 秒")
            
            # 验证结果
            assert "final_diagnosis" in result, "缺少最终诊断"
            assert "execution_results" in result, "缺少执行结果"
            assert "diagnostic_conclusions" in result, "缺少诊断结论"
            
            final_diagnosis = result["final_diagnosis"]
            assert "diagnosis" in final_diagnosis, "缺少诊断结论"
            assert "confidence_score" in final_diagnosis, "缺少置信度"
            assert "evidence" in final_diagnosis, "缺少证据"
            assert "recommendation" in final_diagnosis, "缺少建议"
            
            print(f"  诊断结论: {final_diagnosis['diagnosis']}")
            print(f"  置信度: {final_diagnosis['confidence_score']}%")
            print(f"  证据数量: {len(final_diagnosis['evidence'])}")
            
            # 验证执行统计
            total_steps = result["total_steps"]
            successful_steps = result["successful_steps"]
            success_rate = (successful_steps / total_steps) * 100
            
            print(f"  成功率: {success_rate}% ({successful_steps}/{total_steps})")
            
            assert success_rate >= 80, f"成功率过低: {success_rate}%"
            
            return result
        
        result = asyncio.run(test_e2e_async())
        print("✅ 端到端诊断流程测试通过\n")
        return result
        
    except Exception as e:
        print(f"❌ 端到端诊断流程测试失败: {e}\n")
        return False

def test_different_scenarios():
    """测试5: 不同场景验证"""
    print("🧪 测试5: 不同诊断场景验证")
    
    try:
        from autogen_mcp_executor import AutogenMCPExecutor, get_llm_generated_mcp_chain, get_minimal_fallback_chain
        
        # 定义不同的测试场景
        scenarios = [
            {
                "name": "短期会话跳包",
                "alert": {
                    "server_ip": "*************",
                    "link_id": 2,
                    "trigger_time": "2025-06-16 14:00:00",
                    "session_duration": 2,  # 秒
                    "retransmission_rate": 100
                },
                "expected_diagnosis": "确认跳包",
                "expected_confidence": 70
            },
            {
                "name": "长期会话连续重传",
                "alert": {
                    "server_ip": "**********",
                    "link_id": 1,
                    "trigger_time": "2025-06-16 15:30:00",
                    "session_duration": 1800,  # 30分钟
                    "retransmission_rate": 100
                },
                "expected_diagnosis": "疑似跳包",
                "expected_confidence": 40
            },
            {
                "name": "误报检测",
                "alert": {
                    "server_ip": "***********",
                    "link_id": 3,
                    "trigger_time": "2025-06-16 16:45:00",
                    "session_duration": 60,
                    "retransmission_rate": 5  # 正常重传率
                },
                "expected_diagnosis": "跳包可能性较低",
                "expected_confidence": 30
            }
        ]
        
        async def test_scenarios_async():
            results = {}
            
            for scenario in scenarios:
                print(f"  测试场景: {scenario['name']}")
                
                alert = scenario["alert"]
                
                try:
                    mcp_chain = await get_llm_generated_mcp_chain(alert)
                    print(f"    ✅ LLM生成调用链成功")
                except Exception as e:
                    print(f"    ⚠️ LLM生成失败，使用备用方案: {e}")
                    mcp_chain = get_minimal_fallback_chain(alert)
                
                executor = AutogenMCPExecutor()
                result = await executor.execute_mcp_chain(mcp_chain, alert)
                
                final_diagnosis = result["final_diagnosis"]
                actual_confidence = final_diagnosis["confidence_score"]
                
                print(f"    实际诊断: {final_diagnosis['diagnosis']}")
                print(f"    实际置信度: {actual_confidence}%")
                
                # 记录结果
                results[scenario["name"]] = {
                    "diagnosis": final_diagnosis["diagnosis"],
                    "confidence": actual_confidence,
                    "evidence_count": len(final_diagnosis["evidence"]),
                    "success_rate": result["successful_steps"] / result["total_steps"] * 100
                }
            
            return results
        
        results = asyncio.run(test_scenarios_async())
        
        # 输出汇总
        print("  场景测试汇总:")
        for scenario_name, result in results.items():
            print(f"    {scenario_name}: {result['diagnosis']} ({result['confidence']}%)")
        
        print("✅ 不同场景验证测试通过\n")
        return results
        
    except Exception as e:
        print(f"❌ 不同场景验证测试失败: {e}\n")
        return False

def test_performance_benchmark():
    """测试6: 性能基准测试"""
    print("🧪 测试6: 性能基准测试")
    
    try:
        from autogen_mcp_executor import AutogenMCPExecutor, get_llm_generated_mcp_chain, get_minimal_fallback_chain
        
        async def benchmark_async():
            alert_info = {
                "server_ip": "*************",
                "link_id": 2,
                "trigger_time": "2025-06-16 14:00:00",
                "alert_name": "性能测试警报"
            }
            
            # 预生成调用链避免重复LLM调用
            try:
                mcp_chain = await get_llm_generated_mcp_chain(alert_info)
                print(f"  ✅ LLM生成测试调用链成功")
            except Exception as e:
                print(f"  ⚠️ LLM生成失败，使用备用方案: {e}")
                mcp_chain = get_minimal_fallback_chain(alert_info)
            
            executor = AutogenMCPExecutor()
            
            # 执行多次测试
            times = []
            for i in range(3):
                start_time = time.time()
                result = await executor.execute_mcp_chain(mcp_chain, alert_info)
                execution_time = time.time() - start_time
                times.append(execution_time)
                print(f"  第 {i+1} 次执行: {execution_time:.2f} 秒")
            
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            
            print(f"  平均执行时间: {avg_time:.2f} 秒")
            print(f"  最快执行时间: {min_time:.2f} 秒")
            print(f"  最慢执行时间: {max_time:.2f} 秒")
            
            # 性能要求
            assert avg_time < 10, f"平均执行时间过长: {avg_time:.2f} 秒"
            assert max_time < 15, f"最大执行时间过长: {max_time:.2f} 秒"
            
            return {
                "average_time": avg_time,
                "min_time": min_time,
                "max_time": max_time,
                "total_runs": len(times)
            }
        
        benchmark_result = asyncio.run(benchmark_async())
        print("✅ 性能基准测试通过\n")
        return benchmark_result
        
    except Exception as e:
        print(f"❌ 性能基准测试失败: {e}\n")
        return False

def generate_test_report(test_results: Dict[str, Any]):
    """生成测试报告"""
    print("📊 生成测试报告")
    
    report = {
        "test_summary": {
            "total_tests": len(test_results),
            "passed_tests": sum(1 for result in test_results.values() if result is not False),
            "failed_tests": sum(1 for result in test_results.values() if result is False),
            "success_rate": 0
        },
        "test_details": test_results,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "conclusions": []
    }
    
    # 计算成功率
    total = report["test_summary"]["total_tests"]
    passed = report["test_summary"]["passed_tests"]
    report["test_summary"]["success_rate"] = (passed / total * 100) if total > 0 else 0
    
    # 生成结论
    if passed == total:
        report["conclusions"].append("🎉 所有测试通过！系统功能完整，可以投入使用。")
    elif passed >= total * 0.8:
        report["conclusions"].append("✅ 大部分测试通过，系统基本可用，需要修复少量问题。")
    else:
        report["conclusions"].append("⚠️ 多个测试失败，系统存在重要问题，需要进一步调试。")
    
    # 保存测试报告
    report_file = f"integration_test_report_{int(time.time())}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    # 输出摘要
    print(f"  测试总数: {total}")
    print(f"  通过测试: {passed}")
    print(f"  失败测试: {report['test_summary']['failed_tests']}")
    print(f"  成功率: {report['test_summary']['success_rate']:.1f}%")
    print(f"  报告文件: {report_file}")
    
    for conclusion in report["conclusions"]:
        print(f"  {conclusion}")
    
    return report

async def run_full_integration_test():
    """运行完整的集成测试"""
    print("🚀 跳包诊断系统集成测试开始")
    print("=" * 60)
    
    test_results = {}
    
    # 执行所有测试
    test_results["knowledge_corpus"] = test_knowledge_corpus()
    test_results["mcp_tools"] = test_mcp_tools()
    test_results["diagnostic_chain"] = test_diagnostic_chain_generation()
    test_results["end_to_end"] = test_end_to_end_diagnosis()
    test_results["scenarios"] = test_different_scenarios()
    test_results["performance"] = test_performance_benchmark()
    
    # 生成测试报告
    print("=" * 60)
    report = generate_test_report(test_results)
    
    print("\n🏁 集成测试完成!")
    return report

if __name__ == "__main__":
    # 运行完整的集成测试
    asyncio.run(run_full_integration_test()) 