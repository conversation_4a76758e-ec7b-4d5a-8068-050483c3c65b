#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Autogen的MCP调用链执行器
自动执行跳包诊断的MCP工具调用序列
"""

import asyncio
import json
import traceback
from typing import List, Dict, Any, Optional
from datetime import datetime

try:
    import autogen
    from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager
except ImportError:
    print("注意：需要安装autogen包：pip install pyautogen")
    # 提供一个模拟的Autogen实现用于演示
    class MockAutogenAgent:
        def __init__(self, name, **kwargs):
            self.name = name
            self.kwargs = kwargs
        
        def generate_reply(self, message, sender):
            return f"[{self.name}] 收到来自 {sender.name} 的消息: {message}"

class MCPToolExecutor:
    """MCP工具执行器"""
    
    def __init__(self):
        """初始化MCP工具执行器"""
        self.api_client = None
        self.execution_log = []
    
    async def execute_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行单个MCP工具
        
        Args:
            tool_name: 工具名称
            parameters: 工具参数
            
        Returns:
            执行结果
        """
        start_time = datetime.now()
        
        try:
            # 根据工具名称调用相应的MCP工具
            if tool_name == "setup_api_connection":
                result = await self._setup_api_connection(parameters)
            elif tool_name == "query_statistics_table":
                result = await self._query_statistics_table(parameters)
            elif tool_name == "get_detailed_packet_decode":
                result = await self._get_detailed_packet_decode(parameters)
            else:
                result = {
                    "success": False,
                    "message": f"未知的工具: {tool_name}"
                }
            
            # 记录执行日志
            execution_time = (datetime.now() - start_time).total_seconds()
            log_entry = {
                "tool": tool_name,
                "parameters": parameters,
                "result": result,
                "execution_time": execution_time,
                "timestamp": start_time.isoformat()
            }
            self.execution_log.append(log_entry)
            
            return result
            
        except Exception as e:
            error_result = {
                "success": False,
                "message": f"工具执行失败: {str(e)}",
                "error": traceback.format_exc()
            }
            
            execution_time = (datetime.now() - start_time).total_seconds()
            log_entry = {
                "tool": tool_name,
                "parameters": parameters,
                "result": error_result,
                "execution_time": execution_time,
                "timestamp": start_time.isoformat()
            }
            self.execution_log.append(log_entry)
            
            return error_result
    
    async def _setup_api_connection(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """模拟API连接设置"""
        # 在真实环境中，这里会调用实际的MCP服务器
        await asyncio.sleep(0.5)  # 模拟网络延迟
        return {
            "success": True,
            "message": f"成功连接到API服务器: {parameters.get('url')}",
            "connection_id": "mock_connection_123"
        }
    
    async def _query_statistics_table(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """模拟统计表查询"""
        await asyncio.sleep(1.0)  # 模拟查询延迟
        
        # 生成模拟的统计数据
        table = parameters.get("table", "unknown")
        timeunit = parameters.get("timeunit", 0)
        
        if table == "service_access" and timeunit == 1000:
            # 模拟秒级会话统计
            mock_data = """time,server_ip_addr,total_byte,total_packet
2025-06-16 13:59:58,*************,1024,5
2025-06-16 13:59:59,*************,2048,3"""
            
            return {
                "success": True,
                "message": "查询成功",
                "data": mock_data,
                "analysis": "会话持续2秒，属于短期会话"
            }
        
        elif table == "ip_flow" and timeunit == 60000:
            # 模拟分钟级流量统计  
            mock_data = """time,ip_endpoint1,total_byte,total_packet
2025-06-16 13:50:00,*************,1048576,1000
2025-06-16 13:51:00,*************,0,0
2025-06-16 13:52:00,*************,0,0"""
            
            return {
                "success": True,
                "message": "查询成功",
                "data": mock_data,
                "analysis": "发现连续2分钟零传输，疑似持续跳包"
            }
        
        else:
            return {
                "success": True,
                "message": "查询成功",
                "data": f"table={table},timeunit={timeunit}的模拟数据",
                "record_count": 10
            }
    
    async def _get_detailed_packet_decode(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """模拟数据包详细解码"""
        await asyncio.sleep(2.0)  # 模拟解码延迟
        
        decode_level = parameters.get("decode_options", "basic")
        server_ip = parameters.get("server_ip")
        
        # 模拟包解码数据
        if decode_level == "detailed":
            mock_packets = [
                {
                    "timestamp": "2025-06-16 13:59:59.100",
                    "seq_number": 1000000,
                    "is_retransmission": False,
                    "anomaly_indicators": {"is_seq_anomaly": False}
                },
                {
                    "timestamp": "2025-06-16 13:59:59.200",
                    "seq_number": 4294967295,  # 异常大的序列号
                    "is_retransmission": False,
                    "anomaly_indicators": {"is_seq_anomaly": True, "seq_jump_size": 4293967295}
                },
                {
                    "timestamp": "2025-06-16 14:00:00.100",
                    "seq_number": 1000000,
                    "is_retransmission": True,
                    "anomaly_indicators": {"is_seq_anomaly": False}
                }
            ]
            
            analysis = {
                "seq_anomalies_found": True,
                "max_seq_jump": 4293967295,
                "retransmission_rate": 33.3,
                "diagnosis": "检测到序列号跳变，确认存在跳包"
            }
        
        else:
            mock_packets = [{"basic_info": "模拟基础包信息"}]
            analysis = {"basic_analysis": "基础分析结果"}
        
        return {
            "success": True,
            "message": "数据包解码成功",
            "packets": mock_packets,
            "analysis": analysis,
            "packet_count": len(mock_packets)
        }

class AutogenMCPExecutor:
    """基于Autogen的MCP调用链执行器"""
    
    def __init__(self):
        """初始化Autogen MCP执行器"""
        self.mcp_executor = MCPToolExecutor()
        self._setup_autogen_agents()
    
    def _setup_autogen_agents(self):
        """设置Autogen代理"""
        
        # 配置LLM（这里使用模拟配置）
        llm_config = {
            "timeout": 120,
            "cache_seed": 42,
            "temperature": 0.1,
            "config_list": [
                {
                    "model": "gpt-4o-mini",
                    "api_key": "mock_key",  # 在实际使用中需要真实的API密钥
                }
            ]
        }
        
        try:
            # 诊断专家代理
            self.diagnostic_expert = AssistantAgent(
                name="DiagnosticExpert",
                llm_config=llm_config,
                system_message="""
                你是一名网络跳包诊断专家。你的任务是：
                1. 分析MCP工具的执行结果
                2. 根据结果判断是否需要执行下一步
                3. 提供专业的诊断建议
                4. 总结最终的诊断结论
                
                请用专业且易懂的语言回答问题。
                """
            )
            
            # MCP工具执行代理
            self.mcp_agent = AssistantAgent(
                name="MCPExecutor",
                llm_config=llm_config,
                system_message="""
                你是MCP工具执行代理。你的任务是：
                1. 执行指定的MCP工具调用
                2. 返回工具执行结果
                3. 报告执行状态和错误信息
                
                请严格按照工具调用规范执行。
                """
            )
            
            # 用户代理（协调者）
            self.coordinator = UserProxyAgent(
                name="Coordinator",
                human_input_mode="NEVER",
                max_consecutive_auto_reply=10,
                is_termination_msg=lambda x: x.get("content", "").find("TERMINATE") >= 0,
                code_execution_config=False
            )
            
            # 创建群聊
            self.group_chat = GroupChat(
                agents=[self.diagnostic_expert, self.mcp_agent, self.coordinator],
                messages=[],
                max_round=20
            )
            
            self.manager = GroupChatManager(
                groupchat=self.group_chat,
                llm_config=llm_config
            )
            
        except Exception as e:
            print(f"Autogen初始化失败，使用模拟模式: {e}")
            # 使用模拟代理
            self.diagnostic_expert = MockAutogenAgent("DiagnosticExpert")
            self.mcp_agent = MockAutogenAgent("MCPExecutor")
            self.coordinator = MockAutogenAgent("Coordinator")
    
    async def execute_mcp_chain(self, mcp_chain_data: Dict[str, Any], alert_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行完整的MCP调用链
        
        Args:
            mcp_chain_data: 完整的MCP调用链数据（包含meta、steps、branches）
            alert_info: 警报信息
            
        Returns:
            执行结果和诊断报告
        """
        # 兼容新旧格式
        if isinstance(mcp_chain_data, list):
            # 旧格式：直接是步骤列表
            mcp_steps = mcp_chain_data
            mcp_chain = {"steps": mcp_steps, "meta": {"total_steps": len(mcp_steps)}, "branches": {}}
        else:
            # 新格式：包含meta、steps、branches
            mcp_chain = mcp_chain_data
            mcp_steps = mcp_chain.get("steps", [])
        
        print(f"开始执行MCP调用链，共 {len(mcp_steps)} 个步骤")
        
        execution_results = []
        diagnostic_conclusions = []
        
        for step_info in mcp_steps:
            step = step_info.get("step_id", step_info.get("step", "unknown"))
            tool = step_info.get("tool", "unknown")
            purpose = step_info.get("purpose", "unknown")
            parameters = step_info.get("parameters", {})
            reasoning = step_info.get("reasoning", "unknown")
            
            print(f"\n执行步骤 {step}: {tool}")
            print(f"目的: {purpose}")
            print(f"推理: {reasoning}")
            
            # 执行MCP工具
            result = await self.mcp_executor.execute_tool(tool, parameters)
            execution_results.append({
                "step": step,
                "tool": tool,
                "purpose": purpose,
                "result": result
            })
            
            # 分析执行结果
            if result.get("success"):
                print(f"✅ 步骤 {step} 执行成功")
                
                # 根据结果进行诊断分析
                if tool == "query_statistics_table" and "service_access" in parameters.get("table", ""):
                    analysis = result.get("analysis", "")
                    if "短期会话" in analysis:
                        diagnostic_conclusions.append("会话类型: 短期会话（≤2秒）")
                        diagnostic_conclusions.append("诊断策略: 重点关注序列号跳变")
                
                elif tool == "get_detailed_packet_decode":
                    analysis = result.get("analysis", {})
                    if isinstance(analysis, dict):
                        if analysis.get("seq_anomalies_found"):
                            diagnostic_conclusions.append("发现序列号异常跳变")
                            diagnostic_conclusions.append(f"最大跳变幅度: {analysis.get('max_seq_jump', 'N/A')}")
                        
                        retrans_rate = analysis.get("retransmission_rate", 0)
                        if retrans_rate > 0:
                            diagnostic_conclusions.append(f"重传率: {retrans_rate}%")
                
            else:
                print(f"❌ 步骤 {step} 执行失败: {result.get('message')}")
                diagnostic_conclusions.append(f"步骤{step}执行失败，可能影响诊断准确性")
        
        # 生成最终诊断报告
        final_diagnosis = self._generate_final_diagnosis(
            execution_results, diagnostic_conclusions, alert_info
        )
        
        return {
            "alert_info": alert_info,
            "mcp_chain_meta": mcp_chain.get("meta", {}),
            "execution_results": execution_results,
            "diagnostic_conclusions": diagnostic_conclusions,
            "final_diagnosis": final_diagnosis,
            "execution_log": self.mcp_executor.execution_log,
            "total_steps": len(mcp_steps),
            "successful_steps": sum(1 for r in execution_results if r["result"].get("success"))
        }
    
    def _generate_final_diagnosis(self, execution_results: List[Dict], 
                                 conclusions: List[str], alert_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成最终诊断报告"""
        
        # 分析执行结果
        successful_steps = [r for r in execution_results if r["result"].get("success")]
        failed_steps = [r for r in execution_results if not r["result"].get("success")]
        
        # 判断跳包可能性
        packet_drop_indicators = []
        confidence_score = 0
        
        for conclusion in conclusions:
            if "序列号异常跳变" in conclusion or "序列号跳变" in conclusion:
                packet_drop_indicators.append("序列号跳变")
                confidence_score += 40
            elif "重传率" in conclusion and "100%" in conclusion:
                packet_drop_indicators.append("100%重传率")
                confidence_score += 30
            elif "短期会话" in conclusion:
                packet_drop_indicators.append("短期会话特征")
                confidence_score += 10
        
        # 判断诊断结论
        if confidence_score >= 70:
            diagnosis = "确认跳包"
            recommendation = "立即检查网络设备配置，重点关注序列号处理逻辑"
        elif confidence_score >= 40:
            diagnosis = "疑似跳包"
            recommendation = "建议进一步监控和分析，收集更多证据"
        else:
            diagnosis = "跳包可能性较低"
            recommendation = "检查统计算法和监控配置，可能存在误报"
        
        return {
            "diagnosis": diagnosis,
            "confidence_score": min(confidence_score, 100),
            "evidence": packet_drop_indicators,
            "recommendation": recommendation,
            "analysis_summary": {
                "alert_info": alert_info,
                "successful_tools": len(successful_steps),
                "failed_tools": len(failed_steps),
                "key_findings": conclusions[:5]  # 前5个关键发现
            }
        }

# 完整的测试和演示函数
async def run_complete_packet_drop_diagnosis():
    """运行完整的跳包诊断流程"""
    
    print("=== 完整跳包诊断流程演示 ===\n")
    
    # 1. 初始化HyperGraphRAG系统
    print("步骤1: 初始化HyperGraphRAG知识库")
    try:
        from packet_drop_hypergraph_rag import PacketDropHyperGraphRAG
        rag_system = PacketDropHyperGraphRAG()
        await rag_system.insert_packet_drop_knowledge()
        print("✅ HyperGraphRAG知识库初始化完成")
    except Exception as e:
        print(f"❌ HyperGraphRAG初始化失败: {e}")
        print("使用模拟模式继续演示...")
        rag_system = None
    
    # 2. 模拟警报输入
    print("\n步骤2: 处理警报输入")
    alert_info = {
        "server_ip": "*************",
        "link_id": 2,
        "trigger_time": "2025-06-16 14:00:00",
        "alert_name": "TCP会话统计警报",
        "condition": "TCP重传率为100% 大于 90%"
    }
    print(f"警报信息: {json.dumps(alert_info, ensure_ascii=False, indent=2)}")
    
    # 3. 生成MCP调用链
    print("\n步骤3: 基于HyperGraphRAG生成MCP调用链")
    if rag_system:
        try:
            mcp_chain = await rag_system.get_diagnostic_mcp_chain(alert_info)
            print(f"✅ 生成了包含 {mcp_chain['meta']['total_steps']} 个步骤的MCP调用链")
        except Exception as e:
            print(f"❌ MCP调用链生成失败: {e}")
            mcp_chain = await get_llm_generated_mcp_chain(alert_info)
    else:
        mcp_chain = await get_llm_generated_mcp_chain(alert_info)
    
    for i, step in enumerate(mcp_chain["steps"], 1):
        print(f"  {i}. {step['tool']} - {step['purpose']}")
    
    # 4. 使用Autogen执行MCP调用链
    print("\n步骤4: 使用Autogen执行MCP调用链")
    executor = AutogenMCPExecutor()
    
    diagnosis_result = await executor.execute_mcp_chain(mcp_chain, alert_info)
    
    # 5. 输出最终诊断报告
    print("\n步骤5: 最终诊断报告")
    print("=" * 60)
    
    final_diagnosis = diagnosis_result["final_diagnosis"]
    print(f"诊断结论: {final_diagnosis['diagnosis']}")
    print(f"置信度: {final_diagnosis['confidence_score']}%")
    print(f"关键证据: {', '.join(final_diagnosis['evidence'])}")
    print(f"建议措施: {final_diagnosis['recommendation']}")
    
    print(f"\n执行统计:")
    print(f"  总步骤数: {diagnosis_result['total_steps']}")
    print(f"  成功步骤: {diagnosis_result['successful_steps']}")
    print(f"  失败步骤: {diagnosis_result['total_steps'] - diagnosis_result['successful_steps']}")
    
    print(f"\n关键发现:")
    for finding in diagnosis_result["diagnostic_conclusions"][:5]:
        print(f"  • {finding}")
    
    # 6. 保存完整日志
    log_file = f"packet_drop_diagnosis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(log_file, 'w', encoding='utf-8') as f:
        json.dump(diagnosis_result, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n📝 完整诊断日志已保存到: {log_file}")
    
    return diagnosis_result

async def get_llm_generated_mcp_chain(alert_info: Dict[str, Any]) -> Dict[str, Any]:
    """使用LLM生成MCP调用链，完全动态化"""
    from datetime import datetime
    import json
    
    # 可用工具描述（与PacketDropHyperGraphRAG保持一致）
    available_tools = {
        "setup_api_connection": {
            "description": "建立与网络监控API的连接",
            "parameters": {"url": {"type": "string", "description": "API服务器URL，使用machine_ip"}},
            "estimated_duration": "2-5秒",
            "required": True
        },
        "query_statistics_table": {
            "description": "查询网络统计数据表",
            "parameters": {
                "table": {"type": "string", "options": ["service_access", "ip_flow", "tcp_flow", "summary"]},
                "begintime": {"type": "string", "description": "开始时间"},
                "endtime": {"type": "string", "description": "结束时间"},
                "fields": {"type": "array", "description": "查询字段，包括server_ip_addr、client_ip_addr、server_port、client_port等"},
                "keys": {"type": "array", "description": "分组字段"},
                "timeunit": {"type": "integer", "options": [0, 1000, 60000, 3600000]},
                "filter_condition": {"type": "string", "description": "过滤条件，使用server_ip_addr、client_ip_addr等字段"},
                "netlink": {"type": "integer", "description": "链路ID"}
            },
            "estimated_duration": "3-10秒"
        },
        "get_detailed_packet_decode": {
            "description": "获取详细的数据包解码信息", 
            "parameters": {
                "begin_time": {"type": "string", "description": "开始时间"},
                "end_time": {"type": "string", "description": "结束时间"},
                "server_ip": {"type": "string", "description": "服务器IP（使用server_ip_addr）"},
                "client_ip": {"type": "string", "description": "客户端IP（使用client_ip_addr）"},
                "server_port": {"type": "integer", "description": "服务器端口"},
                "client_port": {"type": "integer", "description": "客户端端口"},
                "protocol": {"type": "string", "default": "TCP"},
                "decode_options": {"type": "string", "options": ["basic", "detailed", "full"]}
            },
            "estimated_duration": "10-30秒"
        }
    }
    
    # 构建工具描述
    tools_desc = ""
    for tool_name, tool_info in available_tools.items():
        tools_desc += f"\n## {tool_name}\n描述: {tool_info['description']}\n预估耗时: {tool_info['estimated_duration']}\n"
    
    # 正确提取字段
    machine_ip = alert_info.get('machine_ip', '***************')
    server_ip_addr = alert_info.get('server_ip_addr', alert_info.get('server_ip', '*************'))
    client_ip_addr = alert_info.get('client_ip_addr', alert_info.get('client_ip', '*************'))
    server_port = alert_info.get('server_port', 80)
    client_port = alert_info.get('client_port', 10800)
    link_id = alert_info.get('link_id', 2)
    trigger_time = alert_info.get('trigger_time', '2025-06-16 14:00:00')
    
    # 构建LLM提示词
    prompt = f"""你是网络跳包诊断专家。根据警报信息，智能生成MCP工具调用链。

# 警报信息解析
```json
{json.dumps(alert_info, ensure_ascii=False, indent=2)}
```

# 字段含义说明
- machine_ip ({machine_ip}): 需要登录的机器IP，用于API连接
- server_ip_addr ({server_ip_addr}): 统计表中查询的服务器IP地址
- client_ip_addr ({client_ip_addr}): 统计表中查询的客户端IP地址
- server_port ({server_port}): 统计表中的服务器端口
- client_port ({client_port}): 统计表中的客户端端口
- link_id ({link_id}): 链路ID
- trigger_time ({trigger_time}): 警报触发时间

# 可用工具
{tools_desc}

# 任务目标
基于警报中的TCP重传率100%问题，设计智能诊断流程：
1. 首先建立API连接（使用machine_ip）
2. 查询会话统计，判断是短期会话(≤2秒)还是长期会话(>2秒)
3. 根据会话类型选择不同的诊断路径
4. 最终进行数据包解码确认

# 输出要求
输出标准JSON格式的MCP调用链，包含：
- meta: 元数据信息
- steps: 顺序执行的步骤
- branches: 条件分支（短期会话 vs 长期会话）

重要：
- API连接使用machine_ip: {machine_ip}
- 数据库查询使用server_ip_addr: {server_ip_addr}, client_ip_addr: {client_ip_addr}
- 过滤条件示例：server_ip_addr={server_ip_addr} AND client_ip_addr={client_ip_addr} AND server_port={server_port} AND client_port={client_port}
- 确保每个步骤都有明确的purpose、parameters和reasoning

请直接输出JSON格式的调用链："""

    try:
        # 使用openai_complete_if_cache函数
        from packet_drop_hypergraph_rag import openai_complete_if_cache
        
        llm_response = await openai_complete_if_cache(
            prompt=prompt,
            system_prompt="你是专业的网络诊断专家，生成准确的JSON格式MCP调用链。注意区分machine_ip(API连接)和server_ip_addr/client_ip_addr(数据库查询)。",
            model="deepseek-ai/DeepSeek-R1-0528",
            temperature=0.1
        )
        
        # 解析JSON响应
        if isinstance(llm_response, str):
            # 尝试提取JSON部分
            start_idx = llm_response.find('{')
            end_idx = llm_response.rfind('}') + 1
            if start_idx != -1 and end_idx != 0:
                json_str = llm_response[start_idx:end_idx]
                mcp_chain = json.loads(json_str)
            else:
                raise ValueError("无法找到有效的JSON内容")
        else:
            mcp_chain = llm_response
            
        # 验证和补全
        if "meta" not in mcp_chain:
            mcp_chain["meta"] = {}
        if "steps" not in mcp_chain:
            mcp_chain["steps"] = []
        if "branches" not in mcp_chain:
            mcp_chain["branches"] = {}
            
        # 补全必要字段
        meta = mcp_chain["meta"]
        meta["generated_at"] = datetime.now().isoformat()
        meta["alert_info"] = alert_info
        meta["total_steps"] = len(mcp_chain["steps"])
        meta["has_branches"] = len(mcp_chain["branches"]) > 0
        meta["total_branches"] = len(mcp_chain["branches"])
        
        return mcp_chain
        
    except Exception as e:
        print(f"LLM生成失败，使用最简备用方案: {e}")
        return get_minimal_fallback_chain(alert_info)

def get_minimal_fallback_chain(alert_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    获取最小化的备用MCP调用链，使用基于key的精确过滤
    
    Args:
        alert_info: 警报信息
        
    Returns:
        MCP调用链
    """
    # 提取关键信息
    machine_ip = alert_info.get('machine_ip', '***************')
    server_ip_addr = alert_info.get('server_ip_addr', '')
    client_ip_addr = alert_info.get('client_ip_addr', '')
    server_port = alert_info.get('server_port', '')
    client_port = alert_info.get('client_port', '')
    protocol = alert_info.get('protocol', 'TCP')
    duration = alert_info.get('duration', 0)
    
    # 构建TCP 4元组过滤条件
    tcp_4tuple_filter = ""
    if client_ip_addr and server_ip_addr:
        tcp_4tuple_filter = f"client_ip_addr={client_ip_addr}&&server_ip_addr={server_ip_addr}"
        if client_port:
            tcp_4tuple_filter += f"&&client_port={client_port}"
        if server_port:
            tcp_4tuple_filter += f"&&server_port={server_port}"
    
    # 服务器端口过滤条件
    server_port_filter = ""
    if server_ip_addr:
        server_port_filter = f"server_ip_addr={server_ip_addr}"
        if server_port:
            server_port_filter += f"&&server_port={server_port}"
    
    # IP流过滤条件
    ip_flow_filter = ""
    if client_ip_addr and server_ip_addr:
        ip_flow_filter = f"ip_endpoint1={client_ip_addr}&&ip_endpoint2={server_ip_addr}"
    
    # 确定会话类型和分析策略
    session_type = "short_session" if duration <= 2 else "long_session"
    analysis_focus = "connection_establishment" if duration <= 2 else "ongoing_performance"
    
    mcp_chain = {
        "meta": {
            "generated_at": datetime.now().isoformat(),
            "session_type": session_type,
            "analysis_focus": analysis_focus,
            "key_filtering_strategy": "tcp_4tuple_primary",
            "fallback_mode": True,
            "total_steps": 4,
            "has_branches": True
        },
        "steps": [
            {
                "step_id": 1,
                "tool": "setup_api_connection",
                "description": "建立与科来设备的API连接",
                "parameters": {
                    "machine_ip": machine_ip,
                    "username": "admin",
                    "password": "default",
                    "netlink_id": 2
                },
                "expected_outcome": "成功建立API连接，获取会话令牌",
                "required": True
            },
            {
                "step_id": 2,
                "tool": "query_statistics_table", 
                "description": "查询TCP会话详细信息（使用4元组精确过滤）",
                "parameters": {
                    "table_name": "tcp_flow",
                    "fields": [
                        "client_ip_addr", "server_ip_addr", "client_port", "server_port",
                        "total_byte", "total_packet", "flow_duration", "tcp_status",
                        "client_tcp_retransmission_packet", "server_tcp_retransmission_packet",
                        "connection_rst", "connection_noresponse", "establish_rtt"
                    ],
                    "filters": tcp_4tuple_filter,
                    "sort_field": "total_byte",
                    "limit": 50
                },
                "expected_outcome": "获取指定TCP会话的详细统计信息",
                "conditional_next": {
                    "condition": "tcp_flow_found",
                    "branches": ["detailed_analysis", "fallback_analysis"]
                }
            },
            {
                "step_id": 3,
                "tool": "query_statistics_table",
                "description": "查询服务器端口统计（基于server_ip+port过滤）",
                "parameters": {
                    "table_name": "tcp_server_port",
                    "fields": [
                        "server_ip_addr", "server_port", "total_byte", "total_packet",
                        "visit_count", "tcp_syn_packet", "tcp_rst_packet"
                    ],
                    "filters": server_port_filter,
                    "sort_field": "total_byte", 
                    "limit": 20
                },
                "expected_outcome": "获取服务器端口的整体统计信息"
            },
            {
                "step_id": 4,
                "tool": "query_statistics_table",
                "description": "查询IP流统计（双向流量分析）",
                "parameters": {
                    "table_name": "ip_flow", 
                    "fields": [
                        "ip_endpoint1", "ip_endpoint2", "total_byte", "total_packet",
                        "tcp_retransmission_packet", "tcp_segment_lost_packet",
                        "connection_rst", "connection_noresponse"
                    ],
                    "filters": ip_flow_filter,
                    "sort_field": "total_byte",
                    "limit": 30
                },
                "expected_outcome": "获取IP层面的流量统计和异常指标"
            }
        ],
        "branches": {
            "detailed_analysis": {
                "condition": "tcp_flow_data_available",
                "description": "基于TCP流数据进行详细分析",
                "steps": [
                    {
                        "step_id": "4A",
                        "tool": "get_detailed_packet_decode",
                        "description": "获取数据包详细解码（针对异常连接）",
                        "parameters": {
                            "packet_filters": f"tcp and host {client_ip_addr} and host {server_ip_addr}",
                            "decode_level": "detailed",
                            "time_range": "problem_window"
                        },
                        "expected_outcome": "获取异常数据包的协议层详细信息"
                    }
                ]
            },
            "fallback_analysis": {
                "condition": "tcp_flow_data_limited",
                "description": "当TCP流数据不足时的备用分析",
                "steps": [
                    {
                        "step_id": "4B",
                        "tool": "query_statistics_table",
                        "description": "查询客户端IP统计（范围扩大）",
                        "parameters": {
                            "table_name": "internal_ip_addr",
                            "fields": [
                                "ip_addr", "total_byte", "total_packet",
                                "connection_rst", "connection_noresponse"
                            ],
                            "filters": f"ip_addr={client_ip_addr}",
                            "sort_field": "total_byte",
                            "limit": 10
                        },
                        "expected_outcome": "获取客户端IP的整体网络行为"
                    }
                ]
            }
        }
    }
    
    return mcp_chain

# 为了兼容性，保留原函数名但指向新的LLM版本
async def get_fallback_mcp_chain(alert_info: Dict[str, Any]) -> Dict[str, Any]:
    """生成MCP调用链（现在使用LLM智能生成）"""
    return await get_llm_generated_mcp_chain(alert_info)

if __name__ == "__main__":
    # 运行完整的诊断流程
    asyncio.run(run_complete_packet_drop_diagnosis()) 