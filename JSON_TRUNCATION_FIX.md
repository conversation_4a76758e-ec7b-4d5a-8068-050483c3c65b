# MCP ReAct JSON截断问题修复

## 问题描述

在MCP ReAct代理运行过程中，LLM在生成工具调用JSON时可能因为token限制或其他原因被截断，导致：

1. JSON未完整闭合（缺少`}`）
2. 流式解析器无法识别完整的action
3. 代理将不完整的思考内容作为最终答案返回

### 问题示例

```
[LLM] Final thought:  用户要求查询链路1在2025年6月22日至2025年6月23日的概要数据，我可以直接使用`query_statistics_table`工具来获取这些信息。

```json
{
    "table": "summary",
    "begintime": "2025-06-22 00:00:00",
    
[INFO] No action detected, using thought as final answer
```

## 修复方案

### 1. 智能JSON补全（output_parser/cot_output_parser.py）

在流式处理结束时检测未闭合的JSON缓存，自动补全缺失的闭合括号：

```python
# 处理截断的JSON - 如果存在未闭合的JSON，尝试补全并解析
if in_json and json_cache and not got_json:
    print(f"[PARSER] Detected incomplete JSON, attempting to parse: {json_cache}")
    # 尝试补全JSON闭合括号
    incomplete_json = json_cache.strip()
    if incomplete_json.endswith(','):
        incomplete_json = incomplete_json[:-1]  # 移除末尾逗号
        
    # 计算需要补全的闭合括号数量
    open_braces = incomplete_json.count('{')
    close_braces = incomplete_json.count('}')
    needed_braces = open_braces - close_braces
    
    if needed_braces > 0:
        completed_json = incomplete_json + '}' * needed_braces
        print(f"[PARSER] Attempting to parse completed JSON: {completed_json}")
        yield parse_action(completed_json)
```

### 2. 预防性Token配置（strategies/mcpReAct.py）

确保LLM有足够的token来完成工具调用：

```python
# 为了避免JSON截断，确保max_tokens足够大
if not model_config.completion_params:
    model_config.completion_params = {}

original_max_tokens = model_config.completion_params.get('max_tokens')
if not original_max_tokens or original_max_tokens < 2000:
    model_config.completion_params['max_tokens'] = 2000
    print(f"[LLM] Adjusted max_tokens to 2000 to prevent JSON truncation")
```

### 3. 智能重试机制（strategies/mcpReAct.py）

检测到疑似JSON截断时自动重试：

```python
# 检查是否是由于JSON截断导致的问题
thought_content = scratchpad.thought.strip()
if (thought_content.startswith('{') and not thought_content.endswith('}') and 
    iteration_step < max_iteration_steps):
    print(f"[WARNING] Detected potential JSON truncation in thought: {thought_content[:100]}...")
    print(f"[INFO] Will retry in next iteration to get complete response")
    run_agent_state = True  # 继续下一轮迭代
    
    # 添加提示信息到agent_scratchpad，提示模型完成之前的工具调用
    incomplete_scratchpad = AgentScratchpadUnit(
        agent_response="",
        thought=f"我注意到上一个回复被截断了。让我重新完成查询链路概要数据的工具调用。",
        action_str="",
        observation="",
        action=None,
    )
    agent_scratchpad.append(incomplete_scratchpad)
```

## 测试验证

运行测试脚本验证修复效果：

```bash
python test_json_truncation_fix.py
```

## 修复效果

1. **自动恢复**：能够从截断的JSON中恢复工具调用
2. **预防截断**：通过合理的token配置减少截断发生
3. **智能重试**：检测到截断时自动重试完成任务
4. **透明调试**：提供详细的日志输出便于诊断

## 适用场景

- MCP ReAct代理工具调用被截断
- 流式响应中JSON格式不完整
- Token限制导致的响应截断
- 需要提高代理鲁棒性的场景

## 注意事项

1. JSON补全可能无法处理复杂的嵌套结构截断
2. 重试机制会增加执行时间和token消耗
3. 建议根据具体模型和用例调整max_tokens值
4. 测试时请使用真实的MCP工具环境验证效果 