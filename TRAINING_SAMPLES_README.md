# 网络分析AI训练样例说明

本项目包含两个专业的网络分析AI训练样例，旨在教会AI助手如何使用科来网络回溯系统进行深度网络分析。

## 训练样例概览

### 1. IP流量分析样例 (`network_analysis_training_data.json`)

**专业领域**: 网络流量统计与IP会话分析  
**核心技能**: 基础网络流量查询和分析

**主要特点**:
- 使用 `ip_flow` 统计表进行IP会话查询
- 基础的TOP N分析方法
- 流量规模和会话持续时间分析
- 适合网络流量概览和基础监控场景

**技术要点**:
- 表名: `ip_flow`
- 关键字段: `ip_endpoint1`, `ip_endpoint2`, `total_byte`, `total_packet`, `total_bitps`
- 查询模式: 简单排序查询，按流量大小排序
- 分析重点: 流量规模、数据包数量、会话持续时间

---

### 2. TCP会话性能分析样例 (`tcp_session_analysis_training_data.json`)

**专业领域**: TCP性能诊断与网络质量分析  
**核心技能**: 深度TCP性能问题诊断

**主要特点**:
- 使用 `tcp_flow` 统计表进行TCP会话性能分析
- 复合过滤条件识别性能问题会话
- 基线对比分析方法
- 专业的性能诊断报告生成

**技术要点**:
- 表名: `tcp_flow`
- 关键字段: 
  - 重传指标: `client_tcp_retransmission_rate`, `server_tcp_retransmission_rate`
  - 响应时间: `tcp_transaction_avg_rtt`, `establish_rtt`
  - 连接质量: `tcp_connect_failure_rate`, `tcp_segment_lost_packet_rate`
- 查询模式: 复合过滤 + 基线对比分析
- 分析重点: TCP性能问题诊断、质量评估、优化建议

## 核心技术差异对比

| 维度 | IP流量分析 | TCP性能分析 |
|------|------------|-------------|
| **复杂度** | 入门级 | 专家级 |
| **查询策略** | 单一排序查询 | 复合过滤 + 对比分析 |
| **过滤条件** | 无特殊过滤 | 性能阈值过滤 |
| **分析深度** | 流量统计 | 性能诊断 |
| **输出风格** | 数据展示 | 专业报告 |
| **应用场景** | 日常监控 | 故障诊断 |

## MCP工具使用模式

### 基础查询模式 (IP流量分析)
```python
mcp_qq2_query_statistics_table(
    table="ip_flow",
    begintime="2025-06-17 00:00:00",
    endtime="2025-06-18 00:00:00",
    fields=["ip_endpoint1", "ip_endpoint2", "total_byte", "total_packet"],
    keys=["ip_endpoint1", "ip_endpoint2"],
    timeunit=0,
    filter_condition="",  # 无过滤条件
    topcount=10,
    sortfield="total_byte",
    sorttype=2
)
```

### 高级诊断模式 (TCP性能分析)
```python
# 第一步：查询问题会话
mcp_qq2_query_statistics_table(
    table="tcp_flow",
    fields=[...复合性能指标...],
    filter_condition="client_tcp_retransmission_rate>0.05 OR server_tcp_retransmission_rate>0.05 OR tcp_transaction_avg_rtt>100000",
    sortfield="client_tcp_retransmission_rate"
)

# 第二步：查询基线数据用于对比
mcp_qq2_query_statistics_table(
    table="tcp_flow",
    fields=[...基线指标...],
    filter_condition="",  # 无过滤，获取总体基线
    topcount=1
)
```

## 分析报告特色

### IP流量分析报告特点
- 📊 清晰的数据展示
- 📈 流量规模量化
- ⏱️ 会话时间分析
- 🔍 协议类型识别
- 💡 基础优化建议

### TCP性能分析报告特点
- 📊 基线对比分析
- 🔴 性能问题标记 (红色=严重, 黄色=警告)
- 📈 性能倍数比较
- 🎯 精确问题定位
- 🔧 专业优化建议
- 📋 结构化诊断报告

## 训练目标

通过这两个样例的训练，AI助手将掌握：

1. **基础技能** (IP流量分析)
   - 科来MCP API的基本使用
   - 统计表查询语法
   - 基础网络分析思路

2. **高级技能** (TCP性能分析)
   - 复合查询条件构建
   - 多步骤分析流程
   - 基线对比分析方法
   - 专业诊断报告生成

3. **通用能力**
   - 网络问题识别
   - 数据解读与分析
   - 专业建议提供
   - 用户交互优化

## 使用建议

1. **训练顺序**: 建议先使用IP流量分析样例进行基础训练，再使用TCP性能分析样例进行高级训练
2. **场景扩展**: 可基于这两个样例的模式，创建更多针对UDP、应用层协议等的专业分析样例
3. **持续优化**: 根据实际使用反馈，不断完善分析逻辑和报告格式

---

这两个训练样例代表了从基础到高级的网络分析能力培养路径，为AI助手提供了全面的网络分析技能训练基础。 