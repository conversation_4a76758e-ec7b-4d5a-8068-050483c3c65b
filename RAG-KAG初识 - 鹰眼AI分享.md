# RAG/KAG初识 - 鹰眼 AI分享 - 2025-06-05

> 本文旨在分享LLM基础知识，教你学会RAG/KAG核心概念和基本应用
>
> 30% 原理 + 70% 理解

## 前言

本次分享将从零开始介绍RAG/KAG技术的核心概念，帮助大家理解这些技术的基本原理和应用场景。内容适合初学者，重点在于概念理解和基础应用。

**分享内容包括：**
- LLM基础知识
- 提示词工程入门
- RAG技术原理
- KAG知识增强技术
- MCP协议基础
- Agent概念介绍
- 实际应用场景

## 什么是LLM？

> LLM是什么，LLM能做什么？

### LLM基本概念

**大语言模型（Large Language Model，LLM）** 是一种基于深度学习的人工智能模型，通过在大量文本数据上进行训练，能够理解和生成人类语言。

### 为什么大模型性能更强？

小模型vs大模型的差异：

| 对比维度 | 小模型 | 大模型 |
|---------|--------|--------|
| **参数量** | 数百万-数十亿 | 数百亿-数千亿 |
| **训练数据** | 相对较少 | 海量文本数据 |
| **语言理解** | 基础理解 | 深度理解上下文 |
| **推理能力** | 简单推理 | 复杂逻辑推理 |
| **知识覆盖** | 有限领域 | 广泛知识体系 |

**大模型的核心优势：**
- 更强的语义理解能力
- 更好的上下文关联
- 更准确的知识推理
- 更灵活的任务适应

Qwen3 模型内部
https://github.com/datawhalechina/self-llm/blob/master/models/Qwen3/01-Qwen3-%E6%A8%A1%E5%9E%8B%E7%BB%93%E6%9E%84%E8%A7%A3%E6%9E%90-Blog.md

![Qwen3 模型内部](https://github.com/datawhalechina/self-llm/raw/master/models/Qwen3/images/01-01.png)

### LLM的主要能力

1. **文本生成**：根据提示生成连贯的文本
2. **语言理解**：理解复杂的语言表达
3. **知识问答**：基于训练知识回答问题
4. **代码生成**：生成各种编程语言代码
5. **文本分析**：情感分析、摘要提取等
6. **翻译转换**：多语言翻译和格式转换

## 什么是提示词？

> 提示词是什么，如何设计有效的提示词？

### 提示词基础

**提示词（Prompt）** 是用户向LLM提供的指令或问题，用于引导模型生成特定类型的回答。

### 提示词的作用

- **任务指导**：告诉LLM要做什么
- **格式约束**：限制输出的格式和结构
- **上下文提供**：给出必要的背景信息
- **行为控制**：控制LLM的回答风格和内容

### 提示词设计原则

1. **明确性**：指令要清晰明确
2. **具体性**：避免模糊的描述
3. **结构化**：使用清晰的格式
4. **示例化**：提供具体的例子

### 提示词设计示例

**基础提示词：**
```
请帮我写一个邮件
```

**优化后的提示词：**
```
请帮我写一封商务邮件，内容如下：
- 收件人：客户张总
- 目的：邀请参加产品发布会
- 时间：2025年6月15日下午2点
- 地点：公司会议室
- 语气：正式礼貌
- 字数：200字以内
```

## 什么是RAG？

> RAG技术如何解决LLM的知识局限？

### RAG基本概念

**RAG（Retrieval-Augmented Generation，检索增强生成）** 是一种结合了信息检索和文本生成的AI架构。

### 为什么需要RAG？

**LLM的限制：**
- 训练数据有截止时间，无法获取最新信息
- 无法访问专业或私有知识库
- 对于特定领域的深度知识有限
- 可能产生"幻觉"（编造不存在的信息）

**RAG的解决方案：**
- 实时检索最新信息
- 接入专业知识库
- 提供可靠的信息来源
- 减少幻觉问题

### RAG工作原理

```
用户问题
    ↓
1. 问题理解
    ↓
2. 知识检索 → 知识库（文档、数据库等）
    ↓
3. 相关信息
    ↓
4. 上下文构建（问题 + 检索到的信息）
    ↓
5. LLM生成回答
    ↓
最终答案
```

### RAG的核心步骤

1. **文档预处理**
   - 文档收集和清理
   - 文本分块（Chunking）
   - 向量化（Embedding）

2. **检索阶段**
   - 用户查询向量化
   - 相似度计算
   - 相关文档片段检索

3. **生成阶段**
   - 构建增强提示词
   - LLM生成回答
   - 结果后处理

### RAG应用场景

- **智能客服**：基于企业知识库的问答
- **文档助手**：帮助查找和理解大量文档
- **学习助手**：基于教材和资料的学习辅导
- **法律咨询**：基于法律条文的智能咨询

## 什么是KAG？

> KAG如何进一步增强LLM的推理能力？

### KAG基本概念

**KAG（Knowledge-Augmented Generation，知识增强生成）** 是RAG的进阶版本，它不仅使用文档检索，还引入了结构化的知识图谱。

### RAG vs KAG

| 维度 | RAG | KAG |
|------|-----|-----|
| **数据结构** | 非结构化文本 | 结构化知识图谱 |
| **关系处理** | 文本相似度 | 实体关系推理 |
| **检索方式** | 向量相似度 | 图遍历+向量检索 |
| **推理能力** | 基于文本上下文 | 基于知识关系 |

### 知识图谱基础

**知识图谱**是一种结构化的知识表示方法：
- **实体（Entity）**：知识图谱中的节点，如"苹果公司"、"iPhone"
- **关系（Relation）**：实体间的连接，如"生产"、"位于"
- **属性（Attribute）**：实体的特征，如"成立时间"、"CEO"

**例子：**
```
苹果公司 --[生产]--> iPhone
苹果公司 --[位于]--> 美国加州
iPhone --[发布时间]--> 2007年
```

### 常见的KAG技术

1. **GraphRAG**：微软开源的图RAG技术
2. **LightRAG**：轻量级的图RAG实现
3. **HyperGraphRAG**：支持超图关系的高级RAG

### KAG的优势

- **更准确的关系理解**：能理解实体间的复杂关系
- **更好的推理能力**：基于知识图谱进行逻辑推理
- **更丰富的上下文**：提供结构化的背景知识
- **更可解释的结果**：能追溯推理路径

## 什么是MCP？

> MCP如何让LLM连接外部工具？

### MCP基本概念

**MCP（Model Context Protocol）** 是一个标准化协议，用于LLM与外部工具和数据源的交互。

### 为什么需要MCP？

**LLM的局限：**
- 无法执行实际操作（如发邮件、查数据库）
- 无法获取实时信息
- 无法与外部系统交互

**MCP的价值：**
- 标准化的工具接口
- 安全的外部系统访问
- 丰富的功能扩展
- 统一的错误处理

基于MINICPM4的MCP实现以及如何训练
https://github.com/OpenBMB/MiniCPM/blob/main/demo/minicpm4/MCP/README.md


### 评估结果



| MCP 服务器                          |              | gpt-4o       |            |              | qwen3        |            |              | minicpm4     |            |
| ----------------------------------- | ------------ | ------------ | ---------- | ------------ | ------------ | ---------- | ------------ | ------------ | ---------- |
|                                     | 函数名正确率 | 参数名正确率 | 数值正确率 | 函数名正确率 | 参数名正确率 | 数值正确率 | 函数名正确率 | 参数名正确率 | 数值正确率 |
| Airbnb                              | 89.3         | 67.9         | 53.6       | 92.8         | 60.7         | 50.0       | 96.4         | 67.9         | 50.0       |
| Amap-Maps 高德地图                  | 79.8         | 77.5         | 50.0       | 74.4         | 72.0         | 41.0       | 89.3         | 85.7         | 39.9       |
| Arxiv-MCP-Server Arxiv-MCP-服务器   | 85.7         | 85.7         | 85.7       | 81.8         | 54.5         | 50.0       | 57.1         | 57.1         | 52.4       |
| Calculator 计算器                   | 100.0        | 100.0        | 20.0       | 80.0         | 80.0         | 13.3       | 100.0        | 100.0        | 6.67       |
| Computor-Control-MCP 计算机控制 MCP | 90.0         | 90.0         | 90.0       | 90.0         | 90.0         | 90.0       | 90.0         | 90.0         | 86.7       |
| Desktop-Commander 桌面指挥官        | 100.0        | 100.0        | 100.0      | 100.0        | 100.0        | 100.0      | 100.0        | 100.0        | 100.0      |
| Filesystem 文件系统                 | 63.5         | 63.5         | 31.3       | 69.7         | 69.7         | 26.0       | 83.3         | 83.3         | 42.7       |
| Github                              | 92.0         | 80.0         | 58.0       | 80.5         | 50.0         | 27.7       | 62.8         | 25.7         | 17.1       |
| Gaode 高德                          | 71.1         | 55.6         | 17.8       | 68.8         | 46.6         | 24.4       | 68.9         | 46.7         | 15.6       |
| MCP-Code-Executor MCP 代码执行器    | 85.0         | 80.0         | 70.0       | 80.0         | 80.0         | 70.0       | 90.0         | 90.0         | 65.0       |
| MCP-Docx                            | 95.8         | 86.7         | 67.1       | 94.9         | 81.6         | 60.1       | 95.1         | 86.6         | 76.1       |
| PPT                                 | 72.6         | 49.8         | 40.9       | 85.9         | 50.7         | 37.5       | 91.2         | 72.1         | 56.7       |
| PPTx                                | 64.2         | 53.7         | 13.4       | 91.0         | 68.6         | 20.9       | 91.0         | 58.2         | 26.9       |
| Simple-Time-Server 简单时间服务器   | 90.0         | 70.0         | 70.0       | 90.0         | 90.0         | 90.0       | 90.0         | 60.0         | 60.0       |
| Slack 松弛                          | 100.0        | 90.0         | 70.0       | 100.0        | 100.0        | 65.0       | 100.0        | 100.0        | 100.0      |
| Whisper 耳语                        | 90.0         | 90.0         | 90.0       | 90.0         | 90.0         | 90.0       | 90.0         | 90.0         | 30.0       |
| **平均值**                          | **80.2**     | **70.2**     | **49.1**   | **83.5**     | **67.7**     | **43.8**   | **88.3**     | **76.1**     | **51.2**   |

### MCP工作原理

```
LLM → MCP客户端 → MCP服务器 → 外部工具/服务
                    ↓
               执行结果
                    ↓
LLM ← MCP客户端 ← MCP服务器
```

### MCP应用例子

**天气查询工具：**
```python
@mcp.tool()
def get_weather(city: str) -> str:
    """获取指定城市的天气信息"""
    # 调用天气API
    weather_data = weather_api.get(city)
    return f"{city}今天{weather_data.condition}，温度{weather_data.temperature}°C"
```

**使用场景：**
```
用户：今天北京天气怎么样？
LLM：我来为您查询北京的天气信息...
MCP：调用get_weather("北京")
工具：返回"北京今天晴，温度25°C"
LLM：根据查询结果，北京今天天气晴朗，温度25°C。
```

## 什么是Agent？

> Agent如何让AI具备自主决策能力？

### Agent基本概念

**Agent（智能体）** 是具有自主决策能力的AI系统，能够：
- 感知环境
- 制定计划
- 执行行动
- 学习改进

### Agent的核心特征

1. **自主性**：能独立完成任务
2. **目标导向**：朝着特定目标工作
3. **适应性**：能根据环境变化调整策略
4. **社交性**：能与其他Agent或人类协作

### Agent工作流程

```
感知环境 → 分析情况 → 制定计划 → 执行行动 → 评估结果
    ↑                                            ↓
    ←──────────── 学习和调整 ←─────────────────────┘
```

### Agent类型

1. **反应式Agent**：根据当前感知直接行动
2. **目标式Agent**：朝着特定目标规划行动
3. **效用式Agent**：优化整体效用
4. **学习式Agent**：从经验中学习改进

### Agent应用场景

- **智能客服**：自动处理客户咨询
- **自动化运维**：监控和处理系统问题
- **智能助理**：帮助完成日常任务
- **游戏AI**：在游戏中做出智能决策

## 技术对比总结

| 技术 | 主要功能 | 适用场景 | 复杂度 |
|------|----------|----------|---------|
| **LLM** | 语言理解和生成 | 文本处理、对话 | 基础 |
| **RAG** | 检索增强生成 | 知识问答、文档助手 | 中等 |
| **KAG** | 知识图谱增强 | 复杂推理、关系分析 | 较高 |
| **MCP** | 工具集成 | 系统集成、功能扩展 | 中等 |
| **Agent** | 自主决策 | 智能助理、自动化 | 高 |

## 入门学习路径

### 第一阶段：基础理解

#### 1. 熟悉LLM基本概念和能力

**核心概念深入理解：**

**什么是Token？**
- Token是LLM处理文本的最小单位
- 中文：一个汉字通常是1个token
- 英文：一个单词可能是1-3个token
- 特殊符号和标点符号也是token

**示例：**
```
文本: "你好世界"
Token数量: 4个 ["你", "好", "世", "界"]

文本: "Hello World"  
Token数量: 2个 ["Hello", " World"]
```

**LLM的输入输出限制：**
- **上下文窗口**：LLM一次能处理的最大token数
- **常见模型上下文长度**：
  - GPT-3.5: 4,096 tokens
  - GPT-4: 8,192 tokens  
  - Claude-3: 200,000 tokens
  - 国产模型：通常8k-32k tokens

**LLM能力边界理解：**

✅ **LLM擅长的任务：**
- 文本理解和生成
- 语言翻译
- 代码编写和解释
- 逻辑推理（基于训练数据）
- 创意写作
- 数据格式转换

❌ **LLM不擅长的任务：**
- 精确的数学计算
- 实时信息获取
- 文件操作
- 网络请求
- 记忆长期对话历史

**实践练习：**
```python
# 尝试不同类型的提示，观察LLM的回答
prompts = [
    "1234 + 5678 = ?",  # 数学计算
    "今天的天气如何？",  # 实时信息
    "帮我写一个Python函数计算斐波那契数列",  # 代码生成
    "解释一下什么是递归",  # 概念解释
]

# 观察哪些任务LLM能很好完成，哪些不能
```

#### 2. 学习提示词工程技巧

**提示词工程的核心原则：**

**原则一：明确具体**
```
❌ 不好的提示：
"帮我写个程序"

✅ 好的提示：
"请用Python写一个程序，实现以下功能：
1. 读取CSV文件
2. 计算每列的平均值
3. 将结果保存为新的CSV文件
4. 添加适当的错误处理"
```

**原则二：提供上下文**
```
❌ 缺少上下文：
"这个问题怎么解决？"

✅ 丰富的上下文：
"我是Python初学者，在使用pandas读取CSV文件时遇到编码错误：
UnicodeDecodeError: 'utf-8' codec can't decode byte...
我的代码是：pd.read_csv('data.csv')
请问如何解决这个问题？"
```

**原则三：角色扮演**
```
示例：
"你是一个有10年经验的Python开发工程师，请以专业且易懂的方式，
为初学者解释Python中的装饰器概念，并提供3个实用的例子。"
```

**原则四：思维链（Chain of Thought）**
```
示例：
"请一步步分析这个问题：
一个班级有30个学生，其中60%是女生，40%是男生。
如果5个女生和3个男生请假，剩下的学生中女生占多少比例？

请按以下步骤分析：
1. 计算原始男女生人数
2. 计算请假后的男女生人数  
3. 计算剩余学生中女生比例
4. 验证计算结果"
```

**高级提示词技巧：**

**Few-Shot Learning（少样本学习）：**
```
"请按照以下格式将句子改写为正式语言：

示例1：
输入：这个东西超级好用
输出：此产品具有优异的使用体验

示例2：  
输入：老板人挺不错的
输出：领导具有良好的管理风格

现在请改写：这个方案感觉有点问题"
```

**结构化输出：**
```
"请分析以下代码并按JSON格式输出结果：
{
  "功能描述": "代码的主要功能",
  "输入参数": ["参数1", "参数2"],
  "输出结果": "返回值描述", 
  "时间复杂度": "O(...)",
  "潜在问题": ["问题1", "问题2"],
  "改进建议": ["建议1", "建议2"]
}

代码：
def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr"
```

**实践练习题：**
1. 设计一个提示词，让LLM帮你生成产品需求文档模板
2. 写一个提示词，让LLM扮演代码审查员，检查代码质量
3. 创建一个提示词，让LLM帮你生成技术文档的大纲

#### 3. 了解向量和Embedding概念

**什么是向量（Vector）？**

向量是数学中表示方向和大小的量，在AI中用数字数组表示文本的语义信息。

**文本向量化示例：**
```python
# 简单示例：词频向量化
词汇表 = ["苹果", "香蕉", "水果", "甜", "红色"]

句子1 = "苹果是红色的水果"
向量1 = [1, 0, 1, 0, 1]  # 对应词汇表中词的出现次数

句子2 = "香蕉是甜的水果"  
向量2 = [0, 1, 1, 1, 0]
```

**什么是Embedding？**

Embedding是深度学习模型学习到的高维向量表示，能够捕捉语义相似性。

**Embedding的特性：**
- **维度**：通常是几百到几千维（如768、1024、1536维）
- **语义相似性**：语义相近的文本向量距离更近
- **计算相似度**：使用余弦相似度等方法

**向量相似度计算：**
```python
import numpy as np

def cosine_similarity(vec1, vec2):
    """计算两个向量的余弦相似度"""
    dot_product = np.dot(vec1, vec2)
    norm1 = np.linalg.norm(vec1)
    norm2 = np.linalg.norm(vec2)
    return dot_product / (norm1 * norm2)

# 示例
vec_dog = [0.2, 0.8, 0.1, 0.9]    # "狗"的向量
vec_puppy = [0.3, 0.7, 0.2, 0.8]  # "小狗"的向量  
vec_car = [0.9, 0.1, 0.8, 0.2]    # "汽车"的向量

print(f"狗 vs 小狗: {cosine_similarity(vec_dog, vec_puppy):.3f}")  # 高相似度
print(f"狗 vs 汽车: {cosine_similarity(vec_dog, vec_car):.3f}")   # 低相似度
```

**常用的Embedding模型：**

1. **OpenAI Embeddings**
   - text-embedding-3-small: 1536维
   - text-embedding-3-large: 3072维

2. **开源模型**
   - sentence-transformers/all-MiniLM-L6-v2
   - BAAI/bge-large-zh-v1.5（中文）

3. **国产模型**
   - text2vec-base-chinese
   - m3e-base

**实际代码示例：**
```python
# 使用sentence-transformers生成embedding
from sentence_transformers import SentenceTransformer
import numpy as np

# 加载模型
model = SentenceTransformer('all-MiniLM-L6-v2')

# 生成embedding
sentences = [
    "我喜欢吃苹果",
    "苹果很好吃", 
    "今天天气很好",
    "我喜欢吃水果"
]

embeddings = model.encode(sentences)
print(f"向量维度: {embeddings.shape}")  # (4, 384)

# 计算相似度矩阵
from sklearn.metrics.pairwise import cosine_similarity
similarity_matrix = cosine_similarity(embeddings)
print("相似度矩阵:")
print(similarity_matrix)
```

**向量数据库基础：**

向量数据库专门用于存储和搜索高维向量数据：

**主要功能：**
- 高效的向量存储
- 快速的相似度搜索（ANN - Approximate Nearest Neighbor）
- 支持大规模数据

**简单的向量搜索实现：**
```python
import chromadb

# 创建Chroma客户端
client = chromadb.Client()
collection = client.create_collection("my_documents")

# 添加文档
documents = [
    "Python是一种编程语言",
    "机器学习是人工智能的一个分支",
    "深度学习使用神经网络",
    "自然语言处理处理文本数据"
]

# 添加到向量数据库
collection.add(
    documents=documents,
    ids=[f"doc_{i}" for i in range(len(documents))]
)

# 搜索相似文档
results = collection.query(
    query_texts=["什么是AI技术"],
    n_results=2
)

print("搜索结果:")
for doc in results['documents'][0]:
    print(f"- {doc}")
```

**实践作业：**

1. **基础练习**：使用不同的提示词让LLM生成同一主题的内容，比较效果差异

2. **向量实验**：
   - 下载sentence-transformers库
   - 生成5个相关句子的embedding
   - 计算它们之间的相似度
   - 观察语义相似的句子是否向量距离更近

3. **综合练习**：
   - 设计一个简单的文档问答系统流程
   - 明确每个步骤需要用到的技术（LLM、embedding、向量搜索）

**推荐学习资源：**
- **文档**：Hugging Face Transformers文档
- **教程**：sentence-transformers官方教程  
- **实践**：Google Colab中的向量搜索示例
- **工具**：OpenAI Playground进行提示词实验

### 第二阶段：RAG实践
1. 搭建简单的RAG系统
2. 实践文档分块和向量化
3. 尝试不同的检索策略

### 第三阶段：进阶应用
1. 学习知识图谱基础
2. 尝试KAG技术
3. 集成MCP工具

### 第四阶段：系统设计
1. 设计复杂的Agent系统
2. 优化系统性能
3. 处理实际业务场景

## 推荐工具和资源

### 开源框架
- **LangChain**：LLM应用开发框架
- **LlamaIndex**：数据连接和索引框架
- **Haystack**：端到端NLP框架

### 向量数据库
- **Chroma**：开源向量数据库
- **Pinecone**：云端向量数据库
- **Weaviate**：开源向量搜索引擎

### 学习资源
- **官方文档**：各框架的官方文档
- **GitHub示例**：开源项目和示例代码
- **技术博客**：相关技术文章和教程

一些Agent简单实现
https://github.com/Shubhamsaboo/awesome-llm-apps

提示词教程
https://github.com/anthropics/prompt-eng-interactive-tutorial

Agent国外学习课程
https://github.com/panaversity/learn-agentic-ai

开源大模型的微调和应用
https://github.com/datawhalechina/self-llm

系统性的 LLM 学习教程
https://github.com/datawhalechina/happy-llm
## 总结

RAG/KAG技术代表了AI应用的重要发展方向：
- **RAG**解决了LLM的知识时效性问题
- **KAG**增强了复杂推理能力
- **MCP**扩展了功能边界
- **Agent**实现了自主智能

这些技术相互补充，共同构建了现代AI应用的技术栈。掌握这些基础概念，是进入AI应用开发的重要基础。

---

*本分享为初学者提供了RAG/KAG技术的入门指南，更多深度实践请关注我们的进阶分享。* 