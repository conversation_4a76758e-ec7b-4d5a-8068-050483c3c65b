#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON截断修复的有效性
"""

import json
from output_parser.cot_output_parser import CotAgentOutputParser
from dify_plugin.entities.model.llm import LLMResultChunk, LLMResultChunkDelta, LLMResultChunkDeltaMessage
from typing import Generator

def mock_llm_response_with_truncation() -> Generator[LLMResultChunk, None, None]:
    """模拟被截断的LLM响应"""
    
    # 模拟正常开始
    chunks = [
        "用户要求查询链路1在2025年6月22日至2025年6月23日的概要数据，我可以直接使用`query_statistics_table`工具来获取这些信息。\n\n",
        
        # 开始JSON工具调用，但被截断
        '{\n',
        '    "table": "summary",\n',
        '    "begintime": "2025-06-22 00:00:00",\n',
        # 这里被截断了，没有后续内容
    ]
    
    for chunk_content in chunks:
        delta_message = LLMResultChunkDeltaMessage(content=chunk_content)
        delta = LLMResultChunkDelta(message=delta_message)
        chunk = LLMResultChunk(delta=delta)
        yield chunk

def mock_llm_response_complete() -> Generator[LLMResultChunk, None, None]:
    """模拟完整的LLM响应"""
    
    chunks = [
        "用户要求查询链路1在2025年6月22日至2025年6月23日的概要数据，我可以直接使用`query_statistics_table`工具来获取这些信息。\n\n",
        
        # 完整的JSON工具调用
        '{\n',
        '    "action": "query_statistics_table",\n',
        '    "action_input": {\n',
        '        "table": "summary",\n',
        '        "begintime": "2025-06-22 00:00:00",\n',
        '        "endtime": "2025-06-23 00:00:00",\n',
        '        "fields": ["time", "total_byte", "total_packet"],\n',
        '        "keys": ["time"],\n',
        '        "netlink": 1\n',
        '    }\n',
        '}'
    ]
    
    for chunk_content in chunks:
        delta_message = LLMResultChunkDeltaMessage(content=chunk_content)
        delta = LLMResultChunkDelta(message=delta_message)
        chunk = LLMResultChunk(delta=delta)
        yield chunk

def test_truncation_handling():
    """测试截断处理"""
    print("=" * 60)
    print("测试JSON截断处理")
    print("=" * 60)
    
    # 测试1: 截断的响应
    print("\n1. 测试截断的JSON响应:")
    usage_dict = {}
    truncated_chunks = CotAgentOutputParser.handle_react_stream_output(
        mock_llm_response_with_truncation(), usage_dict
    )
    
    result_parts = []
    actions_found = []
    
    for chunk in truncated_chunks:
        if hasattr(chunk, 'action_name'):
            actions_found.append(chunk)
            print(f"   发现Action: {chunk.action_name}")
            print(f"   Action参数: {chunk.action_input}")
        else:
            result_parts.append(str(chunk))
    
    print(f"   Text输出: {''.join(result_parts)}")
    print(f"   Actions数量: {len(actions_found)}")
    
    # 测试2: 完整的响应
    print("\n2. 测试完整的JSON响应:")
    usage_dict2 = {}
    complete_chunks = CotAgentOutputParser.handle_react_stream_output(
        mock_llm_response_complete(), usage_dict2
    )
    
    result_parts2 = []
    actions_found2 = []
    
    for chunk in complete_chunks:
        if hasattr(chunk, 'action_name'):
            actions_found2.append(chunk)
            print(f"   发现Action: {chunk.action_name}")
            print(f"   Action参数: {chunk.action_input}")
        else:
            result_parts2.append(str(chunk))
    
    print(f"   Text输出: {''.join(result_parts2)}")
    print(f"   Actions数量: {len(actions_found2)}")
    
    # 比较结果
    print("\n3. 比较结果:")
    print(f"   截断响应检测到的Actions: {len(actions_found)}")
    print(f"   完整响应检测到的Actions: {len(actions_found2)}")
    
    if len(actions_found) > 0:
        print("   ✅ 截断修复生效：成功从截断的JSON中恢复了工具调用")
    else:
        print("   ❌ 截断修复失败：未能从截断的JSON中恢复工具调用")

if __name__ == "__main__":
    test_truncation_handling() 