 

 

 

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml18864\wps9.png)***\*科来网络回溯分析系统\****

 

 

 

 

 

 

 

 

***\*数据接口说明\****

 

 

 

 

 

 

 

本文档所有内容均为科来独立完成，未经科来做出明确书面许可，不得为任何目的、以任 何形式或手段（包括电子、机械、复印、录音或其他形式）对本文档的任何部分进行复

制、修改、存储、引入检索系统或者传播。

 

 

 

 

©2024 科来 版权所有 保留所有权利

 

 

 

 

 

 

 

 

科来

电话：400-6869-069

网址：[http://www.colasoft.com.cn](http://www.colasoft.com.cn/) 邮箱：[<EMAIL>](mailto:<EMAIL>)



 

***\*目录\****

 

[***\*1\**** ***\*版本历史\**** ***\*1\****](#bookmark2)

[***\*2\**** ***\*系统说明\**** ***\*3\****](#bookmark4)

[***\*3\**** ***\*系统规则\**** ***\*4\****](#bookmark6)

[3.1 多线程 4](#bookmark8)

[3.2 JSON  4](#bookmark10)

[3.3 二进制数据 4](#bookmark12)

[3.4 错误码 5](#bookmark14)

[3.5 过滤器 6](#bookmark16)

[3.6 统计表 ID 定义 7](#bookmark18)

[3.7 统计表字段 ID 定义 9](#bookmark20)

[***\*4\**** ***\*身份验证与注销\**** ***\*10\****](#bookmark22)

[4.1 身份验证 10](#bookmark24)

[4.2 注销登录 12](#bookmark26)

[***\*5\**** ***\*基础信息获取\**** ***\*14\****](#bookmark28)

[5.1 接口版本信息  14](#bookmark30)

[5.2  网络协议信息  15](#bookmark32)

[5.3 链路时间 16](#bookmark34)

[***\*6\**** ***\*配置获取\**** ***\*19\****](#bookmark36)

[6.1 配置获取 19](#bookmark38)

[***\*7\**** ***\*配置设置\**** ***\*22\****](#bookmark40)

[7.1 配置格式说明  22](#bookmark42)

[7.2 配置接口说明  22](#bookmark44)

[7.3 接口配置 24](#bookmark46)

[7.3.1 管理接口  24](#bookmark48)

[7.3.2 采集接口  26](#bookmark50)

[7.4 虚接口配置 29](#bookmark52)

[7.4.1 VLAN 虚接口  30](#bookmark54)

[7.4.2 ISL VLAN 虚接口 33](#bookmark56)

[7.4.3 物理地址虚接口  36](#bookmark58)

[7.4.4  网段虚接口 39](#bookmark60)

[7.4.5 NETFLOW 虚接口 42](#bookmark62)



[7.4.6 MPLS VPN 虚接口  45](#bookmark64)

[7.4.7 VXLAN 虚接口 45](#bookmark66)

[7.4.8 GRE 虚接口 45](#bookmark68)

[7.4.9 GENEVE 虚接口 45](#bookmark70)

[7.4.10 删除虚接口配置  45](#bookmark72)

[7.5 链路配置 46](#bookmark74)

[7.5.1 新增链路  47](#bookmark76)

[7.5.2 更新链路  54](#bookmark78)

[7.5.3 删除链路  55](#bookmark80)

[7.6 名字表 56](#bookmark82)

[7.6.1 物理地址名字表  57](#bookmark84)

[7.6.2 IPv4 地址名字表  60](#bookmark86)

[7.6.3 IPv6 地址名字表  61](#bookmark88)

[7.6.4 VLAN 标识名字表 62](#bookmark90)

[7.6.5 MPLS VPN 标签名字表 64](#bookmark92)

[7.6.6 VXLAN 标识名字表  66](#bookmark94)

[7.6.7 ISL VLAN 标识名字表  67](#bookmark96)

[7.6.8 DSCP 标记名字表  68](#bookmark98)

[7.6.9 交易名称名字表  69](#bookmark100)

[7.6.10 接口标识名字表  70](#bookmark102)

[7.7 应用配置 71](#bookmark104)

[7.7.1 标准应用  72](#bookmark106)

[7.7.2 特征值应用 75](#bookmark108)

[7.7.3 WEB 应用 77](#bookmark110)

[7.7.4 加密应用  80](#bookmark112)

[7.7.5 协议应用  82](#bookmark114)

[7.8  网段配置 84](#bookmark116)

[7.8.1 进出网网段 84](#bookmark118)

[7.8.2  自定义网段 86](#bookmark120)

[7.9 子链路配置 88](#bookmark122)

[7.9.1 VLAN 子链路  89](#bookmark124)

[7.9.2 ISL VLAN 子链路 92](#bookmark126)

[7.9.3 VXLAN 子链路 93](#bookmark128)

[7.9.4 MPLS VPN 子链路  94](#bookmark130)

[7.9.5  网段子链路 96](#bookmark132)

[7.9.6 MAC 子链路 97](#bookmark134)

[7.9.7 Netflow 标识子链路  99](#bookmark136)



[7.9.8 GRE 子链路 100](#bookmark138)

[7.9.9 OPCODE 子链路 101](#bookmark140)

[7.10 安全策略  102](#bookmark142)

[***\*8\**** ***\*数据查询\**** ***\*107\****](#bookmark144)

[8.1 查询过滤器 107](#bookmark146)

[8.2 枚举统计表 107](#bookmark148)

[8.3 枚举统计表字段 109](#bookmark150)

[8.4 查询统计数据  112](#bookmark152)

[8.4.1 查询统计表名称  124](#bookmark154)

[8.4.2 查询统计表字段  124](#bookmark156)

[8.4.3 查询结果  124](#bookmark158)

[***\*9\**** ***\*下载\**** ***\*CSV\**** ***\*统计数据\****  ***\*127\****](#bookmark160)

[***\*10\**** ***\*下载数据包\**** ***\*129\****](#bookmark162)

[10.1 下载过滤器 129](#bookmark164)

[10.2 下载数据包 130](#bookmark166)

[***\*11\**** ***\*在线解码\**** ***\*133\****](#bookmark168)

[11.1 流解码接口 133](#bookmark170)

[11.2 数据包概要解码接口  135](#bookmark172)

[11.3 数据包详细解码接口  137](#bookmark174)



 

***\*1\**** ***\*版本历史\****

| ***\*变更时间\**** | ***\*变更原因\****                                           | ***\*修改人\**** | ***\*API\**** ***\*版本\**** | ***\*适用回溯版本\**** |
| ------------------ | ------------------------------------------------------------ | ---------------- | ---------------------------- | ---------------------- |
| 2015-3-3           | 新增                                                         | 满小春           | 0.1                          | 5.0.1                  |
| 2015-3-11          | 增加节“9. API 示例” 修正错误                                 | 满小春           | 0.1                          | 5.0.1                  |
| 2015-10-19         | 按照实现更正文档                                             | 满小春           | 0.1                          | 5.0.1                  |
| 2016-03-17         | 修改接口 BUG，更新接口文档                                   | 满小春           | 1.0                          | 5.1.1                  |
| 2016-04-22         | 完善 API 接口文档： 填充表 ip_flow 的字段说明； 完善警报配置说明；更新目录和格式 | 满小春           | 1.1                          | 5.1.1                  |
| 2016-9-7           | 增加配置类型的描述； 增加名字表配置；                        | 满小春           | 5.2                          | 5.2                    |
| 2017-8-4           | 将数据接口按 Restful API 规范作对 应调整。                   | 廖施硕           | 5.4.1                        | 5.4.1                  |
| 2017-8-11          | 新增 VOIP 表字段内容                                         | 廖施硕           | 5.4.1                        | 5.4.1                  |
| 2018-6-25          | 新增子链路配置, 新增 vlan_stat、vxlan_stat、mpls_vpn、netflow_stat 表字段内容 | 廖施硕           | 5.4.2                        | 5.4.2                  |
| 2018-8-8           | Summary ，application，application_group 等表更新字段        | 廖施硕           | 5.5.0                        | 5.5.0                  |
| 2018-11-21         | 所有表及相关字段描述改由工具获 取；登录接口新增加密参数描述。 | 廖施硕           | 5.5.2                        | 5.5.2                  |
| 2019-05-13         | 新增服务器时间获取接口；统计表及其字段 ID 与描述信息更新至 5.6.0.11679。 | 廖施硕           | 5.6.0                        | 5.6.0                  |
| 2019-11-19         | 数据查询支持格式化开关，开关开启 后将返回 json 格式的统计数据； | 廖施硕           | 5.6.2                        | 5.6.2                  |

 



 

| ***\*变更时间\**** | ***\*变更原因\****                           | ***\*修改人\**** | ***\*API\**** ***\*版本\**** | ***\*适用回溯版本\**** |
| ------------------ | -------------------------------------------- | ---------------- | ---------------------------- | ---------------------- |
|                    | 统计表及字段描述更新；                       |                  |                              |                        |
| 2020-05-07         | 统计表及字段 ID 与描述信息更新至 6.0.0.14461 | 廖施硕           | 6.0.0                        | 6.0.0                  |
| 2021-05-13         | 文档中错误信息更正                           | 周小强           | 6.2.1                        | 6.2.1                  |
| 2022-11-28         | 新增在线解码内容                             | 周小强           | 6.2.2                        | 6.2.2                  |
| 2023-5-11          | 文档中错误信息更正                           | 周小强           | 6.2.2                        | 6.2.2                  |
| 2023-10-7          | 新增接口配置                                 | 周小强           | 6.4.0                        | 6.4.0                  |
| 2023-12-25         | 更新警报类型枚举列表                         | 姜俊杰           | 6.4.0                        | 6.4.0                  |

 



 

***\*2\**** ***\*系统说明\****

该访问接口是基于 HTTPS 协议和Restful 规范的访问接口，该接口支持 POST 和 GET 两种形式的访问。

GET 方式是将参数附在请求的URL 后面，如：

https://yourserver:port/csras_api/{sessionId}/apiname/{param}

由于 GET 方式将参数附在 URL 后面，因此存在一些长度限制，建议 URL 长度最好 不要超过 2KB。

POST 方式则是将参数附在 HTTP 请求的数据部分中，参数无长度限制，通常使用 HTML 的 form 表单提交的数据则是通过该方式进行传输的，如果有不清楚的地方可 以参考 HTTP 协议。

https://yourserver:port/csras_api/{sessionId}/{apiname} param 通过 json 格式传输

另一方面数据访问接口基于 HTTP 协议，因此在每次请求返回的数据中都包含了

HTTP 响应头部，因此客户端需要对 HTTP 协议本身和回溯系统返回的数据进行区 分。回溯系统的所有数据都将包含在 HTTP 响应的数据部分，其意义由协议文档进 行解释。除此之外，客户端还需要注意 HTTP 响应中的状态码和会话 ID。系统的部 分错误会通过状态码返回。如不能识别的 URL(包括参数错误)将返回 404 文件未找 到；权限不足，将返回 403 访问禁止，而成功将返回 200。客户端的处理流程应该 是先检查状态码是否为 200 ，然后在进行协议的解析。

 

说明：

API 默认使用 8080 端口。



 

***\*3\**** ***\*系统规则\****

 

***\*3.1\**** ***\*多线程\****

 

回溯服务器接口同时最多支持处理 8 个请求，多余的请求会被阻塞。在进行大时间 范围的统计查询时，查询需要消耗一定的时间，所以这段时间内要保证有空闲的线 程用于处理其他的请求。

 

说明：

回溯服务器的统计查询机制是串行的，所以在统计查询时使用多线程并没有意义， 不建议使用多线程并行查询。

 

***\*3.2\**** ***\*JSON\****

 

JSON，是一种轻量级的数据交换格式，采用完全独立于语言的文本格式。接口都  采用 JSON 对象作为请求参数，如果接口返回的数据量不大，如配置、枚举表、枚 举表字段，返回结果也将是 JSON 对象格式。

JSON 作为请求参数时，参数对象均为param，例如接口身份验证参数： param={

"username":"admin",  "password":"123456", "encrypcode":0

}

（注：JSON 数据前需拼接上“param=”）

 

***\*3.3\**** ***\*二进制数据\****

 

有些接口返回的数据量庞大，采用 JSON 或其他文本格式传输成本很高，所以这些 接口的返回数据将是二进制数据，例如查询统计数据接口。二进制数据在传输过程 中遵循以下规则：

l 为了保证数据传输的统一性，在客户端和服务器端的通讯数据统一采用网络字 节序进行数据传输。

l 在二进制传输过程中的字符串格式，统一采用 UTF-8 编码。格式如下：



 

| 长度（ 4 字节） | UTF-8 编码后的内容（N 字节） |
| --------------- | ---------------------------- |
|                 |                              |

其中长度值中包括了结束符‘\0’，就是说，所有的 UTF-8 的字符串都是以‘\0’ 结束的。另外， 长度虽然是 4 个字节，但最大长度不要超过 32M 字节。

l 在传输过程中的二进制格式如下：

| 长度（ 4 字节） | N 字节数据 |
| --------------- | ---------- |
|                 |            |

在传输二进制数据，一般会带有字段描述，包含字段名称、字段类型和字段长度， 字段类型说明如下：

| ***\*字段类型\**** | ***\*类型描述\****                                           |
| ------------------ | ------------------------------------------------------------ |
| 1                  | UINT8 ， 1 字节的无符号整数                                  |
| 2                  | UINT16 ，2 字节的无符号整数                                  |
| 3                  | UINT32 ，4 字节的无符号整数                                  |
| 4                  | UINT64 ，8 字节的无符号整数                                  |
| 5                  | DOUBLE ，8 字节的浮点数                                      |
| 6                  | DATETIME ，8 字节的时间戳                                    |
| 7                  | TEXT，变长的文本字符串: length[4] + value[n]                 |
| 8                  | PERCENT，百分值，长度 4 字节，值范围为：0 - 10000，使用时 除 100 |
| 9                  | MAC ，MAC 地址，8 个字节                                     |
| 10                 | IPADDR，由 1 字节的 IP 版本(IP Version)和 n 字节的 IP 地址组 成：l IP Version = 4 ， 1 字节的 IP 版本和 4 字节的 IPV4 地址；l IP Version = 6 ， 1 字节的 IP 版本和 16 字节的 IPV6 地址；l IP Version = 0，只有 1 字节的 IP 版本（在警报日志中会出 现）； |
| 11                 | 交易内容，变长的请求或响应内容：length[4] + value[n]         |
| 12                 | 虚拟 ID,  如 vlan id ， 1 字节无符号整数                     |

 

 

***\*3.4\**** ***\*错误码\****

 

第三方每次调用接口时，可能获得正确或错误的错误码，开发者可以通过错误码信 息调试接口，排查错误。其取值和意义如下：

 

| ***\*错误码\**** | ***\*描述\**** |
| ---------------- | -------------- |
| 0                | 请求成功       |

 



 

| ***\*错误码\**** | ***\*描述\****             |
| ---------------- | -------------------------- |
| 1                | 资源错误                   |
| 2                | 创建线程失败               |
| 3                | 未知命令                   |
| 4                | 未实现                     |
| 5                | 需要登录                   |
| 6                | 没有权限                   |
| 7                | 太多请求                   |
| 8                | 用户取消                   |
| 9                | 参数错误                   |
| 10               | 连接已断开                 |
| 11               | 请求超时                   |
| 12               | 参数错误：XX 获取失败      |
| 13               | 参数错误：查询字段不能解析 |
| 14               | 参数错误：无效的查询表名称 |
| 15               | 参数错误：无效的链路 ID    |
| 16               | 参数错误：无效的查询时间   |
| 17               | 参数错误：无效的时间单位   |
| 18               | 参数错误：无效的排序类型   |
| 19               | 参数错误：无效的时间周期   |
| 20               | 参数错误：无效的配置类型   |
| 21               | 查询表不存在               |
| 22               | 查询字段不存在             |
| 23               | 请求失败                   |
| 24               | 系统繁忙                   |
| 25               | 错误的 URL                 |
| 26               | 参数错误                   |
| 255              | 其他错误                   |

 

 

***\*3.5\**** ***\*过滤器\****

 

过滤器分为查询过滤器和下载过滤器。顾名思义，查询过滤器在查询时过滤数据结 果使用，而下载过滤器在下载数据包过滤时使用。

无论查询过滤器还是下载过滤器，都是以字符串的逻辑表达式组成，目前查询过滤



器和下载过滤器支持的过滤对象不同。

 

***\*3.6\**** ***\*统计表\**** ***\*ID\**** ***\*定义\****

统计表 ID 都是字符串，可以通过[“8.2 枚举统计表”](#bookmark175)获取。

统计表字段定义如下：

 

| ***\*表\**** ***\*ID\**** | ***\*描述\****    |
| ------------------------- | ----------------- |
| mssummary                 | 概要统计(毫秒)    |
| summary                   | 概要统计          |
| phys_addr                 | 物理地址          |
| phys_flow                 | 物理会话          |
| internal_ip_addr          | 内网地址          |
| external_ip_addr          | 外网地址          |
| ip_flow                   | IP 会话           |
| tcp_flow                  | TCP 会话          |
| udp_flow                  | UDP 会话          |
| application               | 网络应用          |
| netsegment                | 网段统计          |
| service_access            | 服务访问          |
| tcp_server_port           | TCP 服务端口      |
| udp_server_port           | UDP 服务端口      |
| vlan_stat                 | VLAN 统计         |
| mpls_vpn_stat             | MPLS VPN 统计     |
| app_monitor_client        | 应用监控客户端    |
| app_monitor_server        | 应用监控服务器    |
| app_monitor_net_seg       | 应用监控网段统计  |
| app_monitor_ip_flow       | 应用监控 IP 会话  |
| app_monitor_tcp_flow      | 应用监控 TCP 会话 |
| app_trans_stat_trend      | 应用交易统计      |
| app_trans_stat            | 交易统计          |
| app_trans_log             | 交易日志          |
| alarm_log                 | 所有警报          |
| peer_ip_addr              | 通信地址          |
| ip_addr                   | IP 地址           |
| ip_addr_tcp_client        | TCP 客户端        |

 



 

| ***\*表\**** ***\*ID\****         | ***\*描述\****             |
| --------------------------------- | -------------------------- |
| ip_addr_udp_client                | UDP 客户端                 |
| ip_application                    | IP 地址网络应用分布        |
| app_trans_client                  | 交易客户端                 |
| app_trans_server                  | 交易服务器                 |
| app_trans_net_segment             | 交易网段统计               |
| dns_alarm_log                     | 域名 IP 地址               |
| netsegment_flow                   | 网段间统计                 |
| vxlan_stat                        | VXLAN 统计                 |
| dscp_stat                         | DSCP 统计                  |
| netsegment_group                  | 网段分组                   |
| app_monitor_net_seg_group         | 应用监控网段分组           |
| application_group                 | 应用分组                   |
| app_trans_ipflow                  | 交易 IP 会话               |
| app_trans_field_stat              | 交易字段统计               |
| app_trans_field_stat_as_one_field | 按一个统计字段记录查询结果 |
| id_String_mapping_log             | 字符串与 ID 映射关系表     |
| voip_summary                      | VoIP 概要统计              |
| voip_session                      | VoIP 会话                  |
| voip_endpoint                     | VoIP 终端                  |
| voip_endpoint_flow                | VoIP 终端会话              |
| voip_netsegment                   | 网段统计                   |
| voip_netsegmentflow               | VoIP 网段间统计            |
| voip_flow                         | 媒体信令流                 |
| app_client                        | 网络应用客户端             |
| voip_message                      | 消息统计                   |
| app_monitor_udp_flow              | 应用监控 UDP 会话          |
| app_monitor_service_access        | 应用监控服务访问           |
| app_monitor_tcp_server_port       | 应用监控服务端口           |
| app_monitor_udp_server_port       | 应用监控服务端口           |
| netflow_stat                      | NETFLOW 接口统计           |
| virtual_network                   | 所有虚拟网统计             |
| in_seg_ip_addr                    | 进网地址                   |
| seg_all_ip_addr                   | 进出网地址                 |
| server_port_stat                  | 服务端口统计               |
| service_access_client             | 服务访问客户端             |

 



 

| ***\*表\**** ***\*ID\****      | ***\*描述\**** |
| ------------------------------ | -------------- |
| service_access_server          | 服务访问服务   |
| netsegment_congestion          | 网段拥塞       |
| app_monitor_ip_addr_tcp_client | 客户端(TCP)    |
| app_monitor_ip_addr_udp_client | 客户端(UDP)    |
| device_stat                    | 设备统计       |

 

 

说明：

各版本的统计表有差异，上面表格中的统计表仅供参考。

为了获取到统计表的准确信息，建议通过接口查询，获取当前版本的统计表。

 

***\*3.7\**** ***\*统计表字段\**** ***\*ID\**** ***\*定义\****

统计表字段 ID 都是字符串，可以通过[“8.3 枚举统计表字段”](#bookmark176)获取。

统计表字段 ID 参考文档：

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml18864\wps10.jpg) 

科来网络回溯分析 系统_统计表字段ID

 

 

说明：

各版本的统计表字段间有差异，上面表格中的字段仅供参考。

为了获取到统计表字段的准确信息，建议通过接口查询，获取当前版本的统计表字 段。



 

***\*4\**** ***\*身份验证与注销\****

 

***\*4.1\**** ***\*身份验证\****

 

***\*接口功能\****

 

在接口调用之前，需要完成身份的验证，只有当身份验证成功后，才能通过接口进 行后续的数据查询或配置操作。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/login

***\*请求消息\****

 

数据格式：JSON（JSON 数据前需拼接上“param=”,再以 POST 的方式发送） URL 示例：

https:// [************](************):8080/csras_api/login

 

 

JSON 示例： param={

"username":"admin",  "password":"123456",

"encrypcode":0 }

请求参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****         |
| -------------- | ------------------ | -------------- | -------------- | ---------------------- |
| username       | 是                 | String         | 无             | 用户名，登录用户名。   |
| password       | 是                 | String         | 无             | 密码。                 |
| encrypcode     | 是                 | Uint32         | l 0：不加密    | 是否已将密码进行加密。 |

 



 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\****                                            | ***\*说明\**** |
| -------------- | ------------------ | -------------- | --------------------------------------------------------- | -------------- |
|                |                    |                | l 1：已进行 MD5 加密l 2 ：base64 编码l 3：已进行 RSA 加密 |                |

 

 

说明：

l encrypcode 字段值为 1，则密码是 MD5 算法加密后的字符串

l encrypcode 字段值为 2，则密码是 base64 编码后的字符串

l encrypcode 字段值为 3，则密码是 RSA 算法加密后的字符串

l 如密码中存在特殊字符，如 “& ” 字符，会导致登录失败，需要进行参数编 码

***\*响应消息\****

 

正常情况下，身份验证接口的返回 JSON 数据如下： {

"login_errcode":0,

"login_errmsg":"登录成功",

"session":"089A60F4-EA86-47C5-907E-2C51E7F63BEA" }

响应参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| login_errcode  | 否                 | Uint16         | 无             | 登录错误码。                                                 |
| login_errmsg   | 否                 | String         | 无             | 错误信息。                                                   |
| session        | 否                 | String         | 无             | 登录会话 ID，除登录以外的所有接口都要 带参数正确的 session 确保已经成功登录。错误的 session 或者没有参数session 进行任意查询都将返回 403 错误。 |

 

 

登录错误码说明：

 

| ***\*登录状态码\**** | ***\*说明\****   |
| -------------------- | ---------------- |
| 0                    | 登录成功         |
| 1                    | 用户名或密码错误 |

 



 

| ***\*登录状态码\**** | ***\*说明\****                     |
| -------------------- | ---------------------------------- |
| 2                    | 账户禁用                           |
| 3                    | 帐户锁定                           |
| 4                    | 权限不够                           |
| 5                    | 登录超时                           |
| 6                    | 连接受限，连接数超过了系统授权限制 |
| 7                    | 未知错误                           |
| 8                    | 登录票据错误                       |
| 9                    | 授权错误                           |

 

 

***\*4.2\**** ***\*注销登录\****

 

***\*接口功能\****

 

在完成数据的获取后，需要注销退出该会话，服务器将会释放资源。

***\*请求方法\****

 

GET

***\*URL\****

 

https://host:port/csras_api/{session}/logout/

***\*请求消息\****

 

URL 示例：

https:// [************](************):8080/csras_api/089A60F4-EA86-47C5-907E-

2C51E7F63BEA/logout/ 请求参数说明：

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****    |
| -------------- | ------------------ | -------------- | -------------- | ----------------- |
| session        | 是                 | String         | 无             | 已登录的会话 ID。 |

 

 

***\*响应消息\****



正常情况下，注销登录接口的返回 JSON 数据如下： {

"logout_msg":"OK", }

响应参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\**** |
| -------------- | ------------------ | -------------- | -------------- | -------------- |
| logout_msg     | 否                 | String         | 无             | 登出信息。     |

 



 

***\*5\**** ***\*基础信息获取\****

 

***\*5.1\**** ***\*接口版本信息\****

 

***\*接口功能\****

 

为了第三方及时获知科来网络回溯系统接口服务是否更新，系统提供了接口用于获 取版本信息。系统会尽力避免系统接口服务更新给第三方带来影响，例如在查询统 计数据接口时，返回结果带有字段描述，包含了字段类型和长度信息等。

***\*请求方法\****

 

GET

***\*URL\****

 

https://host:port/csras_api/{session}/service_infos

***\*请求消息\****

 

URL 示例：

https:// [************](************):8080/csras_api/089A60F4-EA86-47C5-907E-

2C51E7F63BEA /service_infos 请求参数说明：

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****    |
| -------------- | ------------------ | -------------- | -------------- | ----------------- |
| session        | 是                 | String         | 无             | 已登录的会话 ID。 |

 

 

***\*响应消息\****

 

正常情况下，接口版本信息获取接口的返回 JSON 数据如下： {

"errcode":0,

"errmsg":"",

"api_version":1,



"product_version":"5.0.1" }

响应参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****         |
| -------------- | ------------------ | -------------- | -------------- | ---------------------- |
| errcode        | 否                 | Uint16         | 无             | 错误码。               |
| errmsg         | 否                 | String         | 无             | 错误信息。             |
| api_version    | 否                 | String         | 无             | 接口版本号。           |
| desc           | 否                 | String         | 无             | 科来网络回溯系统版本。 |

 

 

***\*5.2\**** ***\*网络协议信息\****

 

***\*接口功能\****

 

通过该接口可以获取到网络协议的 ID。

***\*请求方法\****

 

GET

***\*URL\****

 

https://host:port/csras_api/{session}/protocols

***\*请求消息\****

 

URL 示例：

https:// [************](************) :8080/csras_api/089A60F4-EA86-47C5-907E-2C51E7F63BEA /protocols

请求参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****    |
| -------------- | ------------------ | -------------- | -------------- | ----------------- |
| session        | 是                 | String         | 无             | 已登录的会话 ID。 |

 

 

***\*响应消息\****

 

正常情况下，网络协议信息配置获取接口的返回 JSON 数据如下：



{

"errcode": 0,

"errmsg": "Successful request", "protocols":[

{

"num":600,

"color":16744192,

"name":"传输控制协议", "abbr":"TCP",

"desc":"TCP 是面向连接的通信协议，通过三次握手建立连接...", },

// 后续还有其他网络协议信息

] }

响应参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****               |
| -------------- | ------------------ | -------------- | -------------- | ---------------------------- |
| errcode        | 否                 | Uint16         | 无             | 错误码。                     |
| errmsg         | 否                 | String         | 无             | 错误信息。                   |
| protocols      | 否                 | String         | 无             | 网络协议对象数组。           |
| number         | 否                 | Uint16         | 无             | 网络协议编号。               |
| color          | 否                 | Uint32         | 无             | 协议文本颜色，RGB 颜色代码。 |
| name           | 否                 | String         | 无             | 协议全称。                   |
| abbr           | 否                 | String         | 无             | 协议简称。                   |
| desc           | 否                 | String         | 无             | 协议描述。                   |

说明：多个协议信息将依次存放 protocols 数组中。

 

 

***\*5.3\**** ***\*链路时间\****

 

***\*接口功能\****

 

通过该接口可以获取到链路的开始统计时间、最新分析数据时间、链路开始获取到 数据包的时间和链路最新获取到数据包的时间。



***\*请求方法\****

 

GET

***\*URL\****

 

https://host:port/csras_api/{session}/ time/{timetype}/{netlinkid}

***\*请求消息\****

 

URL 示例：

https:// [**************](**************):8080/csras_api/4D567F2A-DE62-179E-7582-

C9F5D1657BFE/time/statStartTime/1 请求参数说明：

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| session        | 是                 | String         | 无                                                           | 已登录的会话 ID。                                            |
| timetype       | 是                 | String         | l statStartTime：该链路开始 统计数据的时间；l lastAnalysisTime：该链路 最新分析数据的时间；l packetStartTime：该链路开 始获取到数据包的时间；l packetLastTime：该链路最 新获取到数据包的时间 | 时间类型。                                                   |
| netlinkid      | 是                 | Uint8          | 无                                                           | 链路 ID，可通过[“6.1 配](#bookmark177) [置获取”](#bookmark178)获取到链路 ID。 |

 

 

***\*响应消息\****

 

返回的 json 数据如下： {

"errcode":0,

"errmsg":" Successful request ",

"statStartTime": 1697086500000.0

//注：时间戳单位为毫秒， {timetype}与发起请求时 url 中的{timetype}值相同， 已在上表格中描述。



}

响应参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                       |
| -------------- | ------------------ | -------------- | -------------- | ---------------------------------------------------- |
| errcode        | 否                 | Uint16         | 无             | 错误码。                                             |
| errmsg         | 否                 | String         | 无             | 错误信息。                                           |
| **timetype**   | 否                 | String         | 无             | 与请求消息中时间类型一致。该示例中为 statStartTime。 |

 



 

***\*6\**** ***\*配置获取\****

 

***\*6.1\**** ***\*配置获取\****

 

***\*接口功能\****

 

通过该接口可以获取到系统和链路相关的配置信息。

***\*请求方法\****

 

GET

***\*URL\****

 

https://host:port/csras_api/{session}/configs/getting/{type}/{netlinkid}

***\*请求消息\****

 

URL 示例:

获取链路的应用配置

https:// [************](************):8080/csras_api/089A60F4-EA86-47C5-907E- 2C51E7F63BEA/configs/getting/application/1

获取系统的账户配置

https:// [************](************):8080/csras_api/089A60F4-EA86-47C5-907E-

2C51E7F63BEA /configs/getting/account/ 请求参数说明：

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\****           | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | ------------------------ | ------------------------------------------------------------ |
| session        | 是                 | String         | 无                       | 已登录的会话 ID。                                            |
| netlinkid      | 是                 | Uint8          | 无                       | 链路 ID，可通过[“6.1 配](#bookmark178) [置获取”](#bookmark178)获取到链路 ID。 |
| type           | 是                 | String         | 请参加下方 type 类型表。 | 表示要获取配置的类型。                                       |

 

 

type 类型如下表：



 

| ***\*type\**** ***\*取值\**** | ***\*配置类型\**** | ***\*所属对象\**** |
| ----------------------------- | ------------------ | ------------------ |
| subNetlink                    | 子链路             | 链路配置           |
| customprotocol                | 自定义协议         |                    |
| bargain                       | 交易               |                    |
| statsegment                   | 网段               |                    |
| nametable                     | 名字表             |                    |
| alarminfo                     | 警报               |                    |
| application                   | 应用               |                    |
| innersegment                  | 进出网网段         |                    |
| metafetch                     | 元数据提取任务     |                    |
| netlink                       | 链路               | 系统配置           |
| account                       | 账户               |                    |
| storage                       | 存储               |                    |
| adapter                       | 接口               |                    |
| querytask                     | 推送任务           |                    |
| broker                        | Broker 配置        |                    |
| serverconfig                  | 系统配置           |                    |
| fieldclip                     | 字段裁剪           |                    |
| netflowdevinfo                | Netflow 设备       |                    |

 

 

***\*响应消息\****

 

正常情况下，接口版本信息获取接口的返回 JSON 数据如下： {

"errcode": 0,

"errmsg": "Successful request",

"xml": "<CSRASConfig><Element handle=\"application\" version=\"1\" netlink_id=\"1\">\t<Application id=\"1934\" flags=\"1\" library=\"0\" name=\"DNS\" type=\"0\" priority=\"0\" monitor=\"false\"

goodtransrtt=\"1\" normaltransrtt=\"4\" badtransrtt=\"10\" enablepktcut=\"false\"

pktlimitlen=\"0\">\t\t<Description/>\t\t<BargainGroup enable=\"false\" id=\"29999\"/>\t</Application>\t</Element></CSRASConfig>"

 

 

获取配置失败，将返回错误信息，比如： {



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml18864\wps11.jpg)![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml18864\wps12.png)

"errcode": 20,

"errmsg": "Parameter error: invalid configuration type", "xml": ""

}

响应参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| errcode        | 否                 | Uint16         | 无             | 错误码。                                                     |
| errmsg         | 否                 | String         | 无             | 错误信息。                                                   |
| xml            | 否                 | String         | 无             | 返回获取的配置内容。获取配置成功，将 返回配置对象的 XML 内容，详细参见[7.1](#bookmark179) [配置格式说明。](#bookmark180) |

 



 

***\*7\**** ***\*配置设置\****

 

***\*7.1\**** ***\*配置格式说明\****

 

《科来网络回溯分析系统》自5.0 开始，系统的配置以 xml 的形式返回给第三方， 这样方便阅读，容错性和兼容性增强。

配置的公共规则说明：

1. <CSRASConfig>为配置的根节点，上传或下载配置的根节点都应有该节点。
2. <Element>为一种配置类型的开始节点，<Element>节点可以包含多个同类型 的配置。属性 handle 值为配置唯一标识， 属性 version 为配置格式版本，属 性 netlink_id 为配置所属链路 ID。例如：<Element handle="nametable"

version="1" netlink_id="1">标识接下来是一种配置，handle 的值为

nametable，标识了该配置类型为名字表，服务端将按照链路配置进行相应处 理， netlink_id 为 1，标识该配置的所属链路 ID 为 1。当设置的类型为系统配 置时，netlink_id 固定为 64。

3. 属性 flags,  为配置掩码：

 

| MASK_ENABLE  | = 0x0001, | // 启用          |
| ------------ | --------- | ---------------- |
| MASK_DELETE  | = 0x0002, | // 删除          |
| MASK_INVALID | = 0x0004, | // 无效          |
| MASK_UPM     | = 0x0800, | //  中心下发配置 |

删除配置时，设置配置属性 flags 为2 即可，其余的属性可以不填充。

4. xml 作为配置设置参数时，xml 中没有的属性或节点，对应的配置将不会更改， 只会修改 xml 中具有的对象或对象属性。
5. 如果某配置类型没有配置项，将只返回节点<CSRASConfig>和<Element>。

 

***\*7.2\**** ***\*配置接口说明\****

 

***\*接口功能\****

 

该接口可以配置账户、接口、链路、警报等信息。

***\*请求方法\****

 

POST



***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 数据请求示例，以新增应用为例： param={

"type":"application", "child_type":"",

"netlink":8,

"xml":" <Element handle='application' version='1' netlink_id='1'> <Application flags='1' library='0' name='my-app-test' type='1'

priority='0' monitor='true' goodtransrtt='10' normaltransrtt='30'

badtransrtt='100' enablepktcut='false' pktlimitlen='64'><Description/> <Pair ip1='*************' port1='3000' ip2='*************' port2=''

protocol='1'/> </Application></Element> "

}

请求参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| type           | 是                 | String         | 无             | 配置类型，请参见[“6.1 配置获取”](#bookmark178)。             |
| child_type     | 否                 | String         | 无             | 该参数无需配置，留空即可。                                   |
| netlinkid      | 是                 | Uint8          | 无             | 链路 ID，可通过[“6.1 配置获取”](#bookmark178)获取到链路 ID。 |
| xml            | 是                 | String         | 无             | xml 配置，配置说明请参见各配置项的具体描述。本示例用，应用配置的格式请参见[“7.7.1](#bookmark181)[标准应](#bookmark182) [用”](#bookmark183)。 |

 

 

***\*响应消息\****

 

配置成功时，返回 JSON 数据如下： {

errcode: 0,

errmsg: "请求成功",

xml: "<Element version='0001'>更新自定义应用成功 </Element></CSRASConfig>"



}

配置失败时，返回 JSON 数据如下： {

errcode: 22,

errmsg: "请求成功",

xml: "<CSRASConfig><Element version='0001' netlink_id='12'> <Error code='1'>应用名称'标准应用 2-API2'已经存在

</Error></Element></CSRASConfig>" }

响应参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| errcode        | 否                 | Uint16         | 无             | 错误码。                                                     |
| errmsg         | 否                 | String         | 无             | 错误信息。                                                   |
| xml            | 否                 | String         | 无             | 配置成功： <CSRASConfig><Element version='0001'>**[****成** **功信息****]**</Element></CSRASConfig>配置失败：<CSRASConfig> <Element version='0001' netlink_id="**[****链路****id****]**"> <Error code="**[****错误码****]**">**[****错****误信息****]**</Error> </Element> |

 

 

***\*7.3\**** ***\*接口配置\****

 

***\*7.3.1\**** ***\*管理接口\****

 

***\*接口功能\****

 

用于配置回溯服务器的管理接口。



***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置管理接口： param={

"type":"adapter", "child_type":"",

"xml":"<Element handle='adapter'

netlink_id='64'><CaptureAdapter><PacketSource><Adapter><DeviceId>ens34</D eviceId><Medium>Ethernet</Medium><Tunnel>1</Tunnel><Direction>0</Directio n><VxlanUsed>0</VxlanUsed><CapwapUsed>0</CapwapUsed><GeneveUsed>0</Geneve Used><VxlangpealUsed>0</VxlangpealUsed><SdwanUsed>0</SdwanUsed><PrivateGr eUsed>0</PrivateGreUsed><VxlanAddr/><VxlanPort/><CapwapPort/><GenevePort/ ><VxlangpealPort/><SdwanPort/><Comment/><Ip>0.0.0.0</Ip></Adapter></Packe tSource></CaptureAdapter><ManageAdapter><Adapter>ens32</Adapter></Manage  Adapter><InterruptAdapter/></Element>"

}

说明：

l 采集口和管理口配置属于同一个 xml 中的不同节点，需要一起下发。

l 管理接口的 IP 地址不支持通过 API 接口设置，可以通过修改操作系统的网卡配 置文件进行配置。

 

 

请求参数说明：

 

| ***\*参数\****   | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| ---------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| < ManageAdapter> | 是                 | 无             | 无             | 管理接口配置根节点。                                         |
| <Adapter>        | 是                 | String         | 无             | 接口名称，可通过[“6.1 配置获取”](#bookmark178)获取 到接口名称。 |

 

 

***\*XML\**** ***\*说明\****



 

<ManageAdapter><Adapter>ens32</Adapter></ManageAdapter>

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.3.2\**** ***\*采集接口\****

 

***\*接口功能\****

 

用于配置回溯采集接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置采集接口： param={

"type":"adapter", "child_type":"",

"xml":"<Element handle='adapter'

netlink_id='64'><CaptureAdapter><PacketSource><Adapter><DeviceId>ens34</   DeviceId><Medium>Ethernet</Medium><Tunnel>1</Tunnel><Direction>0</Direct   ion><VxlanUsed>0</VxlanUsed><CapwapUsed>0</CapwapUsed><GeneveUsed>0</Gen   eveUsed><VxlangpealUsed>0</VxlangpealUsed><SdwanUsed>0</SdwanUsed><Priva   teGreUsed>0</PrivateGreUsed><VxlanAddr/><VxlanPort/><CapwapPort/><Geneve   Port/><VxlangpealPort/><SdwanPort/><Comment/><Ip>0.0.0.0</Ip></Adapter><   /PacketSource></CaptureAdapter><ManageAdapter><Adapter>ens32</Adapter></M anageAdapter><InterruptAdapter/></Element>"

}

说明：采集口和管理口配置属于同一个 xml 中的不同节点，需要一起下发。



 

 

请求参数说明：

 

| ***\*参数\****   | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                      | ***\*说明\****                                               |
| ---------------- | ----------------------------- | -------------- | --------------------------------------------------- | ------------------------------------------------------------ |
| <CaptureAdapter> | 是                            | 无             | 无                                                  | 采集接口配置根节点。                                         |
| <PacketSource>   | 是                            | 无             | 无                                                  | 子节点。                                                     |
| <Adapter>        | 是                            | 无             | 无                                                  | 子节点。                                                     |
| <DeviceId>       | 是                            | String         | 无                                                  | 表示接口名称, 通过[“6.1 配置获](#bookmark178) [取”](#bookmark178)接口可以获取到接口名称。 |
| <Medium>         | 是                            | String         | l Ethernet l PPPl CFPl linux cooked capturel Raw IP | 表示传输媒介，配置时直接输入字 符串即可。                    |
| <Tunnel>         | 是                            | Uint8          | l 1：第一层l 2：第二层l 3：第三层l 4：第四层        | 表示 IP 地址识别的层级，IP 地址层 级包括 4 种层级，直接输入对应的  编号即可。 |
| <Direction>      | 是                            | Uint8          | 0                                                   | 无意义节点，直接设置为固定值 0 即可。                        |
| <VxlanUsed>      | 是                            | Bool           | l 0：关闭l 1：开启 默认值：0                        | 自定义 VXLAN 特征是否开启。                                  |
| <CapwapUsed>     | 是                            | Bool           | l 0：关闭l 1：开启 默认值：0                        | 自定义 CAPWAP 特征是否开启。                                 |
| <GeneveUsed>     | 是                            | Bool           | l 0：关闭l 1：开启 默认值：0                        | 自定义 GENEVE 特征是否开启。                                 |
| <VxlangpealUsed> | 是                            | Bool           | l 0：关闭l 1：开启 默认值：0                        | 自定义 VXLAN_GPE_AL 特征是否 开启。                          |
| <SdwanUsed>      | 是                            | Bool           | l 0：关闭l 1：开启                                  | 自定义 SD_WAN 特征是否开启。                                 |

 



 

| ***\*参数\****   | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****               | ***\*说明\****                                               |
| ---------------- | ----------------------------- | -------------- | ---------------------------- | ------------------------------------------------------------ |
|                  |                               |                | 默认值：0                    |                                                              |
| <PrivateGreUsed> | 是                            | Bool           | l 0：关闭l 1：开启 默认值：0 | 启用 GRE 私有格式解码。                                      |
| <Comment/>       | 否                            | String         | 无                           | 表示备注信息。                                               |
| <Ip>             | 是                            | String         | [0.0.0.0](0.0.0.0)           | 接口 IP 地址，采集接口不用配置该 节点，固定为 [0.0.0.0](0.0.0.0) 即可。 |
| <VxlanPort>      | 否                            | Uint16         | 1-65535                      | 自定义 VXLAN 特征端口值，<VxlanUsed>值为 1 才携带该节 点。   |
| <CapwapPort>     | 否                            | Uint16         | 1-65535                      | 自定义 CAPWAP 特征端口值 ，只有 当<CapwapUsed>值为 1 时，才需  要配置该节点。 |
| <GenevePort>     | 否                            | Uint16         | 1-65535                      | 自定义 GENEVE 特征端口值，只有 当<GeneveUsed>值为 1 时，才需  要配置该节点。 |
| <VxlangpealPort> | 否                            | Uint16         | 1-65535                      | 自定义 VXLAN_GPE_AL 特征端口  值，只有当< VxlangpealUsed>值为 1 时，才需要配置该节点。 |
| <SdwanPort>      | 否                            | Uint16         | 1-65535                      | 自定义 SD_WAN 特征端口值，只有 当<SdwanUsed>值为 1 时，才需要 配置该节点。 |

 

 

***\*XML\**** ***\*说明\****

 

<CaptureAdapter> <PacketSource><Adapter><DeviceId>ens34</DeviceId> <Medium>Ethernet</Medium> <Tunnel>1</Tunnel>

 



 

<Direction>0</Direction><VxlanUsed>0</VxlanUsed><CapwapUsed>0</CapwapUsed> <GeneveUsed>0</GeneveUsed><VxlangpealUsed>0</VxlangpealUsed> <SdwanUsed>0</SdwanUsed><PrivateGreUsed>0</PrivateGreUsed><VxlanAddr/> <VxlanPort/> <CapwapPort/> <GenevePort/><VxlangpealPort/> <SdwanPort/><Comment/><Ip>[0.0.0.0](0.0.0.0)</Ip> </Adapter></PacketSource> </CaptureAdapter>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.4\**** ***\*虚接口配置\****

 

用于配置回溯采集虚接口，支持配置多种类型虚接口，包括 VLAN、 ISL VLAN、 MPLS VPN、VXLAN、NETFLOW、GRE、网段、物理地址、GENEVE、

OPCODE 这几种类型。 类型与 type 的对应关系：

l vlan： [1](#bookmark185)

l mpls_vpn：[2](#bookmark186)

l vxlan：[3](#bookmark187)



l 网段：[4](#bookmark188)

l islvlan ：[5](#bookmark189)

l netflow：[6](#bookmark190)

l gre ：[7](#bookmark191)

l 物理地址：[8](#bookmark192)

l geneve： [10](#bookmark193)

l opcode： [11](#bookmark194)

 

***\*7.4.1 VLAN\**** ***\*虚接口\****

 

***\*接口功能\****

 

用于配置 VLAN 类型的虚接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置 VLAN 虚接口： param={

"type":"adapter", "child_type":"",

"xml":"<Element handle='adapter'

netlink_id='64'><CaptureAdapter><PacketSource><Adapter><DeviceId>ens34</D eviceId><Medium>Ethernet</Medium><Tunnel>1</Tunnel><Direction>0</Directio n><VxlanUsed>0</VxlanUsed><CapwapUsed>0</CapwapUsed><GeneveUsed>0</Geneve Used><VxlangpealUsed>0</VxlangpealUsed><SdwanUsed>0</SdwanUsed><PrivateGr eUsed>0</PrivateGreUsed><VxlanAddr/><VxlanPort/><CapwapPort/><GenevePort/ ><VxlangpealPort/><SdwanPort/><Comment/><Ip>0.0.0.0</Ip><Endpoint

***\*alias='vlan-3008'\**** ***\*dir\*******\*ection='0'\**** ***\*interdir='2'\****

type='1'><IP>0.0.0.0</IP><AUTH_CODE/><STATE>1</STATE><UPDATE_OVERRIDE>fa  lse</UPDATE_OVERRIDE><VN_LEVEL>1</VN_LEVEL><VID>3008</VID><NETF_VERSION>  1</NETF_VERSION><INTERFACE/><Netsegment><AnalysisRule><Entry>***********  /16</Entry></AnalysisRule></Netsegment><Macsegment><AnalysisRule><Entry>  00:50:56:2F:72:1E</Entry></AnalysisRule></Macsegment></Endpoint></Adapte



r></PacketSource></CaptureAdapter><ManageAdapter><Adapter>ens32</Adapter> </ManageAdapter><InterruptAdapter/></Element>"

}

说明：

虚接口节点属于采集接口子节点，需要与采集接口一同下发

 

 

请求参数说明：

 

| ***\*参数\****     | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                   | ***\*说明\****                                               |
| ------------------ | ----------------------------- | -------------- | ------------------------------------------------ | ------------------------------------------------------------ |
| <Endpoint>         | 是                            | 无             | 无                                               | 虚接口配置根节点。                                           |
| alias              | 是                            | String         | 无                                               | 表示虚接口别名。 自定义字段， 无长度限制。                   |
| direction          | 是                            | Uint8          | 0                                                | 该参数对 VLAN 虚接口不生效， 配置为固定值即可。              |
| interdir           | 是                            | Uint8          | 2                                                | 该参数对 VLAN 虚接口不生效， 配置为固定值即可。              |
| type               | 是                            | Uint8          | 1                                                | 表示虚接口类型，对于 VLAN 虚 接口来说，固定设置为 1。        |
| <IP>               | 是                            | String         | [0.0.0.0](0.0.0.0)                               | 该参数对 VLAN 虚接口不生效， 配置为固定值即可。              |
| <STATE>            | 是                            | Uint8          | 1                                                | 该参数对 VLAN 虚接口不生效， 配置为固定值即可。              |
| <UPDATE_OVERR IDE> | 是                            | Bool           | false                                            | 该参数对 VLAN 虚接口不生效， 配置为固定值即可。              |
| <VN LEVEL>         | 是                            | Uint16         | l 1：最外层l 2：最内层l 4：最内两层l 8：最外两层 | 表示指定 VLAN的层级。                                        |
| <VID>              | 是                            | String         | 无                                               | 表示 VLAN 标识，该参数与指定 的 VLAN 层级相关。对于最内两 层或最外两层来说，采用内层.外层的格式设置，例如： 2007.2011 |
| <NETF_VERSION>     | 是                            | Uint16         | 1                                                | 该参数对 VLAN 虚接口不生效， 配置为固定值即可。              |
| <Netsegment>       | 否                            | 无             | 无                                               | 网段规则根节点，如果不需要配                                 |

 



 

| ***\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| -------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
|                |                               |                |                                                              | 置网段，则不用配置  <Netsegment>节点。                       |
| <AnalysisRule> | 否                            | 无             | 无                                                           | 网段规则下的子节点。                                         |
| <Entry>        | 否                            | String         | 支持以下格式：l [***********](***********)/16l [***********](***********)/25 [*********](*********) | 表示具体的网段规则，如果需要添加多个规则，需要添加 <Entry>。 |
| <Macsegment>   | 否                            | 无             | 无                                                           | 物理地址根节点，如果不需要配置物理地址，则不用配置< Macsegment >节点。 |
| <AnalysisRule> | 否                            | 无             | 无                                                           | 物理地址下的子节点。                                         |
| <Entry>        | 否                            | String         | 无                                                           | 表示具体的物理地址。物理地址 只能配置单个，不支持范围。如 果需要添加多个规则，需要添加 <Entry>。 |

 

 

***\*XML\**** ***\*说明\****

 

<Endpoint alias='vlan-3008' direction='0' interdir='2' type='1'><IP>[0.0.0.0](0.0.0.0)</IP> <AUTH_CODE/><STATE>1</STATE><UPDATE_OVERRIDE>false</UPDATE_OVERRIDE><VN_LEVEL>1</VN_LEVEL><VID>3008</VID><NETF_VERSION>1</NETF_VERSION> <INTERFACE/><Netsegment><AnalysisRule><Entry>***********/16</Entry> </AnalysisRule></Netsegment>

 



 

<Macsegment><AnalysisRule><Entry>00:50:56:2F:72:1E</Entry></AnalysisRule> </Macsegment></Endpoint>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[7.2 配置接口说明中的](#bookmark44)[“响应消息”](#bookmark184)。

***\*7.4.2\**** ***\*ISL VLAN\**** ***\*虚接口\****

 

***\*接口功能\****

 

用于配置 ISL VLAN 类型的虚接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置 ISL VLAN 虚接口：

param={

"type":"adapter", "child_type":"",

"xml":" <Element handle='adapter'

netlink_id='64'><CaptureAdapter><PacketSource><Adapter><DeviceId>ens34 </DeviceId><Medium>Ethernet</Medium><Tunnel>1</Tunnel><Direction>0 </Direction><VxlanUsed>0</VxlanUsed><CapwapUsed>0</CapwapUsed><G



eneveUsed>0</GeneveUsed><VxlangpealUsed>0</VxlangpealUsed><SdwanU  sed>0</SdwanUsed><PrivateGreUsed>0</PrivateGreUsed><VxlanAddr/><Vxl  anPort/><CapwapPort/><GenevePort/><VxlangpealPort/><SdwanPort/><Co   mment/><Ip>0.0.0.0 </Ip><Endpoint alias='vlan-3008' direction='0' interdir='2'   type='5'><IP>0.0.0.0 </IP><AUTH_CODE/><STATE>1</STATE><UPDATE_OVE  RRIDE>false</UPDATE_OVERRIDE><VN_LEVEL>1</VN_LEVEL><VID>3008</VI  D><NETF_VERSION>1</NETF_VERSION><INTERFACE/><Netsegment><Analys isRule><Entry>***********/16</Entry></AnalysisRule></Netsegment><Macseg  ment><AnalysisRule><Entry>00:50:56:2F:72:1E</Entry></AnalysisRule></Macs  egment></Endpoint> </Adapter></PacketSource></CaptureAdapter><Manag  eAdapter><Adapter>ens32</Adapter></ManageAdapter><InterruptAdapter/> </Element>"

}

说明：

虚接口节点属于采集接口子节点，需要与采集接口一同下发。

 

 

请求参数说明：

 

| ***\*参数\****     | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****     | ***\*说明\****                                          |
| ------------------ | ----------------------------- | -------------- | ------------------ | ------------------------------------------------------- |
| <Endpoint>         | 是                            | 无             | 无                 | 虚接口配置根节点。                                      |
| alias              | 是                            | String         | 无                 | 表示虚接口别名。 自定义字段， 无长度限制。              |
| direction          | 是                            | Uint8          | 0                  | 该参数对 ISL VLAN 虚接口不生 效，配置为固定值即可。     |
| interdir           | 是                            | Uint8          | 2                  | 该参数对 ISL VLAN 虚接口不生 效，配置为固定值即可。     |
| type               | 是                            | Uint8          | 5                  | 表示虚接口类型，对于 ISLVLAN 虚接口来说，固定设置为 5。 |
| <IP>               | 是                            | String         | [0.0.0.0](0.0.0.0) | 该参数对 ISL VLAN 虚接口不生 效，配置为固定值即可。     |
| <STATE>            | 是                            | Uint8          | 1                  | 该参数对 ISL VLAN 虚接口不生 效，配置为固定值即可。     |
| <UPDATE_OVERR IDE> | 是                            | Bool           | false              | 该参数对 ISL VLAN 虚接口不生 效，配置为固定值即可。     |

 



 

| ***\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| -------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| <VN LEVEL>     | 是                            | Uint16         | 1                                                            | 表示指定 ISL VLAN 的层级。                                   |
| <VID>          | 是                            | String         | 无                                                           | 表示 ISL VLAN 标识。                                         |
| <NETF_VERSION> | 是                            | Uint16         | 1                                                            | 该参数对 ISL VLAN 虚接口不生 效，配置为固定值即可。          |
| <Netsegment>   | 否                            | 无             | 无                                                           | 网段规则根节点，如果不需要配置网段，则不用配置  <Netsegment>节点。 |
| <AnalysisRule> | 否                            | 无             | 无                                                           | 网段规则下的子节点。                                         |
| <Entry>        | 否                            | String         | 支持以下格式：l [***********](***********)/16l [***********](***********)/25 [*********](*********) | 表示具体的网段规则，如果需要添加多个规则，需要添加 <Entry>。 |
| <Macsegment>   | 否                            | 无             | 无                                                           | 物理地址根节点，如果不需要配置物理地址，则不用配置< Macsegment >节点。 |
| <AnalysisRule> | 否                            | 无             | 无                                                           | 物理地址下的子节点。                                         |
| <Entry>        | 否                            | String         | 无                                                           | 表示具体的物理地址。物理地址 只能配置单个，不支持范围。如 果需要添加多个规则，需要添加 <Entry>。 |

 

 

***\*XML\**** ***\*说明\****

 

<Endpoint alias='vlan-3008' direction='0' interdir='2' type='1'> <IP>[0.0.0.0](0.0.0.0)</IP><AUTH_CODE/><STATE>1</STATE><UPDATE_OVERRIDE>false</UPDATE_OVERRIDE><VN_LEVEL>1</VN_LEVEL><VID>3008</VID><NETF_VERSION>1</NETF_VERSION> <INTERFACE/><Netsegment>

 



 

<AnalysisRule><Entry>***********/16</Entry></AnalysisRule> </Netsegment><Macsegment><AnalysisRule><Entry>00:50:56:2F:72:1E</Entry></AnalysisRule> </Macsegment></Endpoint>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.4.3\**** ***\*物理地址虚接口\****

 

***\*接口功能\****

 

用于配置物理地址类型的虚接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置物理地址虚接口：

param={

"type":"adapter",



"child_type":"",

"xml":"<Element handle='adapter'

netlink_id='64'><CaptureAdapter><PacketSource><Adapter><DeviceId>ens34 </DeviceId><Medium>Ethernet</Medium><Tunnel>1</Tunnel><Direction>0  </Direction><VxlanUsed>0</VxlanUsed><CapwapUsed>0</CapwapUsed><G  eneveUsed>0</GeneveUsed><VxlangpealUsed>0</VxlangpealUsed><SdwanU sed>0</SdwanUsed><PrivateGreUsed>0</PrivateGreUsed><VxlanAddr/><Vxl  anPort/><CapwapPort/><GenevePort/><VxlangpealPort/><SdwanPort/><Co   mment/><Ip>0.0.0.0 </Ip><Endpoint alias='mac-virtual' direction='0'

interdir='2'

type='8'><IP>0.0.0.0 </IP><AUTH_CODE/><STATE>1</STATE><UPDATE_OVE  RRIDE>false</UPDATE_OVERRIDE><VN_LEVEL>1</VN_LEVEL><VID>00:50:56:  2F:72:1E</VID><MACDIRE>0</MACDIRE><NETF_VERSION>1</NETF_VERSION  ><INTERFACE/><Netsegment><AnalysisRule><Entry>***********/16</Entry></ AnalysisRule></Netsegment></Endpoint></Adapter></PacketSource></Captu reAdapter><ManageAdapter><Adapter>ens32</Adapter></ManageAdapter>  <InterruptAdapter/></Element>"

}

说明：

虚接口节点属于采集接口子节点，需要与采集接口一同下发。

 

 

请求参数说明：

 

| ***\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****     | ***\*说明\****                                          |
| -------------- | ----------------------------- | -------------- | ------------------ | ------------------------------------------------------- |
| <Endpoint>     | 是                            | 无             | 无                 | 虚接口配置根节点。                                      |
| alias          | 是                            | String         | 无                 | 表示虚接口别名。 自定义字段， 无长度限制。              |
| direction      | 是                            | Uint8          | 0                  | 该参数对物理地址虚接口不生 效，配置为固定值即可。       |
| interdir       | 是                            | Uint8          | 2                  | 该参数对物理地址虚接口不生 效，配置为固定值即可。       |
| type           | 是                            | Uint8          | 8                  | 表示虚接口类型，对于物理地址 虚接口来说，固定设置为 8。 |
| <IP>           | 是                            | String         | [0.0.0.0](0.0.0.0) | 该参数对物理地址虚接口不生                              |

 



 

| ***\*参数\****     | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| ------------------ | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
|                    |                               |                |                                                              | 效，配置为固定值即可。                                       |
| <STATE>            | 是                            | Uint8          | 1                                                            | 该参数对物理地址虚接口不生 效，配置为固定值即可。            |
| <UPDATE_OVERR IDE> | 是                            | Bool           | false                                                        | 该参数对物理地址虚接口不生 效，配置为固定值即可。            |
| <VN LEVEL>         | 是                            | Uint16         | 1                                                            | 该参数对物理地址虚接口不生 效，配置为固定值即可。            |
| <VID>              | 是                            | String         | 无                                                           | 表示物理地址，只能设置一个物 理地址。                        |
| <MACDIRE>          | 是                            | Uint8          | l 0：双向地址l 1：源地址l 2：目标地址                        | 用于指定采集物理地址特定方向 的流量。l 双向地址：会采集物理地址 作为源地址和目标地址的流 量。l 源地址：只会采集物理地址 作为源地址的流量。l 目标地址：只会采集物理地 址作为目标地址的流量。 |
| <NETF_VERSION>     | 是                            | Uint16         | 1                                                            | 该参数对物理地址虚接口不生 效，配置为固定值即可。            |
| <Netsegment>       | 否                            | 无             | 无                                                           | 网段规则根节点，如果不需要配置网段，则不用配置  <Netsegment>节点。 |
| <AnalysisRule>     | 否                            | 无             | 无                                                           | 网段规则下的子节点。                                         |
| <Entry>            | 否                            | String         | 支持以下格式：l [***********](***********)/16l [***********](***********)/25 [*********](*********) | 表示具体的网段规则，如果需要添加多个规则，需要添加 <Entry>。 |

 

 

***\*XML\**** ***\*说明\****

 

<Endpoint alias='mac-virtual' direction='0' interdir='2' type='8'> <IP>[0.0.0.0](0.0.0.0)</IP><AUTH_CODE/>

 



 

<STATE>1</STATE><UPDATE_OVERRIDE>false</UPDATE_OVERRIDE> <VN_LEVEL>1</VN_LEVEL><VID>00:50:56:2F:72:1E</VID> <MACDIRE>0</MACDIRE><NETF_VERSION>1</NETF_VERSION> <INTERFACE/><Netsegment><AnalysisRule><Entry>***********/16</Entry> </AnalysisRule></Netsegment> </Endpoint>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.4.4\**** ***\*网段虚接口\****

 

***\*接口功能\****

 

用于配置网段类型的虚接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置网段虚接口：



param={

"type":"adapter", "child_type":"",

"xml":"<Element handle='adapter'

netlink_id='64'><CaptureAdapter><PacketSource><Adapter><DeviceId>ens34 </DeviceId><Medium>Ethernet</Medium><Tunnel>1</Tunnel><Direction>0  </Direction><VxlanUsed>0</VxlanUsed><CapwapUsed>0</CapwapUsed><G  eneveUsed>0</GeneveUsed><VxlangpealUsed>0</VxlangpealUsed><SdwanU sed>0</SdwanUsed><PrivateGreUsed>0</PrivateGreUsed><VxlanAddr/><Vxl  anPort/><CapwapPort/><GenevePort/><VxlangpealPort/><SdwanPort/><Co   mment/><Ip>0.0.0.0 </Ip><Endpoint alias='segment-virtual' direction='0'

interdir='2'

type='4'><IP>0.0.0.0 </IP><AUTH_CODE/><STATE>1</STATE><UPDATE_OVE RRIDE>false</UPDATE_OVERRIDE><VN_LEVEL>1</VN_LEVEL><VID/><NETF_  VERSION>1</NETF_VERSION><INTERFACE/><Netsegment><AnalysisRule><E ntry>***********/16</Entry></AnalysisRule></Netsegment></Endpoint> </Ada pter></PacketSource></CaptureAdapter><ManageAdapter><Adapter>ens32  </Adapter></ManageAdapter><InterruptAdapter/></Element>"

}

说明：

虚接口节点属于采集接口子节点，需要与采集接口一同下发。

 

 

请求参数说明：

 

| ***\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                      |
| -------------- | ----------------------------- | -------------- | -------------- | --------------------------------------------------- |
| <Endpoint>     | 是                            | 无             | 无             | 虚接口配置根节点。                                  |
| alias          | 是                            | String         | 无             | 表示虚接口别名。 自定义字段， 无长度限制。          |
| direction      | 是                            | Uint8          | 0              | 该参数对网段虚接口不生效，配 置为固定值即可。       |
| interdir       | 是                            | Uint8          | 2              | 该参数对网段虚接口不生效，配 置为固定值即可。       |
| type           | 是                            | Uint8          | 4              | 表示虚接口类型，对于网段虚接 口来说，固定设置为 4。 |

 



 

| ***\*参数\****     | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| ------------------ | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| <IP>               | 是                            | String         | [0.0.0.0](0.0.0.0)                                           | 该参数对网段虚接口不生效，配 置为固定值即可。                |
| <STATE>            | 是                            | Uint8          | 1                                                            | 该参数对网段虚接口不生效，配 置为固定值即可。                |
| <UPDATE_OVERR IDE> | 是                            | Bool           | false                                                        | 该参数对网段虚接口不生效，配 置为固定值即可。                |
| <VN LEVEL>         | 是                            | Uint16         | l 1：最外层l 2：最内层l 4：最内两层l 8：最外两层             | 表示指定 VLAN的层级。                                        |
| <VID>              | 否                            | String         | 无                                                           | 该参数对网段虚接口不生效，配 置为空节点。                    |
| <NETF_VERSION>     | 是                            | Uint16         | 1                                                            | 该参数对网段虚接口不生效，配 置为固定值即可。                |
| <Netsegment>       | 是                            | 无             | 无                                                           | 网段规则根节点，如果不需要配置网段，则不用配置  <Netsegment>节点。 |
| <AnalysisRule>     | 是                            | 无             | 无                                                           | 网段规则下的子节点。                                         |
| <Entry>            | 是                            | String         | 支持以下格式：l [***********](***********)/16l [***********](***********)/25 [*********](*********) | 表示具体的网段规则，如果需要添加多个规则，需要添加 <Entry>。 |

 

 

***\*XML\**** ***\*说明\****

 

<Endpoint alias='seg-virtual' direction='0' interdir='2' type='4'><IP>[0.0.0.0](0.0.0.0)</IP> <AUTH_CODE/><STATE>1</STATE><UPDATE_OVERRIDE>false</UPDATE_OVERRIDE> <VN_LEVEL>1</VN_LEVEL><VID/><NETF_VERSION>1</NETF_VERSION>

 



 

<INTERFACE/> <Netsegment><AnalysisRule><Entry>***********/16</Entry></AnalysisRule> </Netsegment></Endpoint>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.4.5\**** ***\*NETFLOW\**** ***\*虚接口\****

 

***\*接口功能\****

 

用于配置 NETFLOW 类型的虚接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置 NETFLOW 虚接口： param={

"type":"adapter", "child_type":"",

"xml":"<Element handle='adapter'

netlink_id='64'><CaptureAdapter><PacketSource><Adapter><DeviceId>ens  34</DeviceId><Medium>Ethernet</Medium><Tunnel>1</Tunnel><Direction >0</Direction><VxlanUsed>0</VxlanUsed><CapwapUsed>0</CapwapUsed  ><GeneveUsed>0</GeneveUsed><VxlangpealUsed>0</VxlangpealUsed><S



dwanUsed>0</SdwanUsed><PrivateGreUsed>0</PrivateGreUsed><VxlanA  ddr/><VxlanPort/><CapwapPort/><GenevePort/><VxlangpealPort/><Sdwan Port/><Comment/><Ip>0.0.0.0</Ip><Endpoint alias='netflow-virtual'

***\*direction\*******\*='0'\**** ***\*interdir\*******\*='2'\****

type='6'><IP> ***********</IP><AUTH_CODE>12315</AUTH_CODE><STA TE>3</STATE><UPDATE_OVERRIDE>true</UPDATE_OVERRIDE><VN_L  EVEL>1</VN_LEVEL><VID>8000</VID><NETF_VERSION>1</NETF_VER  SION><INTERFACE>10</INTERFACE></Endpoint></Adapter></PacketSou rce></CaptureAdapter><ManageAdapter><Adapter>ens32</Adapter></Man ageAdapter><InterruptAdapter/></Element>"

}

说明：

虚接口节点属于采集接口子节点，需要与采集接口一同下发。

 

 

请求参数说明：

 

| ***\*参数\****     | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                    | ***\*说明\****                                            |
| ------------------ | ----------------------------- | -------------- | ------------------------------------------------- | --------------------------------------------------------- |
| <Endpoint>         | 是                            | 无             | 无                                                | 虚接口配置根节点。                                        |
| alias              | 是                            | String         | 无                                                | 表示虚接口别名。 自定义 字段，无长度限制。                |
| direction          | 是                            | Uint8          | 0                                                 | 对于 NETFLOW 虚接口来 说，配置为固定值即可。              |
| interdir           | 是                            | Uint8          | 2                                                 | 对于 NETFLOW 虚接口来 说，配置为固定值即可。              |
| type               | 是                            | Uint8          | 6                                                 | 表示虚接口类型，对于  NETFLOW 虚接口来说， 固定设置为 6。 |
| <IP>               | 是                            | String         | 无                                                | 表示路由器 IP 地址。                                      |
| <AUTH_CODE>        | 是                            | String         | 无                                                | 验证码 ，验证码请从路由 器配置中获取。                    |
| <STATE>            | 是                            | Uint8          | l 1：未启用l 3：获取中 默认值： 1                 | 表示 SNMP 状态。                                          |
| <UPDATE_OVERR IDE> | 是                            | Bool           | l true：覆盖更新l false：不覆盖更新 默认值：flase | 设置与自定义名字冲突时 是否覆盖更新。                     |

 



 

| ***\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                         |
| -------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------ |
| <VN LEVEL>     | 是                            | Uint16         | 1                                                            | 该参数对 NETFLOW 段虚 接口不生效，配置为固定  值即可。 |
| <VID>          | 是                            | String         | 无                                                           | 表示 NEFLOW 导出接口。                                 |
| <NETF_VERSION> | 是                            | Uint16         | l 1 ：NetflowV1l 2 ：NetflowV5l 3 ：NetflowV7l 4 ：NetflowV9l 5 ：NetsteamV5(16 位)l 6 ：NetsteamV9(16 位)l 7 ：NetsteamV9(32 位)l 9： IPFIXl 10：JFLOWV5l 11：JFLOWV9 | 表示 NETFLOW 版本。                                    |
| <INTERFACE>    | 是                            | String         | 无                                                           | 表示分析接口。                                         |

 

 

***\*XML\**** ***\*说明\****

 

<Endpoint alias='netflow-virtual' direction='0' interdir='2' type='6'><IP>[***********](***********)</IP><AUTH_CODE>12315</AUTH_CODE> <STATE>3</STATE><UPDATE_OVERRIDE>true</UPDATE_OVERRIDE> <VN_LEVEL>1</VN_LEVEL><VID>8000</VID><NETF_VERSION>1</NETF_VERSION><INTERFACE>10</INTERFACE> </Endpoint>

 

 

***\*响应消息\****



响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.4.6\**** ***\*MPLS VPN\**** ***\*虚接口\****

 

MPLS VPN 虚接口与 VLAN 虚接口的配置方式相同，只是对应的虚接口类型不一 样。

在配置时，将 type 设置为“2”即可，其余配置与[“7.4.1 VLAN 虚接口”](#bookmark54)配置一 致。

 

***\*7.4.7 VXLAN\**** ***\*虚接口\****

 

VXLAN 虚接口与 ISL VLAN 虚接口的配置方式相同，只是对应的虚接口类型不一 样。

在配置时，将 type 设置为“3”即可，其余配置与[“7.4.2 ISL VLAN 虚接口”](#bookmark56)配置 一致。

 

***\*7.4.8 GRE\**** ***\*虚接口\****

 

GRE 虚接口与 ISL VLAN 虚接口的配置方式相同，只是对应的虚接口类型不一样。 在配置时，将 type 设置为“7”即可，其余配置与[“7.4.2 ISL VLAN 虚接口”](#bookmark56)配置 一致。

***\*7.4.9 GENEVE\**** ***\*虚接口\****

 

GENEVE 虚接口与 ISL VLAN 虚接口的配置方式相同，只是对应的虚接口类型不一 样。

在配置时，将 type 设置为“10”即可，其余配置与[“7.4.2 ISL VLAN 虚接口”](#bookmark56)配 置一致。

 

***\*7.4.10\**** ***\*删除虚接口配置\****

 

***\*接口功能\****

 

用于删除已配置的虚接口。



***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

请求消息的格式与采集接口的请求消息格式类似，只需要将 XML 中<Endpoint>子 节点删除即可。

请求示例参见[“7.3.2 采集接口”](#bookmark50)。

***\*XML\**** ***\*说明\****

 

重新下发采集接口配置，XML 中不携带<Endpoint>子节点即可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.5\**** ***\*链路配置\****

 

链路配置接口，用于新增，删除或者更新链路的配置。

***\*链路类型映射\****

 

系统所支持的链路类型与对应取值的映射关系如下：

l 标准分路器： [1](#bookmark195)

l 汇聚型分路器：[2](#bookmark196)

l 交换机单向流量镜像：[3](#bookmark197)

l 交换机双向流量镜像：[4](#bookmark198)

l 数据包回放：[5](#bookmark199)

l Netflow 双向流量：[6](#bookmark200)

l Agent：[8](#bookmark201)



l 聚合分析：9

 

***\*7.5.1\**** ***\*新增链路\****

 

***\*接口功能\****

 

该接口用于新增链路配置。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，以新增一条“交换机双向流量镜像”链路为例： param={

"type":"netlink", "child_type":"",

"xml": " ***\*<\*******\*Element\**** ***\*handle\*******\*='\*******\*netlink\*******\*'><\*******\*Netlink\**** ***\*id\*******\*='255'\**** ***\*type\*******\*='4'\**** ***\*name='net_link'\****

***\*sequence\*******\*=''><\*******\*Storage\*******\*>1</\*******\*Storage\*******\*><\*******\*Totalbandwidth\*******\*>1000</\*******\*Totalbandwidth\**** ***\*><\*******\*Inbandwidth\*******\*>1000</\*******\*Inbandwidth\*******\*><\*******\*Outbandwidth\*******\*>1000</\*******\*Outbandwidth\*******\*><\**** ***\*AnalysisOpts\*******\*><\*******\*netflowsampling\*******\*>1</\*******\*netflowsampling\*******\*><\*******\*Millisecond\*******\*>0</\*******\*Milli\**** ***\*second\*******\*><\*******\*Switch\**** ***\*enable\*******\*='0'></\*******\*Switch\*******\*><\*******\*TunnelLevel\****

***\*enable\*******\*='0'>1</\*******\*TunnelLevel\*******\*><\*******\*MacTunnelLevel\****

enable='0'>1</MacTunnelLevel></AnalysisOpts><CompressOpts><PacketC  ompressAlgo>1</PacketCompressAlgo><StatCompressAlgo>1</StatCompr   essAlgo><Ratio>30</Ratio></CompressOpts><ExportOptions><EnableExpo rt>0</EnableExport></ExportOptions><VnIdLayers><PointVnidLayer>0</P  ointVnidLayer><SelectedLayers>1</SelectedLayers><SelectedType>0</Se  lectedType></VnIdLayers><NametableOptions><SnmpEnable>0</SnmpEna  ble><UpdatePeriod>0</UpdatePeriod><CoverageConfig>0</CoverageConfi  g></NametableOptions><PacketSource><Adapter><DeviceId>ens34</Devic eId><Medium>Ethernet</Medium><Tunnel>1</Tunnel><Direction>1</Direc tion><VxlanUsed>0</VxlanUsed><CapwapUsed>0</CapwapUsed><Geneve Used>0</GeneveUsed><VxlangpealUsed>0</VxlangpealUsed><SdwanUsed >0</SdwanUsed><PrivateGreUsed>0</PrivateGreUsed><VxlanAddr/><Vxl  anPort/><CapwapPort/><GenevePort/><VxlangpealPort/><SdwanPort/><Co mment/><Ip>0.0.0.0</Ip></Adapter></PacketSource></Netlink></Element>



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml18864\wps13.jpg)![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml18864\wps14.png)

"

}

请求参数说明：

 

| ***\*参数\****   | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| ---------------- | ------------------ | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| <Netlink>        | 是                 | 无             | 无                                                           | 链路配置根节点                                               |
| id               | 是                 | Uint32         | 255                                                          | 表示链路 ID ，新增链路时 固定设置为 255。                    |
| type             | 是                 | Uint32         | l 1：标准分路器l 2：汇聚型分路器l 3：交换机单向流 量镜像l 4：交换机双向流 量镜像l 5：数据包回放l 6 ：Netflow 双向流 量l 8：Agentl 9：聚合分析 | 表示链路类型。                                               |
| name             | 是                 | String         | 无                                                           | 表示链路名称。                                               |
| sequence         | 是                 | 无             | 无                                                           | 表示序列号 ，新建链路  时，不用设置具体数值。                |
| <Storage>        | 是                 | String         | 无                                                           | 表示存储区 ID ，通过[“6.1](#bookmark178) [配置获取”](#bookmark178)接口获取存储  区 ID。 |
| <Totalbandwidth> | 是                 | Uint64         | 无                                                           | 表示总带宽，总带宽不大 于进网带宽与出网带宽之 和 ，不小于两者中的最大 者。单位：Mbps |
| <Inbandwidth>    | 是                 | Uint64         | 1-1000000                                                    | 表示进网带宽 ，单位： Mbps                                   |
| <Outbandwidth>   | 是                 | Uint64         | 1-1000000                                                    | 表示出网带宽 ，单位： Mbps                                   |
| <AnalysisOpts>   | 是                 | 无             | 无                                                           | 分析选项根节点 ，具体描  述请参见[“表格  1 分析选](#bookmark202) [项子节点 XML 说明”](#bookmark203)。 |
| <CompressOpts>   | 是                 | 无             | 无                                                           | 压缩选项根节点 ，具体描                                      |

 



 

| ***\*参数\****     | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| ------------------ | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
|                    |                    |                |                | 述请参见[“表格 2 压缩选](#bookmark204) [项子节点 XML 说明”](#bookmark205)。 |
| <ExportOptions>    | 是                 | 无             | 无             | 启用自动导出 CSV 格式数据根节点 ，具体描述请参  见[“表格 3  自动导出](#bookmark206)    [CSV 格式数据子节点 XML](#bookmark207) [说明”](#bookmark208)。 |
| <VnIdLayers>       | 是                 | 无             | 无             | 指定虚拟网统计层级和类型根节点，具体描述请参  见[“表格 4 虚拟网统计类](#bookmark209) [型和层级子节点 XML 说](#bookmark210)  [明”](#bookmark211)。 |
| <NametableOptions> | 是                 | 无             | 无             | 名字表选项根节点，具体  描述请参见[“表格 5 名字](#bookmark212) [表选项子节点 XML 说](#bookmark213)    [明”](#bookmark214)。 |
| <PacketSource>     | 是                 | 无             | 无             | 采集接口根节点，具体描 述请参见[“7.3.2 采集接](#bookmark50)  [口”](#bookmark50)。 |

 

 

表格  1 分析选项子节点 XML 说明

 

| ***\*参数\****    | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\****                             | ***\*说明\****                                               |
| ----------------- | ------------------ | -------------- | ------------------------------------------ | ------------------------------------------------------------ |
| <AnalysisOpts>    | 是                 | 无             | 无                                         | 分析选项根节点。                                             |
| <netflowsampling> | 是                 | Uint32         | 1                                          | 采样率，交换机镜像  流量链路不关注该字  段 ，固定值 1。对于  Netflow 类型链路，请 按照实际情况设置采  样率。 |
| <Millisecond>     | 是                 | Bool           | l false：不启用l true：启用                | 启用毫秒级流量统 计。                                        |
| <Switch>          | 是                 | Uint32         | l 1：ARISTAl 2：VSSMonitoringl 4 ：Gigamon | 设置使用哪种交换机 的时间戳。当 enable 设置为 1 时，按照交 换机对应的数字取 |

 



 

| ***\*参数\****   | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\****                               | ***\*说明\****                                               |
| ---------------- | ------------------ | -------------- | -------------------------------------------- | ------------------------------------------------------------ |
|                  |                    |                | l 8 ：HUAWEIl 32：TAP NTC                    | 值；当 enable 设置为0 时，取值为空。                         |
| enable           | 是                 | Bool           | l 0：不启用l 1：启用 默认值：0               | 是否启用交换机时间 戳分析。                                  |
| <TunnelLevel>    | 是                 | Uint8          | l 1：第一层l 2：第二层l 3：第三层l 4：第四层 | 设置 IP 统计层级。当 enable 设置为 1 时， 按照统计层级对应的  数字取值；当 enable 设置为 0 时，取值为 空。 |
| enable           | 是                 | Bool           | l 0：不启用l 1：启用 默认值：0               | 是否指定 IP 层级统计 层级。                                  |
| <MacTunnelLevel> | 是                 | Uint8          | l 1：第一层l 2：第二层l 3：第三层l 4：第四层 | 设置 MAC 统计层级。 当 enable 设置为 1   时，按照统计层级对  应的数字取值；当enable 设置为 0 时， 取值为空。 |
| enable           | 是                 | Bool           | l 0：不启用l 1：启用 默认值：0               | 是否指定 MAC 统计层 级。                                     |

 

 

表格 2 压缩选项子节点 XML 说明

 

| ***\*参数\****       | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\****                               | ***\*说明\****                         |
| -------------------- | ------------------ | -------------- | -------------------------------------------- | -------------------------------------- |
| <CompressOpts>       | 是                 | 无             | 无                                           | 压缩选项根节点。                       |
| <PacketCompressAlgo> | 是                 | Uint8          | l 0：不使用压缩算法l 1 ：lz4 算法 默认值： 1 | 表示数据包压缩算 法。                  |
| <StatCompressAlgo>   | 是                 | Uint8          | 1                                            | 表示统计数据压缩算 法 ，固定值 1，使用 |

 



 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\****   | ***\*说明\****              |
| -------------- | ------------------ | -------------- | ---------------- | --------------------------- |
|                |                    |                |                  | lz4 算法。                  |
| <Ratio>        | 是                 | Uint8          | 1-100 默认值：30 | 表示压缩率 ，推荐使 用 30。 |

 

 

表格 3  自动导出 CSV 格式数据子节点 XML 说明

 

| ***\*参数\****  | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\****                            | ***\*说明\****                   |
| --------------- | ------------------ | -------------- | ----------------------------------------- | -------------------------------- |
| <ExportOptions> | 是                 | 无             | 无                                        | 自动导出 CSV 格式数据根 节点。   |
| <EnableExport>  | 是                 | Bool           | l true：启用l false：不启用 默认值：false | 是否启用自动导出 CSV 格 式数据。 |

 

 

表格 4 虚拟网统计类型和层级子节点 XML 说明

 

| ***\*参数\****   | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| ---------------- | ------------------ | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| <VnIdLayers>     | 是                 | 无             | 无                                                           | 虚拟网统计类型和层级根 节点。                                |
| <PointVnidLayer> | 是                 | Bool           | l true：启用l false：不启用 默认值：false                    | 是否启用指定虚拟网统计 类型和层级。当启用该参数时，才需要设置<SelectedLayers>和 <SelectedType>。 |
| <SelectedLayers> | 是                 | Uint8          | l 1：最内层l 2：最外层l 4：最内两层l 5：最外两层             | 选择虚拟网的统计层级。                                       |
| <SelectedType>   | 是                 | Uint8          | l 0：不支持选择 虚拟网l 1：VLANl 2 ：MPLS VPNl 3：VXLANl 4： ISL VLANl 6 ：GRE | 选择虚拟网的类型。当虚 拟网类型为 GRE 时，虚 拟网的统计层级只能选择 最内层或最外层。 |

 



 

 

表格 5 名字表选项子节点 XML 说明

 

| ***\*参数\****      | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| ------------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| < NametableOptions> | 是                 | 无             | 无             | 名字表选项根节点。                                           |
| < SnmpEnable>       | 是                 | Bool           | 0              | 是否启用自动获取接口表， 1：（启用）0：（关 闭），交换机双向流量镜 像链路不关注该字段。 |
| < UpdatePeriod>     | 是                 | Uint8          | 0              | 名字表更新频率，开启自 动获取接口表后该字段才 有意义，交换机双向流量 镜像链路不关注该字段。 |
| < CoverageConfig>   | 是                 | Bool           | 0              | 与自定义名字表冲突时是 否覆盖， 1：（覆盖），0：（不覆盖），开启自  动获取接口表后该字段才 有意义，交换机双向流量 镜像链路不关注该字段。 |

 

 

***\*XML\**** ***\*说明\****

 

<Element handle='netlink'><Netlink id='255' type='4' name='net_link' sequence=''> <Storage>6</Storage><Totalbandwidth>1000</Totalbandwidth><Inbandwidth>1000</Inbandwidth><Outbandwidth>1000</Outbandwidth> <AnalysisOpts><netflowsampling>1</netflowsampling><Millisecond>0</Millisecond> <Switch enable='0'></Switch><TunnelLevel enable='0'>1</TunnelLevel><MacTunnelLevel enable='0'>1</MacTunnelLevel>

 



 

</AnalysisOpts> <CompressOpts><PacketCompressAlgo>1</PacketCompressAlgo> <StatCompressAlgo>1</StatCompressAlgo><Ratio>30</Ratio></CompressOpts> <ExportOptions><EnableExport>0</EnableExport></ExportOptions> <VnIdLayers><PointVnidLayer>0</PointVnidLayer> <SelectedLayers>1</SelectedLayers> <SelectedType>0</SelectedType></VnIdLayers><NametableOptions><SnmpEnable>0</SnmpEnable><UpdatePeriod>0</UpdatePeriod><CoverageConfig>0</CoverageConfig> </NametableOptions><PacketSource> <Adapter><DeviceId>ens34</DeviceId> <Medium>Ethernet</Medium><Tunnel>1</Tunnel><Direction>1</Direction><VxlanUsed>0</VxlanUsed><CapwapUsed>0</CapwapUsed> <GeneveUsed>0</GeneveUsed><VxlangpealUsed>0</VxlangpealUsed> <SdwanUsed>0</SdwanUsed><PrivateGreUsed>0</PrivateGreUsed>

 



 

<VxlanAddr/> <VxlanPort/> <CapwapPort/> <GenevePort/><VxlangpealPort/> <SdwanPort/><Comment/><Ip>[0.0.0.0](0.0.0.0)</Ip> </Adapter></PacketSource> </Netlink></Element>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.5.2\**** ***\*更新链路\****

 

***\*接口功能\****

 

该接口用于更新已添加链路的状态或参数。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，以更新“交换机双向流量镜像”链路运行状态为例：

param={



"type":"netlink", "child_type":"",

"xml": " <Element handle='netlink'><Netlink id='2'type='4' name='net_link' sequence=''><Storage>1</Storage><Totalbandwidth>1000</Totalbandwidth>  <Inbandwidth>1000</Inbandwidth><Outbandwidth>1000</Outbandwidth><St atus>running</Status> <AnalysisOpts><netflowsampling>1</netflowsampling>  <Millisecond>0</Millisecond><Switch enable='0'></Switch><TunnelLevel

enable='0'>1</TunnelLevel><MacTunnelLevel

enable='0'>1</MacTunnelLevel></AnalysisOpts><CompressOpts><PacketCom  pressAlgo>1</PacketCompressAlgo><StatCompressAlgo>1</StatCompressAlgo ><Ratio>30</Ratio></CompressOpts><ExportOptions><EnableExport>0</Ena bleExport></ExportOptions><VnIdLayers><PointVnidLayer>0</PointVnidLayer  ><SelectedLayers>1</SelectedLayers><SelectedType>0</SelectedType></VnId Layers><NametableOptions><SnmpEnable>0</SnmpEnable><UpdatePeriod>  0</UpdatePeriod><CoverageConfig>0</CoverageConfig></NametableOption  s><PacketSource><Adapter><DeviceId>ens34</DeviceId><Medium>Ethernet  </Medium><Tunnel>1</Tunnel><Direction>1</Direction><VxlanUsed>0</Vxl  anUsed><CapwapUsed>0</CapwapUsed><GeneveUsed>0</GeneveUsed><V  xlangpealUsed>0</VxlangpealUsed><SdwanUsed>0</SdwanUsed><PrivateGre Used>0</PrivateGreUsed><VxlanAddr/><VxlanPort/><CapwapPort/><Geneve Port/><VxlangpealPort/><SdwanPort/><Comment/><Ip>0.0.0.0 </Ip></Adapt  er></PacketSource></Netlink></Element>"

}

说明：新创建的链路是未运行状态，所以需要通过该接口更改链路为运行状态。

 

链路配置更新时，首先需要重新通过[“6.1 配置获取”](#bookmark178)接口重新获取链路的 ID。

然后才能更新指定链路的配置信息，如上述示例中，将链路 2 的 status 更新为 running 状态。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.5.3\**** ***\*删除链路\****



***\*接口功能\****

 

该接口用于删除链路。 说明：

在删除链路前，一定要先停止链路，否则会删除失败。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例： param={

"type":"netlink", "child_type":"",

"xml":"<Element handle='netlink'><Delete id='3'></Delete></Element>" }

请求参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| <Delete>       | 是                 | 无             | 无             | 配置删除根节点。                                             |
| id             | 是                 | Uint8          | 无             | 表示要删除的链路 ID，链路 ID 可以通过 [“6.1 配置获取”](#bookmark178)接口获取。 |

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.6\**** ***\*名字表\****

 

通过配置名字表，可以自定义 IP 地址别名、物理地址别名、VLAN 标识别名、



MPLS VPN 标签别名、VXLAN 标识别名、 ISL VLAN 标识别名、DSCP 标记别名、 交易名称别名 ，有利于用户更方便的识别和管理网络。

名字表类型与 type 的对应关系：

l 物理地址：mac

l IPv4 地址：ipv4

l IPv6 地址：ipv6

l VLAN 标识：vlan

l MPLS VPN 标签：mplsvpn

l VXLAN 标识：vxlan

l ISL VLAN 标识：islvlan

l DSCP 标记：dscp

l 交易名称：transaction_name

l 接口标识：netflow

 

***\*7.6.1\**** ***\*物理地址名字表\****

 

***\*接口功能\****

 

用于配置物理地址名字表的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置物理地址名字表： param={

"type":"nametable", "child_type":"",

"netlink":16,

"xml":"<Element handle='nametable' version='1' netlink_id='16'>



<Options resolvehostname='0' saveautoname='0' keepdates='2'/>

<NameTable>

<NameTableItem type='mac' alias='broadcast_mac' color='6789414' time='0' erasable='false' manual='1' source='1'

device=''>ff:ff:ff:ff:ff:ff</NameTableItem> </NameTable>

</Element>" }

请求参数说明：

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| <Options>                               | 是                            | 无             | 无                                                           | 名字表配置根节点。                                           |
| resolvehostname                         | 是                            | Bool           | l 0：关闭l 1：开启                                           | 是否启用自动解析主机名。                                     |
| saveautoname                            | 是                            | Bool           | l 0：关闭l 1：开启                                           | 是否保存自动解析的主机名。                                   |
| keepdates                               | 是                            | Uint32         | 默认值：2                                                    | 自动解析主机名保存的时间， 单位是天。该参数依赖“saveautoname”参数设置， 如果关闭了自动解析主机名，  则该字段设置将不会生效。 |
| <NameTable>                             | 是                            | 无             | 无                                                           | 名字表数据的根节点。                                         |
| < NameTableItem>                        | 是                            | 无             | 不同类型的名字表，其 对象格式也将不同，以 下为示例。l type ：ipv4，节点 值： [***********](***********)l type ：ipv6，节点 值：2001:3ef0::0001l type ：mac，节点 值：00:1C:54:09:A4:01l type：vlan，节点 值：23 | 名字表节点，用于设置名字表 的具体对象。                      |

 



 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
|                                         |                               |                | l type ：mplsvpn， 节点值：67l type：vxlan，节点 值：3001l type ：islvlan，节 点值： 10086l type ：dscp，节点 值：0l type：transaction_name ,节点值：sql-bargain |                                                              |
| id                                      | 否                            | Uint32         | 0(不包含) –4294967295(不包含)                                | 名字表唯一标识。新建名字表 时，如果 id 为空或 0，则由系 统自动分配。修改指定名字表 时，需要指定该名字表 id。 |
| type                                    | 是                            | String         | l ipv4l ipv6 l macl vlanl mplsvpnl vxlanl islvlanl dscpl transaction_name | 名字表的类型。                                               |
| alias                                   | 是                            | String         | 无                                                           | 显示的别名，无长度限制。                                     |
| color                                   | 是                            | Uint32         | 无                                                           | 名字表项显示的颜色，RGB值，只能是 10 进制整数，最高 字节表示红色的值，中间字节  表示绿色的值，最低字节表示  蓝色的值。 |
| time                                    | 是                            | Uint32         | 无                                                           | 名字表添加时间，用于定时清  除过期名字表，固定设置为 0。     |
| manual                                  | 是                            | Bool           | 1                                                            | 是否是手动添加。使用接口添 加时，值设置为 1。                |
| device                                  | 否                            | Uint128        | 无                                                           | netflow 设备地址 ，netflow 名 字表才需要配置                 |

 



***\*XML\**** ***\*说明\****

 

<Element handle='nametable' version='1' netlink_id='16'><Options resolvehostname='0' saveautoname='0' keepdates='2'/><NameTable><NameTableItem type='mac' alias='broadcast_mac'color='6789414' time='0' erasable='false' manual='1' source='1' device=''>ff:ff:ff:ff:ff:ff</NameTableItem></NameTable> </Element>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[7.2 配置接口说明中的](#bookmark44)[“响应消息”](#bookmark184)。

***\*7.6.2\**** ***\*IPv4\**** ***\*地址名字表\****

 

***\*接口功能\****

 

用于配置 IPv4 地址名字表的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置 IPv4 名字表： param={

"type":"nametable", "child_type":"",



"netlink":16,

"xml":"<Element handle='nametable' version='1' netlink_id='16'>

<Options resolvehostname='0' saveautoname='0' keepdates='2'/>

<NameTable>

<NameTableItem type='ipv4' alias='gateway' color='6789414' time='0' erasable='false' manual='1' source='1'

device=''>[19*********](19*********)</NameTableItem>

</NameTable> </Element>"

}

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.6.1 物理地址名字表”](#bookmark84)中[“XML 说明”](#bookmark215)。

配置 IPv4 地址名字表时，将 type 设置为 ipv4 ，< NameTableItem>节点中设置为 IPv4 类型的地址即可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.6.3\**** ***\*IPv6\**** ***\*地址名字表\****

 

***\*接口功能\****

 

用于配置 IPv6 地址名字表的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****



JSON 数据请求示例，配置 IPv6 名字表： param={

"type":"nametable", "child_type":"",

"netlink":1,

"xml":"<Element handle='nametable' version='1' netlink_id='1'>

<Options resolvehostname='0' saveautoname='0' keepdates='2'/>

<NameTable>

<NameTableItem type='ipv6' alias='ipv6- broadcast' color='16711680' time='0' erasable='false' manual='1'

device=''>ff02::1</NameTableItem>

</NameTable> </Element>"

}

***\*XML\**** ***\*说明\****

 

参见物理地址名字表 XML 说明。

XML 说明请参见[“7.6.1 物理地址名字表”](#bookmark84)中[“XML 说明”](#bookmark215)。

配置 IPv6 地址名字表时，将 type 设置为 ipv6 ，< NameTableItem>节点中设置为 IPv6 类型的地址即可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.6.4 VLAN\**** ***\*标识名字表\****

 

***\*接口功能\****

 

用于配置 VLAN 名字表的接口。

***\*请求方法\****

 

POST



***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置单层 VLAN 名字表： param={

"type":"nametable", "child_type":"",

"netlink":16,

"xml":"<Element handle='nametable' version='1' netlink_id='16'>

<Options resolvehostname='0' saveautoname='0' keepdates='2'/>

<NameTable>

<NameTableItem type='vlan' alias='vlan-3011' color='6789414' time='0' erasable='false' manual='1' source='1'

device=''>3011</NameTableItem>

</NameTable> </Element>"

}

JSON 示例，配置多层 VLAN 名字表： param={

"type":"nametable", "child_type":"",

"netlink":1,

"xml":"<Element handle='nametable' version='1' netlink_id='1'>

<Options resolvehostname='0' saveautoname='0' keepdates='2'/>

<NameTable>

<NameTableItem type='vlan' alias='multi-vlan' color='16711680' time='0' erasable='false' manual='1'

device=''>3016.2000</NameTableItem>

</NameTable> </Element>"



}

 

 

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.6.1 物理地址名字表”](#bookmark84)中[“XML 说明”](#bookmark215)。

配置 VLAN 标识名字表时，将 type 设置为vlan ，< NameTableItem>节点中设置为 VLAN 类型即可。

多层 Vlan 使用'.'将 2 个 VLANID 拼接，点号左边是内层 VLANID，点号右边是外层 VLANID。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.6.5\**** ***\*MPLS VPN\**** ***\*标签名字表\****

 

***\*接口功能\****

 

用于配置 MPLS VPN 标签名字表的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置单层 MPLS VPN 标签名字表： param={

"type":"nametable", "child_type":"",

"netlink":1,

"xml":"<Element handle='nametable' version='1' netlink_id='1'>



<Options resolvehostname='0' saveautoname='0' keepdates='2'/>

<NameTable>

<NameTableItem type='mplsvpn' alias='multi-vlan' color='16711680' time='0' erasable='false' manual='1'

device=''>3016</NameTableItem> </NameTable>

</Element>" }

JSON 示例，配置多层 MPLS VPN 标签名字表： param={

"type":"nametable", "child_type":"",

"netlink":1,

"xml":"<Element handle='nametable' version='1' netlink_id='1'>

<Options resolvehostname='0' saveautoname='0' keepdates='2'/>

<NameTable>

<NameTableItem type='mplsvpn' alias='multi-vlan' color='16711680' time='0' erasable='false' manual='1'

device=''>3016.2000</NameTableItem>

</NameTable> </Element>"

}

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.6.1 物理地址名字表”](#bookmark84)中[“XML 说明”](#bookmark215)。

配置 MPLS VPN 标签名字表时，将 type 设置为 mplsvpn ，< NameTableItem>节点 中设置为 MPLS VPN 类型即可。

多层 MPLS VPN 标签名字表时，使用'.'将 2 个 mpls vpn 拼接，点号左边是内层 MPLS VPN 标签，点号右边是外层 MPLS VPN 标签。

***\*响应消息\****



响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.6.6 VXLAN\**** ***\*标识名字表\****

 

***\*接口功能\****

 

用于配置 VXLAN 标识名字表的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置 VXLAN 标识名字表： param={

"type":"nametable", "child_type":"",

"netlink":1,

"xml":"<Element handle='nametable' version='1' netlink_id='1'>

<Options resolvehostname='0' saveautoname='0' keepdates='2'/>

<NameTable>

<NameTableItem type='vxlan' alias='vxlan-2011' color='16711680' time='0' erasable='false' manual='1'

device=''>2011</NameTableItem> </NameTable>

</Element>" }

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.6.1 物理地址名字表”](#bookmark84)中[“XML 说明”](#bookmark215)。



配置 VXLAN 标识名字表时，将 type 设置为vxlan ，< NameTableItem>节点中设置 为 VXLAN 类型即可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.6.7 ISL VLAN\**** ***\*标识名字表\****

 

***\*接口功能\****

 

用于配置 ISL VLAN 标识名字表的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置 ISL VLAN 标识名字表： param={

"type":"nametable", "child_type":"",

"netlink":1,

"xml":"<Element handle='nametable' version='1' netlink_id='1'>

<Options resolvehostname='0' saveautoname='0' keepdates='2'/>

<NameTable>

<NameTableItem type='islvlan' alias='islvlan-2011' color='16711680' time='0' erasable='false' manual='1'

device=''>2011</NameTableItem>

</NameTable> </Element>"



}

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.6.1 物理地址名字表”](#bookmark84)中[“XML 说明”](#bookmark215)。

配置 ISL VLAN 标识名字表时，将 type 设置为 islvlan ，< NameTableItem>节点中 设置为 ISL VLAN 类型即可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.6.8\**** ***\*DSCP\**** ***\*标记名字表\****

 

***\*接口功能\****

 

用于配置 DSCP 标记名字表的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置 DSCP 标记名字表： param={

"type":"nametable", "child_type":"",

"netlink":16,

"xml":"<Element handle='nametable' version='1' netlink_id='16'>

<Options resolvehostname='0' saveautoname='0' keepdates='2'/>

<NameTable>

<NameTableItem type='dscp' alias='dscp-1'



color='6789414' time='0' erasable='false' manual='1' source='1' device=''>1</NameTableItem>

</NameTable> </Element>"

}

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.6.1 物理地址名字表”](#bookmark84)中[“XML 说明”](#bookmark215)。

配置 DSCP 标识名字表时，将 type 设置为 dscp ，< NameTableItem>节点中设置为 DSCP 类型即可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.6.9\**** ***\*交易名称名字表\****

 

***\*接口功能\****

 

用于配置交易名称名字表的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置交易名称名字表： param={

"type":"nametable", "child_type":"",

"netlink":16,

"xml":"<Element handle='nametable' version='1' netlink_id='16'>



<Options resolvehostname='0' saveautoname='0' keepdates='2'/>

<NameTable>

<NameTableItem type='transaction_name'

alias='bank-bargain' color='6789414' time='0' erasable='false' manual='1' source='1' device=''>sql-bargain</NameTableItem>

</NameTable> </Element>"

}

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.6.1 物理地址名字表”](#bookmark84)中[“XML 说明”](#bookmark215)。

配置交易名称名字表时，将 type 设置为 transaction_name ，< NameTableItem>节 点中设置为交易名称即可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.6.10\**** ***\*接口标识名字表\****

 

***\*接口功能\****

 

用于配置接口标识名字表的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置接口标识名字表： param={



"type":"nametable", "child_type":"",

"netlink":5,

"xml":"<Element handle='nametable' version='1' netlink_id='5'>

<Options resolvehostname='0' saveautoname='0' keepdates='2'/>

<NameTable>

<NameTableItem type='netflow' alias='netflow- test' color='16711680' time='0' erasable='false' manual='1'

device=' [***********](***********)'>10</NameTableItem>

</NameTable> </Element>"

}

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.6.1 物理地址名字表”](#bookmark84)中[“XML 说明”](#bookmark215)。

配置接口标识名字表时，将 type 设置为 netflow ，< NameTableItem>节点中设置为 接口标识即可。

说明：

接口标识名字表，只有 netflow 链路才能配置

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.7\**** ***\*应用配置\****

 

通过接口可以配置标准应用、Web 应用、特征应用、加密应用、协议应用。 应用类型与 type 的对应关系：

l 标准应用： [1](#bookmark216)

l 特征值应用：[2](#bookmark217)

l WEB 应用：[3](#bookmark218)

l 加密应用：[4](#bookmark219)



l 协议应用：5

 

***\*7.7.1\**** ***\*标准应用\****

 

***\*接口功能\****

 

用于配置标准应用的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置标准应用：

param={

"type":"application", "child_type":"",

"netlink":16,

"xml":"<Element handle='application' version='1' netlink_id='16'>

<Application flags='1' library='0' name='my-app-test' type='1' priority='0'

monitor='true' goodtransrtt='10' normaltransrtt='30' badtransrtt='100'

enablepktcut='false' pktlimitlen='64'><Description/> <Pair ip1='[*************](*************)' port1='3000' ip2='[*************](*************)' port2='' protocol='1'/> <BargainGroup

enable='true' id='50003'/> </Application></Element>"

}

请求参数说明：

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****             |
| --------------------------------------- | ----------------------------- | -------------- | -------------- | -------------------------- |
| <Application>                           | 是                            | 无             | 无             | 应用配置根节点。           |
| id                                      | 否                            | Uint32         | 50000-55000    | 应用唯一标识。新建应用时， |

 



 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
|                                         |                               |                |                                                              | 如果 id 为空或 0，则由系统自 动分配。                        |
| flags                                   | 是                            | Uint64         | 无                                                           | 配置掩码，参见[7.1 配置格式](#bookmark180) [说明](#bookmark180) |
| library                                 | 是                            | Uint32         | 0                                                            | 库标志 ，用户自定义应用固定 为 0。                           |
| name                                    | 是                            | String         | 200                                                          | 应用名称，必须唯一，最多200 个 utf-8 字符。                  |
| type                                    | 是                            | Uint32         | l 标准应用： 1l 特征值应用：2l WEB 应用：3l 加密应用：4l 协议应用：5 | 应用类型。                                                   |
| priority                                | 是                            | Uint32         | 0                                                            | 固定配置为 0。                                               |
| monitor                                 | 是                            | Bool           | l true：启用l false：不启用                                  | 是否启用监控分析。                                           |
| goodtransrtt                            | 是                            | Uint32         | 默认值： 10                                                  | 好的交易响应时间，单位为毫 秒，响应时间小于或等于此值 的交易认为是好的交易。 |
| normaltransrtt                          | 是                            | Uint32         | 默认值：30                                                   | 一般的交易响应时间，单位为 毫秒，响应时间大于goodtransrtt，并且小于或等于 此值的交易认为是一般的交易。 |
| badtransrtt                             | 是                            | Uint32         | 默认值： 100                                                 | 差的交易响应时间，单位为毫 秒，响应时间大于normaltransrtt,并且小于或等于 此值的交易认为是一般的交易。大于此值的交易认为是很 差的交易。 |
| enablepktcut                            | 是                            | Bool           | l true：启用l false：不启用                                  | 是否启用应用的剪裁数据包                                     |
| pktlimitlen                             | 是                            | Uint32         | 取值范围 64-65535单位：B，默认值：64                         | 数据包裁减后的最大长度，enablepktcut 启用该参数设置 才有效。 |

 



 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                            | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ----------------------------------------- | ------------------------------------------------------------ |
| <Description>                           | 否                            | String         | 无                                        | 应用描述，无长度限制。                                       |
| <Pair>                                  | 是                            | 无             | 无                                        | 标准应用中的规则配置，一条  规则由 5 元组（ip1, port1, ip2, port2,protocol）配置，一个应  用中支持配置多条规则。 |
| ip1                                     | 是                            | String         | 无                                        | 服务器 IP 地址。多个 IP 地址 用“ ,”分隔，范围用“-”分 隔，网段用“/”分隔。 |
| port1                                   | 是                            | String         | 1-65535                                   | 服务器端口。多个端口用“ ,” 分隔，范围用“-”分隔。             |
| ip2                                     | 是                            | String         | 无                                        | 客户端 IP 地址。多个 IP 地址 用“ ,”分隔，范围用“-”分 隔，网段用“/”分隔。 |
| port2                                   | 是                            | String         | 1-65535                                   | 客户端端口。多个端口用“ ,” 分隔，范围用“-”分隔。             |
| protocol                                | 是                            | Uint8          | l 0x01：TCPl 0x02 ：UDPl 0x03：TCP 和 UDP | 协议掩码。                                                   |
| reserve                                 | 是                            | Bool           | l 0：关闭l 1：开启                        | 是否为反向链接，设置为反向 链接后，进行应用数据统计时，系统将会把客户端作为服 务器统计。 |
| < BargainGroup>                         | 否                            | 无             | 无                                        | 应用的关联交易配置。                                         |
| enable                                  | 否                            | Bool           | l true：启用l false：不启用               | 表示该应用是否启用交易分 析。                                |
| id                                      | 否                            | Uint32         | 无                                        | 表示被关联的交易 ID ，一个应 用只能关联一个交易。可通过 [“6.1 配置获取”](#bookmark178)获取到交易  ID。 |

 

 

***\*XML\**** ***\*说明\****



 

<Element handle='application' version='1' netlink_id='16'><Application flags='1' library='0' name='app-test' type='1'priority='0' monitor='true' goodtransrtt='10' normaltransrtt='30' badtransrtt='100' enablepktcut='false' pktlimitlen='64'><Description/><Pair ip1='*************' port1='3000' ip2='*************' port2='' protocol='1' reserve='0'/></Application> </Element>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.7.2\**** ***\*特征值应用\****

 

***\*接口功能\****

 

用于配置特征值应用的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置特征值应用：

param={

<Element handle='application' version='1' netlink_id='16'>

<Application flags='1' library='0' name='signature-app' type='2' priority='0' monitor='true' goodtransrtt='10' normaltransrtt='30' badtransrtt='100'



enablepktcut='false' pktlimitlen='64'> <Description/>

<IPRule enable='true' ip='[***********](***********)' port='443'/> <Signature>

<Pattern protocol='3'type='ASC II'>alibaba</Pattern>

<Pattern protocol='3'type='HEX'>FF FF FF FF FF FF</Pattern> </Signature>

</Application> </Element>

}

请求参数说明：

<Application>、<Description>、<BargainGroup>节点的参数配置和说明请参见

[“7.7.1 标准应用”](#bookmark183)中的[“XML 说明”](#bookmark220)，下面表格中只描述特征值应用特有的节点 和参数。

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                         | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | -------------------------------------- | ------------------------------------------------------------ |
| <IPRule>节点                            | 是                            | 无             | 无                                     | IP 规则节点。                                                |
| enable                                  | 是                            | Bool           | l true：启用l false：关闭 默认值：关闭 | 是否启用 IP 规则配置。                                       |
| ip                                      | 否                            | Uint128        | 无                                     | IP 规则中的 IP 地址，当启用了 IP 规则配置时，才需要配置该 字段。 |
| port                                    | 否                            | Uint16         | 1-65535                                | IP 规则中的端口，当启用了 IP 规则配置时，才需要配置该字  段。 |
| <Signature>                             | 是                            | 无             | 无                                     | 配置特征值应用规则，一条规 则配置在一个<Pattern>节点  中 ，一个应用中支持配置多条 规则，多条规则对应多个<Pattern>节点。 |

 



 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                            | ***\*说明\****   |
| --------------------------------------- | ----------------------------- | -------------- | ----------------------------------------- | ---------------- |
| <Pattern>                               | 是                            | String         | 无                                        | 设置具体的特征。 |
| prototcol                               | 是                            | Uint8          | l 0x01：TCPl 0x02 ：UDPl 0x03：TCP 和 UDP | 协议掩码。       |
| type                                    | 是                            | String         | l ASC II：ASC 字符 串l HEX：十六进制      | 特征值类型。     |

 

 

***\*XML\**** ***\*说明\****

 

<Element handle='application' version='1' netlink_id='16'><Application flags='1' library='0' name='signature-app' type='2' priority='0' monitor='true' goodtransrtt='10'normaltransrtt='30' badtransrtt='100' enablepktcut='false' pktlimitlen='64'><Description/><IPRule enable='true' ip='***********' port='443'/> <Signature><Pattern protocol='3' type='ASC II'>alibaba</Pattern><Pattern protocol='3' type='HEX'>FF FF FF FF FF FF</Pattern></Signature> </Application></Element>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.7.3 WEB\**** ***\*应用\****



***\*接口功能\****

 

用于配置 WEB 应用的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置 WEB 应用：

param={

"type":"application", "child_type":"",

"netlink":1,

"xml":"<Element handle='application' version='1' netlink_id='16'>

<Application flags='1' library='0' name='web-app' type='3' priority='0'

monitor='true' goodtransrtt='10' normaltransrtt='30' badtransrtt='100'

enablepktcut='false' pktlimitlen='64'><Description/> <IPRule enable='true' ip='[***********](***********)' port='443'/>

<Host>[***********](***********) </Host><Url>/Login</Url><BargainGroup enable='false' id='0'/> </Application></Element>"

}

请求参数说明：

<Application>、<Description>、<BargainGroup>节点的参数配置和说明请参见

[“7.7.1 标准应用”](#bookmark183)中的[“XML 说明”](#bookmark220)，下面表格中只描述 WEB 应用特有的节点和 参数。

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\**** |
| --------------------------------------- | ----------------------------- | -------------- | -------------- | -------------- |
| <IPRule>节点                            | 是                            | 无             | 无             | IP 规则节点。  |

 



 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                         | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | -------------------------------------- | ------------------------------------------------------------ |
| enable                                  | 是                            | Bool           | l true：启用l false：关闭 默认值：关闭 | 是否启用 IP 规则配置。                                       |
| ip                                      | 否                            | Uint128        | 无                                     | IP 规则中的 IP 地址，当启用了 IP 规则配置时，才需要配置该 字段。 |
| port                                    | 否                            | Uint16         | 1-65535                                | IP 规则中的端口，当启用了 IP 规则配置时，才需要配置该字  段。 |
| <Host>                                  | 是                            | String         | 无                                     | 设置 HTTP 主机，支持输入域  名或 IP 或 IP:PORT，多个主机 用英文半角逗号分隔。 |
| <Url>                                   | 是                            | String         | 无                                     | 设置 HTTP 路径，多个路径用英文半角逗号分隔，如： /Login,/API,/HOME |

 

 

***\*XML\**** ***\*说明\****

 

<Element handle='application' version='1' netlink_id='16'><Application flags='1' library='0' name='web-app' type='3'priority='0' monitor='true' goodtransrtt='10' normaltransrtt='30' badtransrtt='100' enablepktcut='false' pktlimitlen='64'><Description/><IPRule enable='true' ip='***********' port='443'/> <Host>[***********](***********)</Host><Url>/Login</Url><BargainGroup enable='false' id='0'/> </Application></Element>

 

 

***\*响应消息\****



响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.7.4\**** ***\*加密应用\****

 

***\*接口功能\****

 

用于配置加密应用的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置加密应用：

param={

"type":"application", "child_type":"",

"netlink":16,

"xml":"<Element handle='application' version='1' netlink_id='16'>

<Application flags='1' library='0' name='ssl-app' type='4' priority='0'

monitor='true' goodtransrtt='10' normaltransrtt='30' badtransrtt='100'

enablepktcut='false' pktlimitlen='64'><Description/> <Pair ip1='[***********](***********)' port1='443' ip2='[*************](*************)' port2='' protocol='3'

reverse='0'/><BargainGroup enable='false' id='0'/><PrivateKey describe='RAS private key' key='123456789' passward='' keyfilename='private.key'/>

</Application></Element>" }

请求参数说明：

<Application>、<Description>、<BargainGroup>节点的参数配置和说明请参见

[“7.7.1 标准应用”](#bookmark183)中的[“XML 说明”](#bookmark220)，下面表格中只描述加密应用特有的节点和



参数。

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                            | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ----------------------------------------- | ------------------------------------------------------------ |
| <Pair>                                  | 是                            | 无             | 无                                        | 标准应用中的规则配置，一条  规则由 5 元组（ip1, port1, ip2, port2,protocol）配置，一个应  用中支持配置多条规则。 |
| ip1                                     | 是                            | String         | 无                                        | 服务器 IP 地址。多个 IP 地址 用“ ,”分隔，范围用“-”分 隔，网段用“/”分隔。 |
| port1                                   | 是                            | String         | 1-65535                                   | 服务器端口。多个端口用“ ,” 分隔，范围用“-”分隔。             |
| ip2                                     | 是                            | String         | 无                                        | 客户端 IP 地址。多个 IP 地址 用“ ,”分隔，范围用“-”分 隔，网段用“/”分隔。 |
| port2                                   | 是                            | String         | 1-65535                                   | 客户端端口。多个端口用“ ,” 分隔，范围用“-”分隔。             |
| protocol                                | 是                            | Uint8          | l 0x01：TCPl 0x02 ：UDPl 0x03：TCP 和 UDP | 协议掩码。                                                   |
| reserve                                 | 是                            | Bool           | l 0：关闭l 1：开启                        | 是否为反向链接，设置为反向 链接后，进行应用数据统计时，系统将会把客户端作为服 务器统计。 |
| <PrivateKey>子节 点                     | 是                            | 无             | 无                                        | 加密信息根节点。                                             |
| describe                                | 是                            | String         | 无                                        | 加密信息的描述信息。                                         |
| key                                     | 是                            | String         | 无                                        | 密钥文件内容。                                               |
| passward                                | 否                            | String         | 无                                        | 加密密码，该参数不用设置。                                   |
| keyfilename                             | 是                            | String         | 无                                        | 密钥文件名称。                                               |

 

 

***\*XML\**** ***\*说明\****

 

<Element handle='application' version='1' netlink_id='16'>

 



 

<Application flags='1' library='0' name='ssl-app' type='4'priority='0' monitor='true' goodtransrtt='10' normaltransrtt='30' badtransrtt='100' enablepktcut='false' pktlimitlen='64'><Description/><Pair ip1='***********' port1='443' ip2='*************' port2='' protocol='3' reverse='0'/><BargainGroup enable='false' id='0'/><PrivateKey describe='RAS private key' key='123456789' passward='' keyfilename='private.key'/></Application> </Element>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.7.5\**** ***\*协议应用\****

 

***\*接口功能\****

 

用于配置协议应用的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置协议应用：

param={

"type":"application", "child_type":"",



"netlink":1,

"xml":" <Element handle='application' version='1' netlink_id='1'>

<Application flags='1' library='0' name='signature-app' type='5' priority='0' monitor='true' goodtransrtt='10' normaltransrtt='30' badtransrtt='100'

enablepktcut='false' pktlimitlen='64'>

<Description/>

<IPRule enable='true' ip='[***********](***********)' protocol='803'/> <BargainGroup enable="false" id="0"/>

</Application> </Element>"

}

请求参数说明：

<Application>、<Description>、<BargainGroup>节点的参数配置和说明请参见

[“7.7.1 标准应用”](#bookmark183)中的[“XML 说明”](#bookmark220)，下面表格中只描述协议应用特有的节点和 参数。

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                         | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | -------------------------------------- | ------------------------------------------------------------ |
| <IPRule>节点                            | 是                            | 无             | 无                                     | IP 规则节点。                                                |
| enable                                  | 是                            | Bool           | l true：启用l false：关闭 默认值：关闭 | 是否启用 IP 规则配置。                                       |
| ip                                      | 否                            | Uint128        | 无                                     | IP 规则中的 IP 地址，当启用了 IP 规则配置时，才需要配置该 字段。 |
| protocol                                | 是                            | Uint32         |                                        | 设置协议对应的 ID，协议应用只支持设置应用层的协议。协议 ID 可以通过[“5.2 网络协议](#bookmark32) [信息”](#bookmark32)查询获取。 |

 

 

***\*XML\**** ***\*说明\****



 

<Element handle='application' version='1' netlink_id='1'><Application flags='1' library='0' name='signature-app' type='5' priority='0' monitor='true' goodtransrtt='10'normaltransrtt='30' badtransrtt='100' enablepktcut='false' pktlimitlen='64'><Description/><IPRule enable='true' ip='***********' protocol='803'/> <BargainGroup enable="false" id="0"/></Application> </Element>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.8\**** ***\*网段配置\****

 

网段配置接口主要是用于配置链路的进出网网段和自定义网段。

 

***\*7.8.1\**** ***\*进出网网段\****

 

***\*接口功能\****

 

用于配置链路的进出网网段的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置进出网网段：



param={

"type":"innersegment", "child_type":"",

"netlink":16,

"xml":"<Element handle='innersegment' version='1'

netlink_id='16'><NetSegment><AnalysisRule><Entry>[************](************) </Entry></An alysisRule></NetSegment></Element>"

}

请求参数说明：

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| <Netsegment>                            | 是                            | 无             | 无                                                           | 网段配置节点。                                               |
| <AnalysisRule>                          | 是                            | 无             | 无                                                           | 网段规则的父节点。                                           |
| <Entry>                                 | 是                            | String         | 支持以下几种格式：l IP 地址/掩码 如：[***********](***********)/24 或[***********](***********)/255.255.255 .0 或 2001:3ef0::/80l IP 地址范围 如： [***********](***********)-[*************](*************) 或 2001:3ef0::0001- 2001:3ef0::0005l 单个 IP 地址  如： [*************](*************) 或 2001:3ef0::0001l 单个 MAC 地址 如：00:16:EA:AE:3C:40 或 00-16-EA-AE-3C-40 | 网段具体规则，如有多个 规则，则需要添加多个<Entry>节点。 多个规则间 IP、MAC 允 许有交叉。 |

 

 

***\*XML\**** ***\*说明\****

 

<Element handle='innersegment' version='1' netlink_id='16'> <NetSegment>

 



 

<AnalysisRule><Entry>[************](************)</Entry></AnalysisRule> </NetSegment></Element>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.8.2\**** ***\*自定义网段\****

 

***\*接口功能\****

 

用于配置链路自定义网段的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置自定义网段：

param={

"type":"statsegment", "child_type":"",

"netlink":16,

"xml":"<Element handle='statsegment' version='1'

netlink_id='16'><NetSegment flags='1' name='self-segment' total_band='1000' in_band='1000' out_band='1000'><Location/><Description/><Type>子网掩码



</Type><AnalysisRule><Entry>[***********](***********)/24</Entry></AnalysisRule> </NetSe gment></Element>"

}

请求参数说明：

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| <Netsegment>                            | 是                            | 无             | 无                                                           | 网段配置根节点。                                             |
| id                                      | 否                            | Uint32         | 50000（不包含）-65102 （不包含）                             | 网段的唯一标识 ，新建网段 时，id 为空或 0，则由系统 自动分配。 |
| flags                                   | 是                            | Uint64         | 无                                                           | 配置掩码，具体配置方式请 参见[“7.1 配置格式说](#bookmark180)     [明”](#bookmark180)***\*。\**** |
| name                                    | 是                            | String         | 无                                                           | 网段名称，必须唯一。                                         |
| total_band                              | 是                            | Uint32         | 1-1000000，且<=网段所 在链路的总带宽。                       | 网段总带宽，单位默认为Mbps，配置时不需要指定单 位。          |
| in_band                                 | 是                            | Uint32         | 1-1000000，且<=网段所 在链路的进网带宽。                     | 网段进网带宽 ，单位默认为 Mbps，配置时不需要指定单 位。      |
| out_band                                | 是                            | Uint32         | 1-1000000，且<=网段所 在链路的出网带宽。                     | 网段出网带宽，单位默认为 Mbps，配置时不需要指定单 位。       |
| <Description>                           | 否                            | String         | 无                                                           | 网段描述信息。                                               |
| <Location>                              | 否                            | String         | 无                                                           | 网段地理位置信息。                                           |
| <Type>                                  | 否                            | String         | 无                                                           | 网段类型，用户自定义。                                       |
| <AnalysisRule>                          | 是                            | 无             | 无                                                           | 网段规则的父节点。                                           |
| <Entry>                                 | 是                            | String         | 网段支持 3 种格式。l 地址/掩码，如：[***********](***********)/24 或[***********](***********)/255.255. 255.0 或FE80:0:0:CD30::/60l 地址范围，如：[19*********](19*********)-[19*********00](19*********00) 或 | 网段具体规则，如有多个规则，则需要添加多个 <Entry>节点。多个规则间 IP 范围允许有交 叉。 |

 



 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\**** |
| --------------------------------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | -------------- |
|                                         |                               |                | FE80::C0A8:0164- FE80::C0A8:0170l 单独 IP 地址，如：[19*********](19*********) 或FE80::24D1:FE6D:C0 A8:0164 |                |

 

 

注意：同一个网段内的规则不可以交叉和包含。多个规则多个<Entry>节点。

 

 

***\*XML\**** ***\*说明\****

 

<Element handle='statsegment' version='1'netlink_id='16'><NetSegment flags='1' name='self-segment' total_band='1000'in_band='1000' out_band='1000'> <Location/><Description/><Type>子网掩码</Type> <AnalysisRule><Entry>***********/24</Entry> </AnalysisRule></NetSegment> </Element>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.9\**** ***\*子链路配置\****

 

通过接口，可以配置类型为 VLAN、 ISL VLAN、VXLAN、MPLS VPN、GRE，

OPCODE、MAC 地址、NetFlow 标识和网段的子链路，有利于用户更方便的管理  和监控网络环境中的流量。其中 GRE 子链路和 OPCODE 子链路要 RAS6.4.1 及以



后版本才支持。

子链路类型与 type 的对应关系：

l VLAN 子链路： [1](#bookmark221)

l ISL VLAN 子链路：[2](#bookmark222)

l VXLAN 子链路：[3](#bookmark223)

l MPLS VPN 子链路：[4](#bookmark224)

l 网段子链路：[5](#bookmark225)

l MAC 子链路：[6](#bookmark226)

l Netflow 标识子链路：[7](#bookmark227)

l GRE 子链路：[8](#bookmark228)

l OPCODE 子链路：[9](#bookmark229)

 

***\*7.9.1 VLAN\**** ***\*子链路\****

 

***\*接口功能\****

 

用于配置 VLAN 子链路的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置单层 VLAN子链路： param={

"type":"subNetlink", "child_type":"",

"netlink":16,

"xml":"<Element handle='subNetlink' version='1'

netlink_id='16'><subNetlink  flags='1' type='1' name='vlan-2000- subnetlink' ident='2000' device='' inBand='1000' outBand='1000'

totalBand='1000' lowCongestion='20.00' middleCongestion='50.00' highCongestion='90.00'/></Element>"



}

JSON 示例，配置多层 VLAN子链路： param={

"type":"subNetlink", "child_type":"",

"netlink":16,

"xml":"<Element handle='subNetlink' version='1'

netlink_id='16'><subNetlink  flags='1' type='1' name='mult-vlan-subnetlink' ident='2011.3016' device='' inBand='1000' outBand='1000' totalBand='1000' lowCongestion='20.00' middleCongestion='50.00'

highCongestion='90.00'/></Element>" }

请求参数说明：

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| <subNetlink>                            | 是                            | 无             | 无                                                           | 表示子链路配置的根节点。                                     |
| id                                      | 否                            | Uint32         | 1（不包含）-30000（不包 含）                                 | 网段的唯一标识。新建子链路 时，如果 id 为空或 0，则由系 统自动分配。 |
| flags                                   | 是                            | Uint64         | 无                                                           | 配置掩码。参见[“7.1 配置格](#bookmark180) [式说明”](#bookmark180)。 |
| type                                    | 是                            | Uint8          | l 1：VLAN 子链路l 2： ISL VLAN 子链路l 3：VXLAN 子链路l 4 ：MPLS VPN 子链路l 5：网段子链路l 6 ：MAC 子链路l 7 ：Netflow 标识子链路l 8 ：GRE 子链路l 9 ：OPCODE 子链路 | 子链路类型。                                                 |
| name                                    | 是                            | String         | 无                                                           | 子链路名称，子链路名称不允 许重复。                          |
| ident                                   | 是                            | String         | 无                                                           | 子链路标识，针对不同类型的 子链路，ident 配置不同。对  于 VLAN 子链路来说，多个标 |

 



 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                            | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ----------------------------------------- | ------------------------------------------------------------ |
|                                         |                               |                |                                           | 识使用英文逗号分隔。 可配置多层 VLAN，内存 ID. 外层 ID，例如：2011.3016。 |
| device                                  | 否                            | String         | 无                                        | 设备 IP 地址，仅配置 Netflow 子链路会使用该字段。其余子 链路配置时保留为空字段。 |
| totalBand                               | 是                            | Uint32         | 1-1000000，且<=子链路 所在链路的总带宽    | 总带宽，单位为：Mbps。                                       |
| inBand                                  | 是                            | Uint32         | 1-1000000，且<=子链路 所在链路的进网带宽  | 进网带宽，单位为：Mbps。                                     |
| outBand                                 | 是                            | Uint32         | [1-1000000，且<=子链路 所在链路的进网带宽 | 出网带宽，单位为：Mbps。                                     |
| lowCongestion                           | 是                            | Float          | 0-100，且<middleCongestion                | 拥塞评估，非常空闲与空闲的 临界值，单位为：%。               |
| middleCongestion                        | 是                            | Float          | 0-100，且< highCongestion                 | 拥塞评估，空闲与中度拥塞的临界值取值范围，单位 为：%。       |
| highCongestion                          | 是                            | Float          | 0-100，且>middleCongestion                | 取值范围拥塞评估，中度拥塞 与严重拥塞的临界值，单位为：%。   |

 

 

***\*XML\**** ***\*说明\****

 

<Element handle='subNetlink' version='1' netlink_id='16'><subNetlink flags='1' type='1' name='vlan-2000-subnetlink'ident='2000' device='' inBand='1000' outBand='1000'totalBand='1000' lowCongestion='20.00' middleCongestion='50.00' highCongestion='90.00'/></Element>

 

 

***\*响应消息\****



响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.9.2\**** ***\*ISL VLAN\**** ***\*子链路\****

 

***\*接口功能\****

 

用于配置 ISL VLAN 子链路的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置单层 ISL VLAN 子链路： param={

"type":"subNetlink", "child_type":"",

"netlink":16,

"xml":"<Element handle='subNetlink' version='1'

netlink_id='16'><subNetlink  flags='1' type='2' name='islvlan-2000- subnetlink' ident='2000' device='' inBand='1000' outBand='1000'

totalBand='1000' lowCongestion='20.00' middleCongestion='50.00' highCongestion='90.00'/></Element>"

}

JSON 示例，配置多层 ISL VLAN 子链路： param={

"type":"subNetlink", "child_type":"",

"netlink":16,

"xml":"<Element handle='subNetlink' version='1'

netlink_id='16'><subNetlink  flags='1' type='2' name='mult-islvlan-  subnetlink' ident='2000.3016' device='' inBand='1000' outBand='1000' totalBand='1000' lowCongestion='20.00' middleCongestion='50.00'



highCongestion='90.00'/></Element>" }

请求参数说明：

配置 ISL VLAN 子链路时，“type”和“ident”参数说明如下，其他参数说明请参

见[“7.9.1 VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| --------------------------------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| type                                    | 是                 | Uint8          | 2              | 子链路类型。                                                 |
| ident                                   | 是                 | String         | 无             | 子链路标识。对于 ISL VLAN 子链路来 说，多个标识使用英文逗号分隔。可配置多层 ISL VLAN，内存 ID.外层 ID，例如：2011.3016。 |

 

 

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.9.1VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

配置 ISL VLAN 子链路时，将 type 设置为 2 ，ident 设置为 ISL VLAN 标识即可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.9.3 VXLAN\**** ***\*子链路\****

 

***\*接口功能\****

 

用于配置 VXLAN 子链路的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting



***\*请求消息\****

 

JSON 示例，配置 VXLAN子链路： param={

"type":"subNetlink", "child_type":"",

"netlink":16,

"xml":"<Element handle='subNetlink' version='1'

netlink_id='16'><subNetlink  flags='1' type='3' name='vxlan-2000- subnetlink' ident='2000' device='' inBand='1000' outBand='1000'

totalBand='1000' lowCongestion='20.00' middleCongestion='50.00' highCongestion='90.00'/></Element>"

}

请求参数说明：

配置 VXLAN 子链路时，“type”和“ident”参数说明如下，其他参数说明请参见

[“7.9.1 VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| --------------------------------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| type                                    | 是                 | Uint8          | 3              | 子链路类型。                                                 |
| ident                                   | 是                 | String         | 无             | 子链路标识。对于 VXLAN 子链路来说， 多个标识使用英文逗号分隔。 |

 

 

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.9.1VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

配置 VXLAN 子链路时，将 type 设置为 3 ，ident 设置为 VXLAN 标识即可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.9.4\**** ***\*MPLS VPN\**** ***\*子链路\****

 

***\*接口功能\****



用于配置 MPLS VPN 子链路的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置单层 MPLS VPN 子链路： param={

"type":"subNetlink", "child_type":"",

"netlink":16,

"xml":"<Element handle='subNetlink' version='1'

netlink_id='16'><subNetlink  flags='1' type='4' name='mpls-2000- subnetlink' ident='2000' device='' inBand='1000' outBand='1000'

totalBand='1000' lowCongestion='20.00' middleCongestion='50.00' highCongestion='90.00'/></Element>"

}

JSON 示例，配置多层 MPLS VPN 子链路： param={

"type":"subNetlink", "child_type":"",

"netlink":16,

"xml":"<Element handle='subNetlink' version='1'

netlink_id='16'><subNetlink  flags='1' type='4' name='mult-mpls-

subnetlink' ident='2000.3016' device='' inBand='1000' outBand='1000' totalBand='1000' lowCongestion='20.00' middleCongestion='50.00'

highCongestion='90.00'/></Element>"

}

请求参数说明：

配置 MPLS VPN 子链路时，“type”和“ident”参数说明如下，其他参数说明请参

见[“7.9.1 VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。



 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| --------------------------------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| type                                    | 是                 | Uint8          | 4              | 子链路类型。                                                 |
| ident                                   | 是                 | String         | 无             | 子链路标识。对于 MPLS VPN 子链路来 说，多个标识使用英文逗号分隔。可配置多层 mpls vpn 标签，内存 ID.外 层 ID，例如：2011.3016。 |

 

 

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.9.1VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

配置 MPLS VPN 子链路时，将 type 设置为 4 ，ident 设置为 MPLS VPN 标签即 可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.9.5\**** ***\*网段子链路\****

 

***\*接口功能\****

 

用于配置网段子链路的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置网段子链路： param={

"type":"subNetlink",



"child_type":"", "netlink":16,

"xml":"<Element handle='subNetlink' version='1'

netlink_id='16'><subNetlink flags='1' type='5' name='segment-subnetlink' ident='50001' device='' inBand='1000' outBand='1000' totalBand='1000'

lowCongestion='20.00' middleCongestion='50.00'

highCongestion='90.00'/></Element>" }

请求参数说明：

配置网段子链路时，“type”和“ident”参数说明如下，其他参数说明请参见

[“7.9.1 VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| --------------------------------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| type                                    | 是                 | Uint8          | 5              | 子链路类型。                                                 |
| ident                                   | 是                 | String         | 无             | 子链路标识。一条网段子链路只能配置 1 个网段 ID。网段 ID 通过[“6.1 配置获](#bookmark178)   [取”](#bookmark178)接口获取。 |

 

 

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.9.1VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

配置网段子链路时，将 type 设置为 5 ，ident 设置为网段 ID 即可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.9.6\**** ***\*MAC\**** ***\*子链路\****

 

***\*接口功能\****

 

用于配置 MAC 子链路的接口。

***\*请求方法\****

 

POST



***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置 MAC 子链路： param={

"type":"subNetlink", "child_type":"",

"netlink":16,

"xml":"<Element handle='subNetlink' version='1'

netlink_id='16'><subNetlink  flags='1' type='6' name='mac-subnetlink' ident=' 00:50:56:2f:72:1e' device='' inBand='1000' outBand='1000'

totalBand='1000' lowCongestion='20.00' middleCongestion='50.00' highCongestion='90.00'/></Element>"

}

请求参数说明：

配置 MAC 子链路时，“type”和“ident”参数说明如下，其他参数说明请参见

[“7.9.1 VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| --------------------------------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| type                                    | 是                 | Uint8          | 6              | 子链路类型。                                                 |
| ident                                   | 是                 | String         | 无             | 子链路标识。一条 MAC 子链路可以配置 多个 MAC 地址，多个 MAC 地址之间用 英文逗号分隔。 |

 

 

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.9.1VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

配置 MAC 子链路时，将 type 设置为 6 ，ident 设置为 MAC 地址即可。

 

 

***\*响应消息\****

响应消息的格式和说明请参见[7.2 配置接口说明中的](#bookmark44)[“响应消息”](#bookmark184)。



***\*7.9.7\**** ***\*Netflow\**** ***\*标识子链路\****

 

***\*接口功能\****

 

用于配置 Netflow 标识子链路的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置 Netflow 标识子链路： param={

"type":"subNetlink", "child_type":"",

"netlink":5,

"xml":"<Element handle='subNetlink' version='1'

netlink_id='5'><subNetlink flags='1' type='7' name='netflow-subnetlink'

ident='10' device=' [***********](***********)' inBand='1000' outBand='1000'

totalBand='1000' lowCongestion='20.00' middleCongestion='50.00' highCongestion='90.00'/></Element>"

}

请求参数说明：

配置 Netflow 标识子链路时，“type”、“ident”和“device”参数说明如下，其

他参数说明请参见[“7.9.1 VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| --------------------------------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| type                                    | 是                 | Uint8          | 7              | 子链路类型。                                                 |
| ident                                   | 是                 | String         | 无             | 子链路标识。对于 Netflow 标识子链路来 说，多个标识使用英文逗号分隔。 |
| device                                  | 是                 | Unit128        | 无             | 设备 IP 地址，只支持配置 1 个 IP 地 址。                     |

说明：只有在 Netflow 类型的链路下才能创建 Netflow 标识子链路。



 

 

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.9.1VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

配置 Netflow 标识子链路时，将 type 设置为 7 ，ident 设置为 Netflow 标识，device 设置为设备 IP 地址即可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.9.8 GRE\**** ***\*子链路\****

 

***\*接口功能\****

 

用于配置 GRE 子链路的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置 GRE 子链路： param={

"type":"subNetlink", "child_type":"",

"netlink":16,

"xml":"<Element handle='subNetlink' version='1'

netlink_id='16'><subNetlink  flags='1' type='8' name='gre-2000-subnetlink' ident='2000' device='' inBand='1000' outBand='1000' totalBand='1000'

lowCongestion='20.00' middleCongestion='50.00'

highCongestion='90.00'/></Element>" }



请求参数说明：

配置 GRE 子链路时，“type”和“ident”参数说明如下，其他参数说明请参见

[“7.9.1 VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| --------------------------------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| type                                    | 是                 | Uint8          | 8              | 子链路类型。                                                 |
| ident                                   | 是                 | String         | 无             | 子链路标识。对于 GRE 子链路来说， 多个标识使用英文逗号分隔。 |

 

 

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.9.1VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

配置 GRE 子链路时，将 type 设置为 8 ，ident 设置为 GRE 标识即可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.9.9 OPCODE\**** ***\*子链路\****

 

***\*接口功能\****

 

用于配置 OPCODE 子链路的接口。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置 OPCODE 子链路： param={

"type":"subNetlink",



"child_type":"", "netlink":16,

"xml":"<Element handle='subNetlink' version='1'

netlink_id='16'><subNetlink  flags='1' type='9' name='opcode-2000- subnetlink' ident='2000' device='' inBand='1000' outBand='1000'

totalBand='1000' lowCongestion='20.00' middleCongestion='50.00' highCongestion='90.00'/></Element>"

}

请求参数说明：

配置 OPCODE 子链路时，“type”和“ident”参数说明如下，其他参数说明请参

见[“7.9.1 VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| --------------------------------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| type                                    | 是                 | Uint8          | 9              | 子链路类型。                                                 |
| ident                                   | 是                 | String         | 无             | 子链路标识。对于 OPCODE 子链路来 说， 多个标识使用英文逗号分隔。 |

 

 

***\*XML\**** ***\*说明\****

XML 说明请参见[“7.9.1VLAN 子链路”](#bookmark124)中[“XML 说明”](#bookmark230)。

配置 OPCODE 子链路时，将 type 设置为 9 ，ident 设置为 OPCODE 标识即可。

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。

***\*7.10\**** ***\*安全策略\****

 

***\*接口功能\****

 

用于配置安全策略的接口。

***\*请求方法\****

 

POST

***\*URL\****



https://host:port/csras_api/{session}/configs/setting

***\*请求消息\****

 

JSON 示例，配置安全策略： param={

"type":"serverconfig", "child_type":"",

"xml":"<Element handle='serverconfig'

netlink_id='64'><ServerConfig><Security policy='1' threshold='3'

locktime='600' resettime='600' passlen='8' passdays='0' passrecords='2' webserverAccessTimeout='10' ><AccessControl

enable='1'><AnalysisRule><Entry> [************](************)/24</Entry></AnalysisRule> </AccessControl><RestrictUserFirstLoginTime>false</RestrictUserFirstLo  ginTime></Security></ServerConfig></Element>"

}

请求参数说明：

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| <Security>                              | 是                            | 无             | 无                                                           | 安全策略配置的根节点。                                       |
| policy                                  | 是                            | Bool           | l 0：关闭l 1：启用 默认值：启用                              | 是否启用安全策略。                                           |
| threshold                               | 是                            | Uint32         | 1-999                                                        | l IP 锁定阈值，用于设置一个 IP 地址登录尝试失败次数。l 如果某个 IP 地址尝试登录  失败的次数达到设置值，则 该 IP 地址将被锁定。l 在管理员手动解锁或 IP 锁  定时间期满之前，都无法使 用该 IP 地址进行登录。 |
| locktime                                | 是                            | Uint32         | 0-59940 设置为 0，则表示该 IP 地址将一直被锁定，直 到服务器重启。 | l IP 锁定时间，用于设置 IP  地址被锁定后，保持锁定的 时间，单位：秒。只有在设 置了 IP 锁定阈值时，该参  数才有意义。 |

 



 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                         | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | -------------------------------------- | ------------------------------------------------------------ |
|                                         |                               |                |                                        | l 该参数取值必须大于或等于 复位锁定计数器设置的值。          |
| resettime                               | 是                            | Uint32         | 1-9999                                 | l 复位锁定计数，表示一个时 间间隔，在某次登录尝试失 败后开始计时，经过的时间 超过该间隔后，则将登录尝 试失败计数器重置为 0。单 位：秒。l 只有在设置了 IP 锁定阈值 时，该参数才有意义。l 如果 IP 锁定时间不为 0，则 该参数取值必须小于或等于 IP 锁定时间设置的值。 |
| passlen                                 | 是                            | String         | 8-20 个字符                            | 设置密码长度最小值。                                         |
| passdays                                | 是                            | Uint32         | 0 表示不限制                           | 设置密码有效期，密码到期后，用户必须修改密码才能继 续使用系统。单位：天。 |
| passrecords                             | 是                            | Uint32         | 1-5                                    | 记住历史密码数 ，修改密码时，不能与最近使用的密码重 复。     |
| webserverAccessTi meout                 | 是                            | Uint32         | 0-9999                                 | l 会话超时时间，用于设置 在非监控页面下，如果用 户在超时时间无任何操作，则系统会自动退出登 录。单位：分钟l 用户需要重新登录后才可 以正常使用系统 |
| <AccessControl>                         | 是                            | 无             | 无                                     | 客户端访问控制根节点                                         |
| enable                                  | 是                            | Bool           | l true：开启l false：关闭 默认值：关闭 | 是否设置客户端访问控制白名 单。                              |
| <AnalysisRule>                          | 是                            | 无             | 无                                     | 访问控制台列表节点，可以包 含多个 Entry 节点。               |
| <Entry>                                 | 是                            | String         | 支持的 IP 地址格式：                   | 白名单 IP 地址规则，每个                                     |

 



 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
|                                         |                               |                | l IP 地址/掩码，如：[***********](***********)/24 或 [***********](***********)/255.2 55.255.0 或2001:3ef0::/80l IP 地址范围，如： [***********](***********)-[*************](*************) 或 2001:3ef0::0001-2001:3ef0::0005l 单个 IP 地址，如： [*************](*************) 或  2001:3ef0::0001 | <Entry>节点中只能输入一个 IP 或范围。如有多个规则，则需  要添加多个<Entry>节点。多个<Entry>节点设置的 IP 范 围不允许重复。 |
| RestrictUserFirstLo ginTime             | 是                            | Bool           | l true：开启l false：关闭 默认值：关闭                       | 设置首次登录是是否修改密 码。                                |

 

 

***\*XML\**** ***\*说明\****

 

<Element handle='serverconfig' netlink_id='64'> <ServerConfig><Security policy='1' threshold='3' locktime='600'resettime='600' passlen='10' passdays='0' passrecords='2' webserverAccessTimeout='10'><AccessControl enable='1'> <AnalysisRule><Entry>************/24</Entry> </AnalysisRule></AccessControl> <RestrictUserFirstLoginTime>false</RestrictUserFirstLoginTime> </Security>

 



 

</ServerConfig> </Element>

 

 

***\*响应消息\****

响应消息的格式和说明请参见[“7.2 配置接口说明”](#bookmark44)中的[“响应消息”](#bookmark184)。



 

***\*8\**** ***\*数据查询\****

 

***\*8.1\**** ***\*查询过滤器\****

 

查询过滤条件中支持设置过滤字段、关系运算符和逻辑运算符。

l 过滤字段

过滤字段，系统支持将统计表的字段作为过滤字段。统计表字段的获取方式请 参考[8.3 枚举统计表字段。](#bookmark231)

l 关系运算符

| ***\*关系运算符\**** | ***\*描述\**** |
| -------------------- | -------------- |
| =                    | 等于           |
| !=                   | 不等于         |
| >=                   | 大于等于       |
| <=                   | 小于等于       |
| >                    | 大于           |
| <                    | 小于           |

 

l 逻辑运算符

| ***\*关系运算符\**** | ***\*描述\**** |
| -------------------- | -------------- |
| &&                   | 与关系         |
| \|\|                 | 或关系         |

 

 

l 特殊字符与转义

| ***\*关系运算符\**** | ***\*描述\**** |
| -------------------- | -------------- |
| &                    | %26            |

 

 

说明：如果请求参数中有特殊字符可以对请求参数进行编码，适用于 API 脚本开 发，如果使用的是第三方工具，则可以将特殊字符替换为转义字符。

 

***\*8.2\**** ***\*枚举统计表\****

 

***\*接口功能\****

 

通过该接口，可以枚举系统中链路支持的统计表，获取到统计表的名称。



***\*请求方法\****

 

GET

***\*URL\****

 

https://host:port/csras_api/{session}/tables

***\*请求消息\****

 

URL 示例：

https:// [************](************) :8080/csras_api/089A60F4-EA86-47C5-907E-2C51E7F63BEA /tables

请求参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****    |
| -------------- | ------------------ | -------------- | -------------- | ----------------- |
| {session}      | 是                 | String         | 无             | 已登录的会话 ID。 |

 

 

***\*响应消息\****

 

响应参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| errcode        | 否                 | Uint16         | 无             | 错误码。                                                     |
| errmsg         | 否                 | String         | 无             | 错误信息。如果返回的是非 0 的 错误码，则可以通过查看错误信 息分析失败的原因。 |
| tables         | 否                 | String         | 无             | 统计表数组。                                                 |
| name           | 否                 | String         | 无             | 统计表名称，系统统计表的唯一 标识，在查询统计数据时使用。    |
| desc           | 否                 | String         | 无             | 统计表描述。                                                 |

说明：多个统计表依次存放在 tables 数组中。

 

 

正常情况下，获取枚举统计表接口的返回 JSON 数据如下： {

"errcode":0, "errmsg":"",



"tables":[ {

"desc":"概要统计", "name":"summary"

}, {

"desc":"物理地址",

"name":"phys_addr"

},

// 后续还有其他统计表

] }

 

错误时，获取枚举统计表接口的返回 JSON 数据如下： {

"errcode":6,

"errmsg":"没有权限" }

***\*8.3\**** ***\*枚举统计表字段\****

 

***\*接口功能\****

 

通过该接口，可以枚举指定统计表中支持的字段。

***\*请求方法\****

GET

***\*URL\****

 

https://host:port/csras_api/{session}/tables/{table_name}

***\*请求消息\****



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml18864\wps15.jpg)![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml18864\wps16.png)

URL 示例：

https:// [************](************):8080/csras_api/089A60F4-EA86-47C5-907E-

2C51E7F63BEA /tables/tcp_flow 请求参数说明：

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| {session}      | 是                 | String         | 无             | 已登录的会话 ID。                                            |
| {table_name}   | 是                 | String         | 无             | 枚举字段的统计表名称。可以通过[“8.2](#bookmark175) [枚举统计表”](#bookmark175)获取统计表名称。 |

 

 

***\*响应消息\****

 

响应参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| errcode        | 否                 | Uint16         | 无             | 错误码。                                                     |
| errmsg         | 否                 | String         | 无             | 错误信息。如果返回的是非 0 的错误码， 则可以通过查看错误信息分析失败的原   因。 |
| fields         | 否                 | String         | 无             | 字段数组。                                                   |
| name           | 否                 | String         | 无             | 字段名称，在获取统计数据使用。                               |
| type           | 否                 | String         | 无             | 字段类型，字段类型详细解释参见[“3.3 二](#bookmark12) [进制数据”](#bookmark12)。 |
| key            | 否                 | String         | 无             | 是否是 KEY 字段。 回溯存储系统按照 KEY 字段进行存储。        |
| caption        | 否                 | String         | 无             | 字段显示名称。                                               |

说明：多个字段依次存放在 fields 数组中。

 

 

正常情况下，获取枚举统计表字段接口的返回 JSON 数据如下： {

"errcode":0,

"errmsg":"请求成功", "fields":[

{



"caption":"物理地址",

"key":1,

"name":"mac", "type":9

}, {

"caption":"虚拟网类型", "key":0,

"name":"vn_type", "type":1

}, {

"caption":"虚拟网标识",

"key":0,

"name":"vn_id", "type":1

}, {

"caption":"接口标识", "key":0,

"name":"netflow_id", "type":3

}, {

"caption":"发送字节数", "key":0,

"name":"tx_byte",

"type":4 },

{

"caption": "时间",



"key": 0,

"name": "time", "type": 6

},

{

"caption": "发送每秒数据包数", "key": 0,

"name": "tx_packetps", "type": 5

},

// 后续还有其他字段

] }

 

错误时，获取枚举统计表字段接口的返回 JSON 数据如下： {

"errcode":6,

"errmsg":"没有权限" }

***\*8.4\**** ***\*查询统计数据\****

 

***\*接口功能\****

 

该接口用于查询指定统计表，指定字段的统计数据。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/ stats_data

***\*请求消息\****



***\*请求参数示例\**** ***\*1\****

查询链路 ID 为 16 的链路，物理地址表时间段（2024-2-1 20:23:00 - 2024-2-1 20:24:00）TOP10 记录

param={

"netlink": 16,

"table" : "phys_addr", "keys":["mac"],

"fields":["mac","total_byte","tx_bitps","rx_bitps"], "timeunit":0,

"sorttype":2,

"sortfield":"total_byte",

"filter":"",

"topcount":10, "dataformat":1,

"begintime":1706790120000, "endtime":1706790180000

}

查询结果展示

 

{"head": {"company": "colasoft", "netlinkId": 16,"serverIp": " [*************](*************)", "tableId": "phys_addr","taskName": "api_query","time": 1706790120, "totalCount": 10,"startIndex": 0, "endIndex": 10},"records": [ {

 



 

"mac": "1C:6F:65:C0:B2:A2","total_byte": 4847666, "tx_bitps": 50223.73,  "rx_bitps": 596131.73}, {"mac": "00:46:4B:89:CF:10", "total_byte": 4845090,"tx_bitps": 596242.67, "rx_bitps": 49769.33}, {"mac": "01:00:5E:7F:FF:FA","total_byte": 19482, "tx_bitps": 0.00,"rx_bitps": 2597.60}, {"mac": "90:2B:34:95:56:78","total_byte": 15932, "tx_bitps": 2124.27, "rx_bitps": 0.00}, {"mac": "1C:6F:65:EF:20:2F","total_byte": 14522, "tx_bitps": 1936.27, "rx_bitps": 0.00}, {"mac": "FF:FF:FF:FF:FF:FF","total_byte": 12196, "tx_bitps": 0.00,"rx_bitps": 1626.13}, {"mac": "33:33:00:01:00:02","total_byte": 10671, "tx_bitps": 0.00,"rx_bitps": 1422.80}, {"mac": "33:33:00:01:00:03",

 



 

"total_byte": 5632, "tx_bitps": 0.00,"rx_bitps": 750.93}, {"mac": "01:00:5E:00:00:FC","total_byte": 4352, "tx_bitps": 0.00,"rx_bitps": 580.27}, {"mac": "33:33:00:00:00:0C","total_byte": 3816, "tx_bitps": 0.00,"rx_bitps": 508.80} ]}

 

 

***\*请求参数示例\**** ***\*2\****

查询链路 ID 为 16 的链路，TCP 会话表  “未知 TCP 应用”（2024-2-1 20:23:00 -

2024-2-1 20:24:00）TOP10 记录 param={

"netlink": 16,

"table" : "tcp_flow",

"filter":"application_id=65100",

"keys":["client_ip_addr","client_port","server_ip_addr","server_port"],

"fields":["client_ip_addr","client_port","client_netsegment_id","server_ip_ad dr","server_netsegment_id","server_port","application_id","total_byte"],

"timeunit":0, "sorttype":2,

"sortfield":"total_byte", "filter":"",

"topcount":10,



"dataformat":1,

"begintime":1706790120000, "endtime":1706790180000

}

查询结果展示

 

{"head": {"company": "colasoft", "netlinkId": 16,"serverIp": "[*************](*************)","tableId": "tcp_flow","taskName": "api_query", "time": 1706790120,"totalCount": 10,"startIndex": 0,"endIndex": 10 },"records": [ {"client_ip_addr": "[***********](***********)", "client_port": "49567","client_netsegment_id": "self-segment","server_ip_addr": "[*************](*************)",  "server_netsegment_id": "未知网段", "server_port": "995","application_id": "未知 TCP 应用", "total_byte": 869909}, {"client_ip_addr": "[***********](***********)", "client_port": "49540","client_netsegment_id": "self-segment", "server_ip_addr": "[**************](**************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 528324}, {"client_ip_addr": "[***********](***********)", "client_port": "49547",

 



 

"client_netsegment_id": "self-segment","server_ip_addr": "[*************](*************)",  "server_netsegment_id": "未知网段", "server_port": "995","application_id": "未知 TCP 应用", "total_byte": 514802}, {"client_ip_addr": "[***********](***********)", "client_port": "49533","client_netsegment_id": "self-segment", "server_ip_addr": "[***************](***************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 424226}, {"client_ip_addr": "[***********](***********)", "client_port": "49514","client_netsegment_id": "self-segment", "server_ip_addr": "[***************](***************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 277050}, {"client_ip_addr": "[***********](***********)", "client_port": "49510","client_netsegment_id": "self-segment", "server_ip_addr": "[**************](**************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 246788}, {"client_ip_addr": "[***********](***********)", "client_port": "49509","client_netsegment_id": "self-segment", "server_ip_addr": "[**************](**************)","server_netsegment_id": "未知网段",

 



 

"server_port": "80","application_id": "未知 TCP 应用", "total_byte": 228519}, {"client_ip_addr": "[***********](***********)", "client_port": "49511","client_netsegment_id": "self-segment", "server_ip_addr": "[**************](**************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 222122}, {"client_ip_addr": "[***********](***********)", "client_port": "49508","client_netsegment_id": "self-segment", "server_ip_addr": "[**************](**************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 174810}, {"client_ip_addr": "[***********](***********)", "client_port": "49515","client_netsegment_id": "self-segment", "server_ip_addr": "[***************](***************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 139648} ]}

 

 

***\*请求参数示例\**** ***\*3\****

查询链路 ID 为 16 的链路，TCP 会话表服务器地址 [***************](***************) 且服务器端口



80 的 TCP 会话 TOP10 的记录: param={

"netlink": 16,

"table" : "tcp_flow",

"keys":["client_ip_addr","client_port","server_ip_addr","server_port"],

"fields":["client_ip_addr","client_port","client_netsegment_id","server_ip_ad dr","server_netsegment_id","server_port","application_id","total_byte"],

"timeunit":0, "sorttype":2,

"sortfield":"total_byte",

"filter":"((server_ip_addr= [***************](***************))%26%26(server_port=80))", "topcount":10,

"dataformat":1,

"begintime":1706790120000, "endtime":1706790180000

}

特别说明：过滤条件((server_ip_addr= [***************](***************))&& (server_port=80)) 中存在特殊字符&，需要转义为%26，参见常见特殊字符转义

结果展示

 

{"head": {"company": "colasoft", "netlinkId": 16,"serverIp": " [*************](*************)", "tableId": "tcp_flow","taskName": "api_query","time": 1706790120, "totalCount": 10,"startIndex": 0, "endIndex": 10},"records": [ {"client_ip_addr": " [***********](***********)", "client_port": "49533",

 



 

"client_netsegment_id": "self-segment", "server_ip_addr": "[***************](***************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 424226}, {"client_ip_addr": "[***********](***********)", "client_port": "49514","client_netsegment_id": "self-segment", "server_ip_addr": "[***************](***************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 277050}, {"client_ip_addr": "[***********](***********)", "client_port": "49515","client_netsegment_id": "self-segment", "server_ip_addr": "[***************](***************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 139648}, {"client_ip_addr": "[***********](***********)", "client_port": "49524","client_netsegment_id": "self-segment", "server_ip_addr": "[***************](***************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 138714}, {"client_ip_addr": "[***********](***********)", "client_port": "49529","client_netsegment_id": "self-segment", "server_ip_addr": "[***************](***************)","server_netsegment_id": "未知网段",

 



 

"server_port": "80","application_id": "未知 TCP 应用", "total_byte": 120938}, {"client_ip_addr": "[***********](***********)", "client_port": "49525","client_netsegment_id": "self-segment", "server_ip_addr": "[***************](***************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 102164}, {"client_ip_addr": "[***********](***********)", "client_port": "49532","client_netsegment_id": "self-segment", "server_ip_addr": "[***************](***************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 101470}, {"client_ip_addr": "[***********](***********)", "client_port": "49523","client_netsegment_id": "self-segment", "server_ip_addr": "[***************](***************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 73566}, {"client_ip_addr": "[***********](***********)", "client_port": "49558","client_netsegment_id": "self-segment", "server_ip_addr": "[***************](***************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 396

 



 

}, {"client_ip_addr": "[***********](***********)", "client_port": "49557","client_netsegment_id": "self-segment", "server_ip_addr": "[***************](***************)","server_netsegment_id": "未知网段", "server_port": "80","application_id": "未知 TCP 应用", "total_byte": 396} ]}

 

 

请求参数说明：

 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ----------------------------- | ------------------------------------------------------------ |
| table                                   | 是                            | 无             | 无                            | 查询的统计表名称。统计表 名称可以通过接口[“8.2 枚](#bookmark175) [举统计表”](#bookmark175)获得 |
| keys                                    | 是                            | String         | 无                            | Key 字段数组，回溯会按照 Key 字段查询合并，类似于 SQL 的 Group By。（FORM 提交，字段之间用 逗号分隔） |
| fields                                  | 是                            | String         |                               | 查询的统计表字段数组（字 段之间用英文逗号分隔）。 统计表字段可以通过接口[“8.3 枚举统计表字段”](#bookmark231)获 得。 |
| netlink                                 | 是                            | Uint8          | 无                            | 链路 ID，可通过[“6.1 配置](#bookmark178) [获取”](#bookmark178)获取到链路 ID。 |
| sorttype                                | 是                            | Uint8          | l 0：不排序l 1：升序l 2：降序 | 排序类型。                                                   |

 



 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| sortfield                               | 是                            | String         | 无                                                           | 排序字段，只能设置 1 个字 段。统计表字段可以通过接 口[“8.3 枚举统计表字段”](#bookmark231) 获得 |
| begintime                               | 是                            | Uint64         | 无                                                           | 开始时间时间戳，单位：毫 秒。数据内容***\*包括\**** start 指定 的时间点 |
| endtime                                 | 是                            | Uint64         | 无                                                           | 结束时间时间戳，单位：毫秒。数据内容***\*不包括\****endtime 指定的时间点。 |
| timeunit                                | 是                            | Uint32         | l 0：回溯服务器将合并 相同 Key 数据，其他 值会返回指定时间单  位的数据；l 1:1 毫秒，返回 1 毫秒 单位数据，不合并；l 1000:1 秒，返回 1 秒 单位数据，不合并；l 10*1000:10 秒，返回10 秒单位数据，不合 并；l 60*1000:1 分钟，返 回 1 分钟单位数据， 不合并；l 600*1000:10 分钟， 返回 10 分钟单位数 据，不合并；l 3600*1000:1 小时， 返回 1 小时单位数  据，不合并；l 24*3600*1000:1 天， 返回 1 天单位数据， 不合并； 默认值：0 | 设置查询的时间桶。                                           |

 



 

| ***\*节点\*******\*/\*******\*参数\**** | ***\*是否\**** ***\*必选\**** | ***\*类型\**** | ***\*值域\****                                               | ***\*说明\****                                               |
| --------------------------------------- | ----------------------------- | -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| filter                                  | 否                            | String         | 无                                                           | 具体配置方式请参见[8.1 查](#bookmark146) [询过滤器](#bookmark146) |
| topcount                                | 否                            | Uint32         | -1：返回实际查询记录数 默认值： 1000                         | 最大查询结果条数。                                           |
| dataformat                              | 是                            | Uint8          | l 0：不进行格式化，使 用默认二进制流。l 1：数据将被格式化为 json 后返回。 | 返回数据格式。                                               |

 

 

***\*8.4.1\**** ***\*查询统计表名称\****

统计表名称可以通过接口[“8.2 枚举统计表”](#bookmark175)获得，单次查询只能查询一个表的记 录。

 

***\*8.4.2\**** ***\*查询统计表字段\****

统计表字段可以通过接口[“8.3 枚举统计表字段”](#bookmark231)获得，单次查询可以查询任意个 数和任意顺序的统计表字段组合。

 

***\*8.4.3\**** ***\*查询结果\****

 

查询结果直接以二进制和 json 格式返回，当查询结果集数据量比较大时，建议以二 进制的方式返回。查询结果分为三部分组成，错误信息、字段描述和字段数据。

***\*错误信息\****

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| errcode        | 否                 | Uint16         | 2Byte          | 错误码，具体请参见[“3.4 错误码”](#bookmark14)。              |
| msglen         | 否                 | Uint32         | 4Byte          | 错误信息长度。                                               |
| errmsg         | 否                 | String         | 无             | 错误信息。如果返回的是非 0 的错误 码，则可以通过查看错误信息分析失 败的原因。 |

 



说明：当 errcode 为非 0 值，说明查询出错，查询结果中将不会返回字段描述和字 段数据。

 

 

***\*字段描述\****

 

| ***\*参数\****  | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| --------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| fields_counts   | 否                 | Uint16         | 2Byte          | 查询结果字段数。                                             |
| field1_name_len | 否                 | Uint32         | 4Byte          | field1_name 的长度+1                                         |
| field1_name     | 否                 | String         | 无             | 查询结果字段 1 名称，与接口[“8.3 枚](#bookmark231) [举统计表字段”](#bookmark231)的 field_name 一致。 |
| field1_type     | 否                 | Uint8          | 1Byte          | 查询结果字段 1 类型，与接口[“8.3 枚](#bookmark231) [举统计表字段”](#bookmark231)的 field_type 一致。 |

说明：多个查询结果字段依次存放

 

 

***\*字段数据\****

 

| ***\*参数\****          | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| ----------------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| 网络链路数              | 否                 | Uint8          | 固定为 1       | 返回的网络链路数。                                           |
| 网络链路 ID             | 否                 | Uint16         | 2Byte          | 返回的网络链路的 ID。                                        |
| 链路时间记录集个 数     | 否                 | Uint32         | 4 Byte         | 查询一段时间的记录，按照查询时间划分为 多少个单位时间，就有多少个时间记录集。如果查询参数 timeunit 为默认值 0，服务器 会合并查询时间的记录，记录集个数为 1， 相同 KEY 的记录只有 1 条。 |
| 链路时间记录集 1 时间   | 否                 | Uint64         | 8 Byte         | 链路时间记录集 1 的记录时间，单位：毫 秒。                   |
| 链路时间记录集 1 记录数 | 否                 | Uint32         | 4 Byte         | 链路时间记录集 1 的记录数。                                  |
| field1_data             | 否                 | String         | field1_len     | 查询结果字段 1 数据。由 field1_type 解释其意义（字段类型详细解释见[3.3 二进制数](#bookmark12) [据）](#bookmark12) ， field1_len，字段 1 数据长度。如：l field1_type=4, field1_len=8, 则该字段 为 8 字节的无符号整数；l field1_type=10, field1_len=4, 则该字 段为变长的 IP 地址，由 |

 



 

| ***\*参数\****          | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| ----------------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
|                         |                    |                |                | IP_VERSION[1] + IP_ADDR[N]组成， 首先解析 1 字节的 IP_VERSION ， 如 果 IP_VERSION 为 4，则 N 为 4，后  面是 4 个字节的 IPV4 地址，如果IP_VERSION 为 6，则 N 为 16，后面, 是 16 个字节的 IPV6 地址；l field1_type=7, field1_len=10, 则该字  段为变长的字符串数据，LENGTH[4] + VALUE[N]组成，首先解析 LENGTH 4 字节的字符串长度，再解析相应长度的 字符串。 |
| field2_data             | 否                 | String         | field2_len     | 查询结果字段 2 数据。                                        |
| field3_data             | 否                 | String         | field3_len     | 查询结果字段 3 数据。                                        |
| field4_data             | 否                 | String         | field4_len     | 查询结果字段 4 数据。                                        |
| ......                  |                    |                |                | 多个查询结果字段数据依次存放                                 |
| 链路时间记录集 2 时间   | 否                 | Uint64         | 8 Byte         | 链路时间记录集 2 的记录时间，单位：毫 秒。                   |
| 链路时间记录集 2 记录数 | 否                 | Uint32         | 4 Byte         | 链路时间记录集 2 的记录数                                    |
| field1_data             | 否                 | String         | field1_len     | 查询结果字段 1 数据。                                        |
| field2_data             | 否                 | String         | field2_len     | 查询结果字段 2 数据。                                        |
| field3_data             | 否                 | String         | field3_len     | 查询结果字段 3 数据。                                        |
| field4_data             | 否                 | String         | field4_len     | 查询结果字段 4 数据。                                        |
| ......                  |                    |                |                | 多个查询结果字段数据依次存放。                               |
| ......                  |                    |                |                | 多个时间记录集数据依次存放。                                 |

 



 

***\*9\**** ***\*下载\**** ***\*CSV\**** ***\*统计数据\****

***\*接口功能\****

 

通过该接口可以查询链路的 CSV 统计数据。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/data_files

***\*请求消息\****

 

JSON 示例：

param={

"type":"csv",

"netlink":"2",

"time":"1444979280", "table":"phys_flow"

}

请求参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| type           | 是                 | String         | csv：csv 文件  | 下载文件类型。                                               |
| netlink        | 是                 | Uint8          | 无             | 链路 ID。可通过[“6.1 配置获取”](#bookmark178)获取 到链路 ID。 |
| time           | 是                 | String         | 无             | 时间戳，CSV 文件每分钟生成一次，所 以时间戳必须是整分钟。    |
| table          | 是                 | String         | 无             | 统计表 id ，参见[“8.2 枚举统计表”](#bookmark175)             |

 

 

***\*响应消息\****

 

响应消息包括两部分内容：错误信息和 CSV 文件。



错误参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| errcode        | 否                 | Uint16         | 2Byte          | 错误码，具体请参见[“3.4 错误](#bookmark14) [码”](#bookmark14)。 |
| msglen         | 否                 | Uint32         | 4Byte          | 错误信息长度。                                               |
| errmsg         | 否                 | String         | 无             | 错误信息。如果返回的是非 0 的 错误码，则可以通过查看错误信 息分析失败的原因。 |

说明：如 errcode 返回为非0 值，则说明下载出错，不会返回 CSV 文件。

 

 

***\*CSV\**** ***\*文件：\****

CSV 文件是以逗号分隔值文件格式 ， CSV 文件以二进制的方式返回。



 

***\*10\**** ***\*下载数据包\****

 

***\*10.1\**** ***\*下载过滤器\****

 

通过下载过滤器，可以对需要下载的数据包进行过滤 ，下载过滤器支持逻辑运算 符。

目前支持的过滤对象如下。

 

| ***\*过滤器对象\**** | ***\*说明\****                                               |
| -------------------- | ------------------------------------------------------------ |
| ip_addr              | IP 地址，例如：ip_addr=[19*********](19*********)            |
| client_ip_addr       | 客户端 IP 地址 ，在过滤时不会进行客户端 IP 地址的判 断，过滤结果与 ip_addr 一样 |
| server_ip_addr       | 服务端 IP 地址 ，在过滤时不会进行服务器 IP 地址判断， 过滤结果与 ip_addr 一样 |
| ip_range             | IP 范围，例如：ip_range=[19*********](19*********)-[*************](*************) |
| ip_flow              | IP 会话，例如：ip_flow=[[19*********](19*********)]-[[*********](*********)] |
| ipport_flow          | TCP/UDP 会话，例如： ipport_flow = [[*************](*************)]:5652- [[*************](*************)]:443 |
| port                 | 端口，例如：port=443                                         |
| client_port          | 客户端端口 ，在过滤时不会进行客户端端口的判断，过滤 结果与 port 一样 |
| server_port          | 服务端端口 ，在过滤时不会进行服务器端口的判断，过滤 结果与 port 一样 |
| port_range           | 端口范围，例如：port=80-443                                  |
| phys_addr            | 物理地址，例如：phys_addr =1C:6F:65:97:20:66                 |
| phys_range           | 物理地址范围，例如： FF:FF:FF:FF:FF:00-FF:FF:FF:FF:FF:FF     |
| phys_flow            | 物理会话，例如： [90:2B:34:12:26:EE]- [33:33:00:01:00:03]    |
| application          | 应用，只支持“ =”，例如：application=30001。应用 ID 通过[“6.1 配置获取”](#bookmark178)接口获取。 |
| netsegment           | 网段，只支持“ =”，例如： netsegment=30002。网段 ID 通过[“6.1 配置获取”](#bookmark178)接口获取。 |
| netsegment_flow      | 网段会话，例如：netsegment_flow=[30002]-[65101]              |

 



 

| ***\*过滤器对象\**** | ***\*说明\****                                               |
| -------------------- | ------------------------------------------------------------ |
| tree_protocol        | 协议树协议，通过数据包的所有协议标签过滤，例如：basic_protocol=245。协议 ID 可以通过[5.2 网络协议信](#bookmark32) [息查](#bookmark32)询获取。 |
| top_protocol         | 顶层协议，数据包所属的顶层协议，例如： top_protocol=443      |
| vlan_id              | VLAN ID，例如：vlan_id=236                                   |
| vlan_id_range        | VLAN ID 范围，例如：vlan_id_range =1-236                     |
| mplsvpn_id           | MPLS VPN 标签，例如：mplsvpn_id=768                          |
| mplsvpn_range        | MPLS VPN 标签范围，例如：mplsvpn_id=1-768                    |
| netflow_id           | Netflow 接口 ID，例如： netflow_id =1                        |
| netflow_id_range     | Netflow 接口 ID 范围，例如：netflow_id_range =1-10           |
| adapter_id           | 网卡 ID，例如：adapter_id =1。网卡 ID 通过[“6.1 配置](#bookmark178) [获取”](#bookmark178)接口获取。 |
| adapter_id_range     | 网卡 ID 范围，例如：adapter_id_range =1-3                    |
| dscp                 | dscp，例如：dscp =1                                          |
| dscp_range           | dscp 范围，例如：dscp_range =1-10                            |

 

 

***\*10.2\**** ***\*下载数据包\****

 

***\*接口功能\****

 

该接口用于下载指定链路、指定时间段的数据包。

***\*请求方法\****

 

POST

***\*URL\****

 

https://host:port/csras_api/{session}/data_packets

***\*请求消息\****

 

***\*下载数据包示例\**** ***\*1\****

下载指定时间范围（2024-2-1 20:23:00 - 2024-2-1 20:24:00）的数据包 param={



"netlink": 16,

"begintime":1706790120, "endtime":1706790180,

"filter":"" }

 

***\*下载数据包示例\**** ***\*2\****

下载指定时间范围（2024-2-1 20:23:00 - 2024-2-1 20:24:00）且 ip 地址为 [***********](***********) 的数据包，

param={

"netlink":16,

"filter":"ip_addr=[***********](***********)", "begintime":1706790120,

"endtime":1706790180

}

请求参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| session        | 是                 | String         | 无             | 登录会话 ID。                                                |
| netlink        | 是                 | Uint8          | 无             | 链路 ID。可通过[“6.1 配置获取”](#bookmark178)获取到 链路 ID。 |
| begintime      | 是                 | String         | 无             | 开始时间，单位：秒。                                         |
| endtime        | 是                 | String         | 无             | 结束时间，单位：秒。                                         |
| filter         | 否                 | String         | 无             | 过滤器配置参见[“10.1 下载过滤器”](#bookmark164)。            |

 

 

***\*响应消息\****

 

返回数据包数据量一般会比较庞大，所以下载结果以二进制的方式返回。下载结果 由两部分组成：错误信息和数据包数据。

错误参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                  |
| -------------- | ------------------ | -------------- | -------------- | ----------------------------------------------- |
| errcode        | 否                 | Uint16         | 2Byte          | 错误码，具体请参见[“3.4 错误码”](#bookmark14)。 |
| msglen         | 否                 | Uint32         | 4Byte          | 错误信息长度。                                  |

 



 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| errmsg         | 否                 | String         | 无             | 错误信息。如果返回的是非 0 的错误  码，则可以通过查看错误信息分析失败 的原因。 |

说明：如 errcode 返回为非0 值，则说明下载数据包出错，不会返回数据包。

 

 

数据包数据参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| MediumType     | 否                 | Uint16         | 2 Byte         | 数据包的媒介类型。                                           |
| AdapterId      | 否                 | Uint32         | 4 Byte         | 适配器 Id。                                                  |
| TimeStamp      | 否                 | String         | 8 Byte         | 数据包的时间戳。                                             |
| PktLength      | 否                 | Uint32         | 4 Byte         | 数据包的实际长度。                                           |
| CapturedLength | 否                 | Uint32         | 4 Byte         | 数据包的捕捉长度。                                           |
| PktData        | 否                 | String         | 无             | 数据包的数据内容，长度由捕捉长度决 定。多个数据包的数据依次返回。 |

 



 

***\*11\**** ***\*在线解码\****

 

***\*11.1\**** ***\*流解码接口\****

 

***\*接口功能\****

 

该接口用于获取指定链路、指定时间段内的流解码信息。

***\*请求方法\****

 

GET、POST

***\*URL\****

 

https://host:port/csras_api/{session}/forward

***\*请求消息\****

 

JSON 数据请求示例：

param= {

"cmd": 126, "fmt":1,

"list":[

{"type":"uint16_t", "value":8},

{"type":"uint32_t ", "value":1669651410}, {"type":"uint32_t ", "value":1669651411},

{"type":"string", "value":""},

{"type":"uint32_t ", "value":1000}, {"type":"uint32_t ", "value":1000}

] }

请求参数说明



 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| cmd            | 是                 | Unit16         | 126            | 转发命令， 126 表示流解码，固定设置为126 即可。              |
| fmt            | 是                 | Unit8          | 1              | 固定设置为 1 即可。                                          |
| list           | 是                 | 无             | 无             | 命令参数列表。                                               |
| $1             | 是                 | Unit16         | 无             | 参数列表第一个参数，表示链路 ID ，可 通过[“6.1 配置获取”](#bookmark178)获取到链路 ID。 |
| $2             | 是                 | Unit32         | 无             | 参数列表第二个参数，表示开始时间，单 位秒。                  |
| $3             | 是                 | Unit32         | 无             | 参数列表第三个参数，表示结束时间，单 位秒。                  |
| $4             | 否                 | String         | 无             | 参数列表第四个参数，表示过滤条件，过 滤器配置参见[“10.1 下载过滤器”](#bookmark164)。如  果不需要过滤，设置为空即可。 |
| $5             | 是                 | Unit32         | 无             | 参数列表第五个参数，总字节数限制。                           |
| $6             | 是                 | Unit32         | 无             | 参数列表第六个参数，总数据包限制。                           |

 

 

***\*响应消息\****

 

返回结果： [

{

"downBytes": 180, "downPackets": 3,

"downloadFiter" : "(ipport_flow=[[*************](*************) ]:80-

[ [***********](***********) ]:49507)&&(top_protocol=600||tree_protocol=600)",  #当前会话的过滤器，

可以使用该过滤器，对会话进行数据包详细解码信息的提取

"dstAddr": "[*************](*************)",

"dstMac": "00:46:4B:89:CF:10", "dstPort": 80,

"flowId": 0,

"ipprotocol" : 600, "protocol": 600,

"srcAddr": " [***********](***********)",

"srcMac": "1C:6F:65:C0:B2:A2",



"srcPort" : 49507, "upBytes" : 54,

"upPackets" : 1

} ]

 

响应参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| errcode        | 否                 | Unit16         | 2Byte          | 错误码，具体请参见[“3.4 错误码”](#bookmark14)。              |
| msglen         | 否                 | Unit32         | 4Byte          | 错误信息长度。                                               |
| errmsg         | 否                 | String         | 无             | 错误信息。如果返回的是非 0 的错误  码，则可以通过查看错误信息分析失败 的原因。 |

 

 

***\*11.2\**** ***\*数据包概要解码接口\****

 

***\*接口功能\****

 

该接口用于获取数据包的概要解码信息，包括确认号、序列号、窗口大小和头部长 度等信息。

***\*请求方法\****

 

GET、POST

***\*URL\****

 

https://host:port/csras_api/{session}/forward

***\*请求消息\****

 

JSON 数据请求示例：

param= {

"cmd": 125,



"fmt":1, "list":[

{"type":"uint16_t", "value":8},

{"type":"uint32_t", "value":1669651410}, {"type":"uint32_t", "value":1669651411},

{"type":"string", "value":""},

{"type":"uint32_t", "value":10}, {"type":"uint32_t", "value":10} ]

}

请求参数说明

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| cmd            | 是                 | Unit16         | 125            | 转发命令， 125 表示概要解码，固定设置 为 125 即可。          |
| fmt            | 是                 | Unit8          | 1              | 固定设置为 1 即可。                                          |
| list           | 是                 | 无             | 无             | 命令参数列表。                                               |
| $1             | 是                 | Unit16         | 无             | 参数列表第一个参数，表示链路 ID ，可 通过[“6.1 配置获取”](#bookmark178)获取到链路 ID。 |
| $2             | 是                 | Unit32         | 无             | 参数列表第二个参数，表示开始时间，单 位秒。                  |
| $3             | 是                 | Unit32         | 无             | 参数列表第三个参数，表示结束时间，单 位秒。                  |
| $4             | 否                 | String         | 无             | 参数列表第四个参数，表示过滤条件，过 滤器配置参见[“10.1 下载过滤器”](#bookmark164)。如果 不需要过滤，设置为空即可。 |
| $5             | 是                 | Unit32         | 无             | 参数列表第五个参数，总字节数限制。                           |
| $6             | 是                 | Unit32         | 无             | 参数列表第六个参数，总数据包限制。                           |

 

 

***\*响应消息\****

 

返回结果： [

{



"adapterId": 1,

"application": "", "caplen": 60,

"downloadFiter": "(ipport_flow=[*************]:80-

[[***********](***********)]:49507)&&(top_protocol=600||tree_protocol=600)", #当前会话的过滤 器， 可以使用该过滤器，对会话进行数据包详细解码信息的提取

"dstAddr": "[***********](***********)",

"dstMac": "1C:6F:65:C0:B2:A2", "dstPort": "49507",

"ipprotocol": 600, "payloadlen": 0,

"pktlen": 64, "pktnum": 0,

"protocol": 600,

"srcAddr": "*************",

"srcMac": "00:46:4B:89:CF:10", "srcPort": "80",

"summaryDecoder": "<PacketSummary>[ACK]序列号=2537253340,确认号 =3739161209,窗口=123,头部长度=20 字节</PacketSummary>",  #概要解码结果

"time": 1669651410261189247 }

]

响应参数说明

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| errcode        | 否                 | Unit16         | 2Byte          | 错误码，具体请参见[“3.4 错误码”](#bookmark14)。              |
| msglen         | 否                 | Unit32         | 4Byte          | 错误信息长度。                                               |
| errmsg         | 否                 | String         | 无             | 错误信息。如果返回的是非 0 的错误  码，则可以通过查看错误信息分析失败 的原因。 |

 

 

***\*11.3\**** ***\*数据包详细解码接口\****

 

***\*接口功能\****



该接口用于获取数据包详细解码信息 ，信息中包括每个数据包解码字段的内容。

***\*请求方法\****

 

GET、POST

***\*URL\****

 

https://host:port/csras_api/{session}/forward

***\*请求消息\****

 

JSON 数据请求示例：

param= {

"cmd": 124, "fmt":1,

"list":[

{"type":"uint16_t", "value":8},

{"type":"uint32_t", "value":1669651410}, {"type":"uint32_t", "value":1669651411},

{"type":"String", "value":""},  {"type":"uint32_t", "value":1}, {"type":"uint8_t", "value":1}  ]

}

请求参数说明

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| cmd            | 是                 | Unit16         | 124            | 转发命令， 124 表示详细解码，固定设置 为 124 即可。          |
| fmt            | 是                 | Unit8          | 1              | 固定设置为 1 即可。                                          |
| list           | 是                 | 无             | 无             | 命令参数列表。                                               |
| $1             | 是                 | Unit16         | 无             | 参数列表第一个参数，表示链路 ID ，可 通过[“6.1 配置获取”](#bookmark178)获取到链路 ID。 |
| $2             | 是                 | Unit32         | 无             | 参数列表第二个参数，表示开始时间，单                         |

 



 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
|                |                    |                |                | 位秒。                                                       |
| $3             | 是                 | Unit32         | 无             | 参数列表第三个参数，表示结束时间，单 位秒。                  |
| $4             | 否                 | String         | 无             | 参数列表第四个参数，表示过滤条件，过 滤器配置参见[“10.1 下载过滤器”](#bookmark164)。如果 不需要过滤，设置为空即可。 |
| $5             | 是                 | Unit32         | 无             | 参数列表第五个参数，总字节数限制。                           |
| $6             | 是                 | Unit32         | 无             | 参数列表第六个参数，总数据包限制。                           |

 

 

***\*响应消息\****

 

返回结果：

<XML>

<PacketInfo>

<Bytes>1C 6F 65 C0 B2 A2 00 46 4B 89 CF 10 08 00 45 60 00 28 1C DB 40 00 2F 06 67 D8 DC B5 20 57 C0 A8 09 08 00 50 C1 63 97 3B 69 DC DE DF 12 79 50 11 00 7B 34 77 00 00 00 00 22 10 79 FE </Bytes>

<PacketDecodeInfo>

<Block id="PACKET_HEADER" shortname="数据包" longname="数据包信息" description="" protocol="0" offset="0" length="-1" nodetype="1">

<Field id="PACKET_NUMBER:-1" shortname="" longname="编号" description="" value="1" offset="-1" length="8" nodetype="4" mask="-1" />

<Field id="PACKET_LENGTH:-1" shortname="" longname="数据包长度" description="" value="64" offset="-1" length="4" nodetype="4" mask="-1" />

<Field id="PACKET_CAPTURE_LENGTH:-1" shortname="" longname=" 捕获长度" description="" value="60" offset="-1" length="4" nodetype="4" mask="-1" />

<Field id="PACKET_TIMESTAMP:-1" shortname="" longname="时间戳" description="" value="2022/11/29 00:03:30.261857613" offset="-1" length="8"

nodetype="3" mask="-1" /> </Block>

<Block id="Ethernet_II" shortname="" longname="以太网 - II" description="" protocol="3" offset="0" length="14" nodetype="2">

<Field id="DstAddress:0" shortname="" longname=" 目的地址"

description="" value="1C:6F:65:C0:B2:A2" offset="0" length="6" nodetype="32" mask="-1" />

<Field id="SrcAddress:6" shortname="" longname="源地址"

description="" value="00:46:4B:89:CF:10" offset="6" length="6" nodetype="32" mask="- 1" />



<Field id="Protocol:12" shortname="" longname="协议类型"

description="IP" value="0x800" offset="12" length="2" nodetype="57" mask="-1" /> </Block>

<Block id="IP" shortname="IP" longname="互联网协议" description="" protocol="14" offset="14" length="20" nodetype="2">

<Field id="Version:0" shortname="" longname="版本" description="" value="4" offset="14" length="1" nodetype="54" mask="240" />

<Field id="IHL:0" shortname="" longname="头部长度" description="20" value="5" offset="14" length="1" nodetype="54" mask="15" />

<Field id="DiffServ:1" shortname="" longname="区分服务字段" description="" value="" offset="15" length="1" nodetype="254" mask="-1">

<Field id="DSCP:1" shortname="" longname="区分服务码点" description="类选择器 3" value="0110 00.." offset="15" length="1" nodetype="50" mask="252" />

<Field id="ECN:1" shortname="" longname="显示拥塞通告"

description="不带 ECN 的传输" value=".... ..00" offset="15" length="1" nodetype="50" mask="3" /></Field>

<Field id="TotalLength:2" shortname="" longname="总长度"

description="" value="40" offset="16" length="2" nodetype="55" mask="-1" />

<Field id="Identification:4" shortname="" longname="标识"

description="7387" value="0x1cdb" offset="18" length="2" nodetype="57" mask="-1" />

<Field id="FragmentFlags:6" shortname="" longname="分段标志" description="" value="" offset="20" length="1" nodetype="254" mask="-1">

<Field id="FragReserved:6" shortname="" longname="保留"

description="" value="0... ...." offset="20" length="1" nodetype="53" mask="128" />

<Field id="Fragment:6" shortname="" longname="不分段"

description="不分段" value=".1.. ...." offset="20" length="1" nodetype="52" mask="64" />

<Field id="MoreFragment:6" shortname="" longname="更多分段" description="最后一个段" value="..0. ...." offset="20" length="1" nodetype="53"

mask="32" /></Field>

<Field id="FragmentOffset:6" shortname="" longname="分段偏移"  description="0" value="...0 0000 0000 0000" offset="20" length="2" nodetype="51" mask="8191" />

<Field id="TimeToLive:8" shortname="" longname="存活时间" description="" value="47" offset="22" length="1" nodetype="54" mask="-1" />

<Field id="Protocol:9" shortname="" longname="协议"

description="TCP" value="6" offset="23" length="1" nodetype="54" mask="-1" />

<Field id="Checksum:10" shortname="" longname="校验和"

description="" value="0x67d8" offset="24" length="2" nodetype="35" mask="-1" />

<Field id="SrcIp:12" shortname="" longname="源地址" description="" value="[*************](*************)" offset="26" length="4" nodetype="33" mask="-1" />

<Field id="DstIp:16" shortname="" longname=" 目的地址" description="" value=" [***********](***********)" offset="30" length="4" nodetype="33" mask="-1" />



</Block>

<Block id="TCP" shortname="TCP" longname="TCP - 传输控制协议" description="" protocol="600" offset="34" length="20" nodetype="2">

<Field id="SrcPort:0" shortname="" longname="源端口" description="" value="80" offset="34" length="2" nodetype="49" mask="-1" />

<Field id="DstPort:2" shortname="" longname=" 目标端口" description="" value="49507" offset="36" length="2" nodetype="49" mask="-1" />

<Field id="SeqNumber:4" shortname="" longname="序列号"

description="下一数据包序列号: 2537253346" value="2537253340" offset="38" length="4" nodetype="55" mask="-1" />

<Field id="AckNumber:8" shortname="" longname="确认号"

description="" value="3739161209" offset="42" length="4" nodetype="55" mask="-1" />

<Field id="TCPOffset:12" shortname="" longname="头部长度" description="20  字节" value="0101 ...." offset="46" length="1" nodetype="51" mask="240" />

<Field id="Flags:13" shortname="" longname="标识" description="" value="" offset="47" length="1" nodetype="254" mask="-1">

<Field id="CWR:13" shortname="" longname="窗口位"

description="Not Set" value="0... ...." offset="47" length="1" nodetype="53" mask="128" />

<Field id="ECN:13" shortname="" longname="阻塞位"

description="Not Set" value=".0.. ...." offset="47" length="1" nodetype="53" mask="64" />

<Field id="URG:13" shortname="" longname="紧急指针位"

description="Not Set" value="..0. ...." offset="47" length="1" nodetype="53" mask="32" />

<Field id="ACK:13" shortname="" longname="确认位"

description="Set" value="...1 ...." offset="47" length="1" nodetype="52" mask="16" />

<Field id="PSH:13" shortname="" longname="上推功能"

description="Not Set" value=".... 0..." offset="47" length="1" nodetype="53" mask="8" />

<Field id="RST:13" shortname="" longname="重置连接位"

description="Not Set" value=".... .0.." offset="47" length="1" nodetype="53" mask="4" />

<Field id="SYN:13" shortname="" longname=" 同步序列号"

description="Not Set" value=".... ..0." offset="47" length="1" nodetype="53" mask="2" />

<Field id="FIN:13" shortname="" longname="终止位"

description="Set" value=".... ...1" offset="47" length="1" nodetype="52" mask="1" /></Field>

<Field id="WindowSize:14" shortname="" longname="窗口大小" description="" value="123" offset="48" length="2" nodetype="55" mask="-1" />

<Field id="Checksum:16" shortname="" longname="校验和"

description="" value="0x3477" offset="50" length="2" nodetype="35" mask="-1" />

<Field id="UrgentPointer:18" shortname="" longname="紧急指针" description="" value="0" offset="52" length="2" nodetype="55" mask="-1" />



</Block>

<Block id="PACKET_EXTRA_DATA" shortname="额外" longname="额外数据 " description="" protocol="0" offset="54" length="6" nodetype="2">

<Field id="PACKET_BYTES:0" shortname="" longname="字节数"  description="" value="6 bytes" offset="54" length="6" nodetype="4" mask="-1" />

</Block>

</PacketDecodeInfo>

</PacketInfo> </XML>

响应参数说明：

 

| ***\*参数\**** | ***\*是否必选\**** | ***\*类型\**** | ***\*值域\**** | ***\*说明\****                                               |
| -------------- | ------------------ | -------------- | -------------- | ------------------------------------------------------------ |
| errcode        | 否                 | Unit16         | 2Byte          | 错误码，具体请参见[“3.4 错误码”](#bookmark14)。              |
| msglen         | 否                 | Unit32         | 4Byte          | 错误信息长度。                                               |
| errmsg         | 否                 | String         | 无             | 错误信息。如果返回的是非 0 的错误  码，则可以通过查看错误信息分析失败 的原因。 |

 