#!/usr/bin/env python3
"""
测试经验管理器功能
"""

import asyncio
import json
import os
from experience_manager import ExperienceManager


async def test_experience_manager():
    """测试经验管理器的基本功能"""
    print("=== 测试经验管理器 ===")
    
    # 初始化经验管理器
    try:
        manager = ExperienceManager()
        print("✓ 经验管理器初始化成功")
    except Exception as e:
        print(f"✗ 经验管理器初始化失败: {e}")
        return
    
    # 测试保存经验
    print("\n--- 测试保存经验 ---")
    test_query = "在192.168.163.209上有多少条链路？"
    test_answer = "在 192.168.163.209 上有 3 条链路，分别是：链路一 (id: 1)、链路二 (id: 2) 和回放链路 (id: 10)。"
    test_tools = ["stats_mcp_server_query_links", "stats_mcp_server_get_link_info"]
    
    success = await manager.save_experience(
        query=test_query,
        final_answer=test_answer,
        execution_time=47.395,
        token_usage=0,  # 测试token为0的情况
        tools_used=test_tools,
        success=True,
        metadata={
            "iterations": 4,
            "model": "gpt-4",
            "provider": "openai"
        }
    )
    
    if success:
        print("✓ 经验保存成功")
    else:
        print("✗ 经验保存失败")
    
    # 测试查找相似经验
    print("\n--- 测试查找相似经验 ---")
    similar_query = "192.168.163.209有几条链路"
    similar_experiences = await manager.find_similar_experiences(similar_query)
    
    print(f"找到 {len(similar_experiences)} 个相似经验:")
    for i, (exp, similarity) in enumerate(similar_experiences):
        print(f"  {i+1}. 相似度: {similarity:.3f}")
        print(f"     查询: {exp.query}")
        print(f"     工具: {exp.tools_used}")
        print(f"     时间: {exp.execution_time:.2f}s")
        print()
    
    # 测试经验统计
    print("--- 经验统计 ---")
    summary = manager.get_experience_summary()
    print(f"总经验数: {summary['total_experiences']}")
    print(f"成功经验数: {summary['successful_experiences']}")
    print(f"成功率: {summary['success_rate']:.2%}")
    print(f"平均执行时间: {summary['avg_execution_time']:.2f}s")
    print(f"平均token使用: {summary['avg_token_usage']:.0f}")
    print(f"最常用工具: {summary['most_used_tools'][:3]}")
    
    # 检查经验文件
    print("\n--- 检查经验文件 ---")
    if os.path.exists(manager.experience_file):
        with open(manager.experience_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✓ 经验文件存在，包含 {len(data)} 条记录")
        
        if data:
            print("最新经验:")
            latest = data[-1]
            print(f"  查询: {latest['query']}")
            print(f"  答案: {latest['final_answer'][:100]}...")
            print(f"  工具: {latest['tools_used']}")
            print(f"  时间: {latest['timestamp']}")
    else:
        print("✗ 经验文件不存在")


async def test_with_different_conditions():
    """测试不同条件下的经验保存"""
    print("\n=== 测试不同保存条件 ===")
    
    manager = ExperienceManager()
    
    # 测试1: 执行时间过短
    print("\n1. 测试执行时间过短的情况")
    success = await manager.save_experience(
        query="测试查询1",
        final_answer="测试答案1",
        execution_time=0.5,  # 小于配置的1.0秒
        token_usage=100,
        tools_used=["tool1"],
        success=True
    )
    print(f"结果: {'保存成功' if success else '保存失败（符合预期）'}")
    
    # 测试2: token使用量过少
    print("\n2. 测试token使用量过少的情况")
    success = await manager.save_experience(
        query="测试查询2",
        final_answer="测试答案2",
        execution_time=5.0,
        token_usage=0,  # 现在配置为0，应该可以保存
        tools_used=["tool2"],
        success=True
    )
    print(f"结果: {'保存成功' if success else '保存失败'}")
    
    # 测试3: 查询失败
    print("\n3. 测试查询失败的情况")
    success = await manager.save_experience(
        query="测试查询3",
        final_answer="错误信息",
        execution_time=5.0,
        token_usage=100,
        tools_used=["tool3"],
        success=False  # 标记为失败
    )
    print(f"结果: {'保存成功' if success else '保存失败（符合预期）'}")
    
    # 测试4: 正常情况
    print("\n4. 测试正常保存情况")
    success = await manager.save_experience(
        query="测试查询4 - 正常情况",
        final_answer="这是一个正常的答案，长度足够，没有错误信息。",
        execution_time=10.0,
        token_usage=150,
        tools_used=["tool4", "tool5"],
        success=True
    )
    print(f"结果: {'保存成功' if success else '保存失败'}")


if __name__ == "__main__":
    asyncio.run(test_experience_manager())
    asyncio.run(test_with_different_conditions())
