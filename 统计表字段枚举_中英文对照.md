# 科来网络分析系统 - 统计表字段枚举
# 格式: 中文描述 - 英文字段名
# 生成时间: {"timestamp": "auto"}

## 所有字段汇总 (已去重)
字段总数: 1549

- 1024-1517 - pktsize1024_1518
- 1024-1517占比 - pktsize1024_1518_ratio
- 1024-1517占比基线 - pktsize1024_1518_ratio_baseline
- 1024-1517基线 - pktsize1024_1518_baseline
- 128-255 - pktsize128_255
- 128-255占比 - pktsize128_255_ratio
- 128-255占比基线 - pktsize128_255_ratio_baseline
- 128-255基线 - pktsize128_255_baseline
- 256-511 - pktsize256_511
- 256-511占比 - pktsize256_511_ratio
- 256-511占比基线 - pktsize256_511_ratio_baseline
- 256-511基线 - pktsize256_511_baseline
- 512-1023 - pktsize512_1023
- 512-1023占比 - pktsize512_1023_ratio
- 512-1023占比基线 - pktsize512_1023_ratio_baseline
- 512-1023基线 - pktsize512_1023_baseline
- 65-127 - pktsize65_127
- 65-127占比 - pktsize65_127_ratio
- 65-127占比基线 - pktsize65_127_ratio_baseline
- 65-127基线 - pktsize65_127_baseline
- <=64 - pktsize0_64
- <=64占比 - pktsize0_64_ratio
- <=64占比基线 - pktsize0_64_ratio_baseline
- <=64基线 - pktsize0_64_baseline
- >=1518 - pktsize1519
- >=1518占比 - pktsize1519_ratio
- >=1518占比基线 - pktsize1519_ratio_baseline
- >=1518基线 - pktsize1519_baseline
- ARP数据包占比 - arp_packet_rate
- ARP数据包数 - arp_packet
- Apdex - transaction_apdex
- DSCP Codepoint Name - dscp_name
- DSCP标记 - dscp
- H323消息 - h323_message
- H323问题 - h323_issues
- ICMP丢包数 - icmp_lost_packets
- ICMP丢包率 - icmp_lost_packets_rate
- ICMP响应数 - icmp_res_count
- ICMP平均回应时间 - icmp_avg_res_time
- ICMP数据包占比 - icmp_packet_rate
- ICMP数据包数 - icmp_packet
- ICMP最大回应时间 - icmp_max_res_time
- ICMP最小回应时间 - icmp_min_res_time
- ICMP预期响应包数 - icmp_expect_packets
- IPv4上行字节数 - uplink_byte_ipv4
- IPv4上行数据包数 - uplink_packet_ipv4
- IPv4上行比特率 - uplink_bitps_ipv4
- IPv4上行比特率峰值 - peak_uplink_bitps_ipv4
- IPv4下行字节数 - downlink_byte_ipv4
- IPv4下行数据包数 - downlink_packet_ipv4
- IPv4下行比特率 - downlink_bitps_ipv4
- IPv4下行比特率峰值 - peak_downlink_bitps_ipv4
- IPv4出字节数 - tx_byte_ipv4
- IPv4出数据包 - tx_packet_ipv4
- IPv4出比特率 - tx_bitps_ipv4
- IPv4出比特率峰值 - peak_out_bitps_ipv4
- IPv4出网数据包 - outbound_packet_ipv4
- IPv4出网比特率 - outbound_bitps_ipv4
- IPv4出网比特率峰值 - peak_outbound_bitps_ipv4
- IPv4出网流量 - outbound_byte_ipv4
- IPv4发送字节数 - send_byte_ipv4
- IPv4发送数据包 - send_packet_ipv4
- IPv4发送比特率 - send_bitps_ipv4
- IPv4字节数 - total_byte_ipv4
- IPv4字节数占比 - ipv4_byte_rate
- IPv4接收字节数 - receive_byte_ipv4
- IPv4接收数据包 - receive_packet_ipv4
- IPv4接收比特率 - receive_bitps_ipv4
- IPv4数据包 - total_inout_packet_ipv4
- IPv4数据包 - total_packet_ipv4
- IPv4数据包占比 - ipv4_inout_packet_rate
- IPv4数据包占比 - ipv4_packet_rate
- IPv4比特率 - total_bitps_ipv4
- IPv4比特率峰值 - peak_total_bitps_ipv4
- IPv4流量 - total_inout_byte_ipv4
- IPv4流量占比 - ipv4_inout_byte_rate
- IPv4进字节数 - rx_byte_ipv4
- IPv4进数据包 - rx_packet_ipv4
- IPv4进比特率 - rx_bitps_ipv4
- IPv4进比特率峰值 - peak_in_bitps_ipv4
- IPv4进网数据包 - inbound_packet_ipv4
- IPv4进网比特率 - inbound_bitps_ipv4
- IPv4进网比特率峰值 - peak_inbound_bitps_ipv4
- IPv4进网流量 - inbound_byte_ipv4
- IPv6上行字节数 - uplink_byte_ipv6
- IPv6上行数据包数 - uplink_packet_ipv6
- IPv6上行比特率 - uplink_bitps_ipv6
- IPv6上行比特率峰值 - peak_uplink_bitps_ipv6
- IPv6下行字节数 - downlink_byte_ipv6
- IPv6下行数据包数 - downlink_packet_ipv6
- IPv6下行比特率 - downlink_bitps_ipv6
- IPv6下行比特率峰值 - peak_downlink_bitps_ipv6
- IPv6出字节数 - tx_byte_ipv6
- IPv6出数据包 - tx_packet_ipv6
- IPv6出比特率 - tx_bitps_ipv6
- IPv6出比特率峰值 - peak_out_bitps_ipv6
- IPv6出网数据包 - outbound_packet_ipv6
- IPv6出网比特率 - outbound_bitps_ipv6
- IPv6出网比特率峰值 - peak_outbound_bitps_ipv6
- IPv6出网流量 - outbound_byte_ipv6
- IPv6发送字节数 - send_byte_ipv6
- IPv6发送数据包数 - send_packet_ipv6
- IPv6发送比特率 - send_bitps_ipv6
- IPv6字节数 - total_byte_ipv6
- IPv6字节数占比 - ipv6_byte_rate
- IPv6接收字节数 - receive_byte_ipv6
- IPv6接收数据包数 - receive_packet_ipv6
- IPv6接收比特率 - receive_bitps_ipv6
- IPv6数据包 - total_inout_packet_ipv6
- IPv6数据包 - total_packet_ipv6
- IPv6数据包占比 - ipv6_inout_packet_rate
- IPv6数据包占比 - ipv6_packet_rate
- IPv6比特率 - total_bitps_ipv6
- IPv6比特率峰值 - peak_total_bitps_ipv6
- IPv6流量 - total_inout_byte_ipv6
- IPv6流量占比 - ipv6_inout_byte_rate
- IPv6进字节数 - rx_byte_ipv6
- IPv6进数据包 - rx_packet_ipv6
- IPv6进比特率 - rx_bitps_ipv6
- IPv6进比特率峰值 - peak_in_bitps_ipv6
- IPv6进网数据包 - inbound_packet_ipv6
- IPv6进网比特率 - inbound_bitps_ipv6
- IPv6进网比特率峰值 - peak_inbound_bitps_ipv6
- IPv6进网流量 - inbound_byte_ipv6
- IP会话警报数 - application_ip_flow_alarm_count
- IP单播数据包 - ip_unicast_packet
- IP单播流量 - ip_unicast_byte
- IP单播流量占比 - ip_unicast_byte_rate
- IP地址 - ip_addr
- IP广播数据包 - ip_broadcast_packet
- IP广播数据包基线 - ip_broadcast_packet_baseline
- IP广播流量 - ip_broadcast_byte
- IP广播流量占比 - ip_broadcast_byte_rate
- IP广播流量基线 - ip_broadcast_byte_baseline
- IP总流量 - ip_total_byte
- IP组播数据包 - ip_multicast_packet
- IP组播流量 - ip_multicast_byte
- IP组播流量占比 - ip_multicast_byte_rate
- IP组播流量基线 - ip_multicast_byte_baseline
- Ipv4发送比特率峰值 - peak_tx_bitps_ipv4
- Ipv4接收比特率峰值 - peak_rx_bitps_ipv4
- Ipv6发送比特率峰值 - peak_tx_bitps_ipv6
- Ipv6接收比特率峰值 - peak_rx_bitps_ipv6
- N/A(视频) - mos_video_na
- N/A(视频已关闭) - mos_video_na_close
- N/A(视频活动中) - mos_video_na_curr
- N/A(通话时长) - duration_NA
- N/A(音频) - mos_audio_na
- N/A(音频已关闭) - mos_audio_na_close
- N/A(音频活动中) - mos_audio_na_curr
- NA呼叫次数 - call_NA
- NA呼叫次数(已关闭) - call_NA_close
- NA呼叫次数(活动中) - call_NA_curr
- SIP消息 - sip_message
- SIP问题 - sip_issues
- SSRC - ssrc
- TCP TTL值较小次数 - tcp_min_ttl_count
- TCP/UDP - ip_protocol
- TCP三次握手客户端确认包 - tcp_handshake_third_ack_packet
- TCP乱序包数 - tcp_disorder_packet
- TCP交易响应和无响应总数 - tcp_transaction_res_and_nores_count
- TCP交易响应次数 - tcp_transaction_response_count
- TCP交易响应率 - tcp_transaction_response_rate
- TCP交易总数 - tcp_transaction_total_count
- TCP交易总数基线 - tcp_transaction_total_count_baseline
- TCP交易无响应次数 - tcp_transaction_no_response_count
- TCP交易无响应率 - tcp_transaction_no_response_rate
- TCP交易请求次数 - tcp_transaction_request_count
- TCP会话半关闭次数 - tcp_flow_closing_status_time
- TCP会话半关闭率 - tcp_flow_closing_status_rate
- TCP会话警报数 - application_tcp_alarm_count
- TCP会话警报数 - tcp_alarm_count
- TCP分段丢失包 - tcp_segment_lost_packet
- TCP分段丢失率 - tcp_segment_loss_rate
- TCP分段丢失率 - tcp_segment_lost_packet_rate
- TCP分段丢失率 - tcp_segment_lost_rate
- TCP分段丢失率 - total_packet_lost_rate
- TCP发收比（有效载荷字节数） - tcp_effective_payload_byte_tr_ratio
- TCP发收比（有效载荷数据包数） - tcp_effective_payload_packet_tr_ratio
- TCP同步包 - tcp_syn_packet
- TCP同步确认包 - tcp_synack_packet
- TCP同步确认重传包 - tcp_synack_retrans_packet
- TCP同步重传包 - tcp_syn_retrans_packet
- TCP字节数（有效载荷） - total_tcp_effective_payload_byte
- TCP总数据包 - tcp_total_packet
- TCP数据包数 - total_tcp_all_payload_packet
- TCP数据包数（有效载荷） - total_tcp_effective_payload_packet
- TCP服务端口警报数 - application_tcp_serverport_alarm_count
- TCP确认包 - tcp_ack_packet
- TCP窗口为0次数 - tcp_window_0
- TCP结束包 - tcp_fin_packet
- TCP负载数据包数 - tcp_payload_packet
- TCP重传包 - tcp_retransmission_packet
- TCP重传率 - tcp_retransmission_packet_rate
- TCP重传率 - tcp_retransmission_rate
- TCP重传率 - total_packet_retrans_rate
- TCP重复确认包 - tcp_duplicate_ack_packet
- TCP重置包 - tcp_rst_packet
- TOS - tos
- TOS描述 - tos_description
- TS_PID - ts_pid
- TS丢包数 - ts_lost_packets
- TS丢失包 - ts_lost_packet
- TS丢失速率 - ts_lost_packet_countps
- TS帧数 - ts_frame_count
- TS帧率 - ts_frame_countps
- TS平均丢失率 - ts_avg_lost_packet_rate
- TS数据包 - ts_total_packet
- TS流类型 - ts_flow_type
- TS码率 - ts_total_code_bitps
- TS码率峰值 - ts_peak_total_code_bitps
- TTL - dns_ttl
- Tos端点1 - endpoint1_tos
- Tos端点2 - endpoint2_tos
- UDP会话警报数 - application_udp_alarm_count
- UDP服务端口警报数 - application_udp_serverport_alarm_count
- VOIP消息 - total_message_count
- VOIP问题 - total_issues_count
- VPIP总接通数 - total_conn
- VoIP主叫名称 - voip_caller_name
- VoIP会话警报个数 - session_alarm_count
- VoIP概要警报个数 - summary_alarm_count
- VoIP终端 - endpoint_name
- VoIP终端1名称 - voip_endpoint_name1
- VoIP终端2名称 - voip_endpoint_name2
- VoIP终端会话警报个数 - endpointflow_alarm_count
- VoIP终端名称 - voip_endpoint_name
- VoIP终端警报个数 - endpoint_alarm_count
- VoIP编号 - voip_call_id
- VoIP网段警报个数 - segment_alarm_count
- VoIP网段间警报个数 - segmentflow_alarm_count
- VoIP被叫名称 - voip_callee_name
- VoIP警报数 - voip_alarm_count
- deviceSN - deviceSN
- group_id - group_id
- ippro - ippro
- ip版本 - ip_version
- lastKeepTime - lastKeepTime
- mapping_id - mapping_id
- mapping_string - mapping_string
- middle_congestion_count_rec - middle_congestion_count_rec
- middle_congestion_count_send - middle_congestion_count_send
- middle_congestion_count_total - middle_congestion_count_total
- mstime - mstime
- new_flow_count_extra - new_flow_count_extra
- recordFlag - recordFlag
- record_type - record_type
- rule_id - rule_id
- rx_bytes - rx_bytes
- serious_congestion_count_rec - serious_congestion_count_rec
- serious_congestion_count_send - serious_congestion_count_send
- serious_congestion_count_total - serious_congestion_count_total
- stat_field_name1_str - stat_field_name1_str
- stat_field_value1_str - stat_field_value1_str
- topprotocol - topprotocol
- ttl - ttl
- tx_bytes - tx_bytes
- 一般(通话时长) - duration_normal
- 一般响应% - response_normal_rate
- 一般的TCP交易次数 - tcp_transaction_normal_count
- 一般的交易次数 - transaction_normal_count
- 一般的交易次数% - transaction_normal_rate
- 一般的呼叫次数 - call_normal
- 一般的呼叫次数(已关闭) - call_normal_close
- 一般的呼叫次数(活动中) - call_normal_curr
- 一般的响应次数 - response_normal_count
- 三次握手客户端RTT - tcp_three_handshake_total_client_rtt
- 三次握手平均客户端RTT - client_avg_rtt
- 三次握手平均时间 - tcp_three_handshake_avg_duration
- 三次握手平均时间 - tcp_three_handshake_avg_rtt
- 三次握手平均服务器RTT - server_avg_rtt
- 三次握手总RTT - tcp_three_handshake_total_rtt
- 三次握手最大客户端RTT - client_max_rtt
- 三次握手最大时间 - tcp_three_handshake_max_rtt
- 三次握手最大服务器RTT - server_max_rtt
- 三次握手最小客户端RTT - client_min_rtt
- 三次握手最小时间 - tcp_three_handshake_min_rtt
- 三次握手最小服务器RTT - server_min_rtt
- 三次握手服务器RTT - tcp_three_handshake_total_server_rtt
- 三次握手次数 - tcp_three_handshake
- 上行R值 - uplink_Rvalue
- 上行TCP-SYN+ACK包数 - uplink_tcp_synack_packet
- 上行TCP-SYN包数 - uplink_tcp_syn_packet
- 上行TCP乱序包数 - uplink_tcp_disorder_packet
- 上行TCP分段丢失包 - uplink_tcp_segment_lost_packet
- 上行TCP分段丢失率 - uplink_packet_lost_rate
- 上行TCP分段丢失率 - uplink_tcp_segment_lost_rate
- 上行TCP字节数（有效载荷） - uplink_tcp_effective_payload_byte
- 上行TCP数据包数 - uplink_tcp_all_payload_packet
- 上行TCP数据包数（有效载荷） - uplink_tcp_effective_payload_packet
- 上行TCP结束包 - uplink_tcp_fin_packet
- 上行TCP重传包 - uplink_tcp_retransmission_packet
- 上行TCP重传率 - uplink_packet_retrans_rate
- 上行TCP重传率 - uplink_tcp_retransmission_rate
- 上行TCP重复确认包 - uplink_tcp_duplicate_ack_packet
- 上行TCP重置包 - uplink_tcp_rst_packet
- 上行丢包数 - uplink_lost_packet
- 上行丢包率 - uplink_lost_packet_rate
- 上行乱序包数 - uplink_wrong_seq_packet
- 上行传输效率 - uplink_packet_trans_rate
- 上行字节数 - uplink_byte
- 上行字节数 - uplink_bytes
- 上行字节数90P基线 - uplink_byte_baseline_90p
- 上行字节数95峰值 - peak_uplink_byte_95p
- 上行字节数基线 - uplink_byte_baseline
- 上行字节数峰值 - peak_uplink_byte
- 上行字节数峰值基线 - peak_uplink_byte_baseline
- 上行字节数谷值 - valley_uplink_byte
- 上行字节数谷值基线 - valley_uplink_byte_baseline
- 上行字节数（有效载荷） - uplink_payload_byte
- 上行帧数 - uplink_ts_frame_count
- 上行平均抖动 - uplink_jitter_avg
- 上行平均视频MoS - uplink_video_mos_avg
- 上行平均音频MoS - uplink_audio_mos_avg
- 上行延时 - uplink_rtt
- 上行抖动 - uplink_jitter
- 上行抖动总值 - uplink_jitter_total
- 上行抖动计数 - uplink_jitter_count
- 上行数据包峰值 - peak_uplink_packet
- 上行数据包峰值基线 - peak_uplink_packet_baseline
- 上行数据包数 - uplink_packet
- 上行数据包数90P基线 - uplink_packet_baseline_90p
- 上行数据包数基线 - uplink_packet_baseline
- 上行数据包谷值 - valley_uplink_packet
- 上行数据包谷值基线 - valley_uplink_packet_baseline
- 上行最大抖动 - uplink_jitter_max
- 上行最大视频MoS - uplink_video_mos_max
- 上行最大音频MoS - uplink_audio_mos_max
- 上行最小抖动 - uplink_jitter_min
- 上行最小视频MoS - uplink_video_mos_min
- 上行最小音频MoS - uplink_audio_mos_min
- 上行期望数据包 - uplink_expect_packet
- 上行每秒字节数 - uplink_byteps
- 上行每秒数据包数 - uplink_packetps
- 上行每秒数据包数90P基线 - uplink_packetps_baseline_90p
- 上行每秒数据包数基线 - uplink_packetps_baseline
- 上行每秒数据包数峰值 - peak_uplink_packetps
- 上行每秒数据包数峰值基线 - peak_uplink_packetps_baseline
- 上行每秒数据包数谷值 - valley_uplink_packetps
- 上行每秒数据包数谷值基线 - valley_uplink_packetps_baseline
- 上行比特率 - uplink_bitps
- 上行比特率90P基线 - uplink_bitps_baseline_90p
- 上行比特率95峰值 - peak_uplink_bitps_95p
- 上行比特率基线 - uplink_bitps_baseline
- 上行比特率峰值 - peak_uplink_bitps
- 上行比特率峰值基线 - peak_uplink_bitps_baseline
- 上行比特率谷值 - valley_uplink_bitps
- 上行比特率谷值基线 - valley_uplink_bitps_baseline
- 上行比特率（有效载荷） - uplink_payload_bitps
- 上行码率 - uplink_code_bitps
- 上行视频MoS - uplink_video_mos
- 上行视频MoS总值 - uplink_video_mos_total
- 上行视频MoS计数 - uplink_video_mos_count
- 上行音频MoS - uplink_audio_mos
- 上行音频MoS总值 - uplink_audio_mos_total
- 上行音频MoS计数 - uplink_audio_mos_count
- 下行R值 - downlink_Rvalue
- 下行TCP-SYN+ACK包数 - downlink_tcp_synack_packet
- 下行TCP-SYN包数 - downlink_tcp_syn_packet
- 下行TCP乱序包数 - downlink_tcp_disorder_packet
- 下行TCP分段丢失包 - downlink_tcp_segment_lost_packet
- 下行TCP分段丢失率 - downlink_packet_lost_rate
- 下行TCP分段丢失率 - downlink_tcp_segment_lost_rate
- 下行TCP字节数（有效载荷） - downlink_tcp_effective_payload_byte
- 下行TCP数据包数 - downlink_tcp_all_payload_packet
- 下行TCP数据包数（有效载荷） - downlink_tcp_effective_payload_packet
- 下行TCP结束包 - downlink_tcp_fin_packet
- 下行TCP重传包 - downlink_tcp_retransmission_packet
- 下行TCP重传率 - downlink_packet_retrans_rate
- 下行TCP重传率 - downlink_tcp_retransmission_rate
- 下行TCP重复确认包 - downlink_tcp_duplicate_ack_packet
- 下行TCP重置包 - downlink_tcp_rst_packet
- 下行丢包数 - downlink_lost_packet
- 下行丢包率 - downlink_lost_packet_rate
- 下行乱序包数 - downlink_wrong_seq_packet
- 下行传输效率 - downlink_packet_trans_rate
- 下行字节数 - downlink_byte
- 下行字节数 - downlink_bytes
- 下行字节数90P基线 - downlink_byte_baseline_90p
- 下行字节数95峰值 - peak_downlink_byte_95p
- 下行字节数基线 - downlink_byte_baseline
- 下行字节数峰值 - peak_downlink_byte
- 下行字节数峰值基线 - peak_downlink_byte_baseline
- 下行字节数谷值 - valley_downlink_byte
- 下行字节数谷值基线 - valley_downlink_byte_baseline
- 下行字节数（有效载荷） - downlink_payload_byte
- 下行帧数 - downlink_ts_frame_count
- 下行平均抖动 - downlink_jitter_avg
- 下行平均视频MoS - downlink_video_mos_avg
- 下行平均音频MoS - downlink_audio_mos_avg
- 下行延时 - downlink_rtt
- 下行抖动 - downlink_jitter
- 下行抖动总值 - downlink_jitter_total
- 下行抖动计数 - downlink_jitter_count
- 下行数据包峰值 - peak_downlink_packet
- 下行数据包峰值基线 - peak_downlink_packet_baseline
- 下行数据包数 - downlink_packet
- 下行数据包数90P基线 - downlink_packet_baseline_90p
- 下行数据包数基线 - downlink_packet_baseline
- 下行数据包谷值 - valley_downlink_packet
- 下行数据包谷值基线 - valley_downlink_packet_baseline
- 下行最大抖动 - downlink_jitter_max
- 下行最大视频MoS - downlink_video_mos_max
- 下行最大音频MoS - downlink_audio_mos_max
- 下行最小抖动 - downlink_jitter_min
- 下行最小视频MoS - downlink_video_mos_min
- 下行最小音频MoS - downlink_audio_mos_min
- 下行期望数据包 - downlink_expect_packet
- 下行每秒字节数 - downlink_byteps
- 下行每秒数据包数 - downlink_packetps
- 下行每秒数据包数90P基线 - downlink_packetps_baseline_90p
- 下行每秒数据包数基线 - downlink_packetps_baseline
- 下行每秒数据包数峰值 - peak_downlink_packetps
- 下行每秒数据包数峰值基线 - peak_downlink_packetps_baseline
- 下行每秒数据包数谷值 - valley_downlink_packetps
- 下行每秒数据包数谷值基线 - valley_downlink_packetps_baseline
- 下行比特率 - downlink_bitps
- 下行比特率90P基线 - downlink_bitps_baseline_90p
- 下行比特率95峰值 - peak_downlink_bitps_95p
- 下行比特率基线 - downlink_bitps_baseline
- 下行比特率峰值 - peak_downlink_bitps
- 下行比特率峰值基线 - peak_downlink_bitps_baseline
- 下行比特率谷值 - valley_downlink_bitps
- 下行比特率谷值基线 - valley_downlink_bitps_baseline
- 下行比特率（有效载荷） - downlink_payload_bitps
- 下行码率 - downlink_code_bitps
- 下行视频MoS - downlink_video_mos
- 下行视频MoS总值 - downlink_video_mos_total
- 下行视频MoS计数 - downlink_video_mos_count
- 下行音频MoS - downlink_audio_mos
- 下行音频MoS总值 - downlink_audio_mos_total
- 下行音频MoS计数 - downlink_audio_mos_count
- 丢失字节数（有效载荷） - lost_payloads
- 丢失字节数（有效载荷）占比 - lost_payloads_rate
- 丢失数据包 - lost_packet
- 丢失数据包 - lost_packets
- 严重拥塞占比 - serious_congestion_rate_total
- 个数 - msg_count
- 中(视频) - mos_video_accept
- 中(视频已关闭) - mos_video_accept_close
- 中(视频活动中) - mos_video_accept_curr
- 中(音频) - mos_audio_accept
- 中(音频已关闭) - mos_audio_accept_close
- 中(音频活动中) - mos_audio_accept_curr
- 中度拥塞占比 - middle_congestion_rate_total
- 主叫 - caller
- 主叫名称 - caller_name
- 主叫端口 - caller_port
- 主叫端点 - caller_ip
- 主叫端点MAC地址 - caller_mac
- 主叫网段 - caller_netsegment_id1
- 主题 - mail_subject
- 乱序包数 - wrong_seq_packet
- 交易ID - transaction_id
- 交易IP会话警报数 - transaction_ipflow_alarm_count
- 交易名称 - transaction_id_str
- 交易响应次数 - response_count
- 交易响应次数基线 - response_count_baseline
- 交易响应率 - transaction_response_rate
- 交易响应率基线 - transaction_response_rate_baseline
- 交易处理时间 - trans_handle_time
- 交易失败率 - failed_rate
- 交易字段统计警报数 - transaction_fieldstat_alarm_count
- 交易客户端警报数 - transaction_client_alarm_count
- 交易总数 - total_count
- 交易总数占比 - total_count_ratio
- 交易总数基线 - total_count_baseline
- 交易成功率 - success_rate
- 交易成功率基线 - success_rate_baseline
- 交易无响应次数 - transaction_no_response_count
- 交易无响应率 - transaction_no_response_rate
- 交易日志警报数 - transaction_log_alarm_count
- 交易服务器警报数 - transaction_server_alarm_count
- 交易状态 - bargain_status
- 交易统计警报数 - transaction_stat_alarm_count
- 交易统计警报数 - transaction_total_stat_alarm_count
- 交易网段统计警报数 - transaction_netset_alarm_count
- 交易警报个数 - transaction_alarm_count
- 交易评价 - trans_evaluation
- 交易识别号（C） - bargain_recog_code_client
- 交易识别号（S） - bargain_recog_code_server
- 交易请求次数 - request_count
- 交易请求次数基线 - request_count_baseline
- 交易返回码 - return_code_value
- 交易返回码名称 - return_code_name
- 会话延迟 - post_dialing_delay
- 会话开始时间 - flow_start_time
- 会话开始时间 - start_time
- 会话开始时间(纳秒) - flow_start_time_ns
- 会话总数 - concurrent_flow_count
- 会话总数90p基线 - concurrent_flow_count_baseline_90p
- 会话总数基线 - concurrent_flow_count_baseline
- 会话持续时间 - duration_time
- 会话持续时间 - flow_duration
- 会话持续时间(纳秒) - flow_duration_ns
- 会话结束时间 - flow_end_time
- 会话结束时间(纳秒) - flow_end_time_ns
- 传输协议 - trans_protocol
- 传输效率 - total_packet_trans_rate
- 元数据字段 - metafields
- 关闭会话数 - close_flow_count
- 其他编码 - codec_other
- 其他编码(已关闭) - codec_other_close
- 其他编码(活动中) - codec_other_curr
- 出字节数 - out_byte
- 出数据包数 - out_packet
- 出网ICMP丢失包数 - outbound_icmp_lost_packets
- 出网ICMP丢失包率 - outbound_icmp_lost_packets_rate
- 出网RTT - outbound_rtt
- 出网TCP分段丢失包 - outbound_tcp_segment_lost_packet
- 出网TCP分段丢失率 - outbound_tcp_segment_lost_packet_rate
- 出网TCP同步包 - outbound_tcp_syn_packet
- 出网TCP同步确认包 - outbound_tcp_synack_packet
- 出网TCP同步确认重传包 - outbound_tcp_synack_retrans_packet
- 出网TCP同步重传包 - outbound_tcp_syn_retrans_packet
- 出网TCP数据包数（有效载荷） - outbound_tcp_effective_payload_packet
- 出网TCP确认包 - outbound_tcp_ack_packet
- 出网TCP结束包 - outbound_tcp_fin_packet
- 出网TCP负载数据包数 - outbound_tcp_payload_packet
- 出网TCP重传包 - outbound_tcp_retransmission_packet
- 出网TCP重传率 - outbound_tcp_retransmission_packet_rate
- 出网利用率 - outbound_utilizaiton
- 出网利用率90P基线 - outbound_utilizaiton_baseline_90p
- 出网利用率基线 - outbound_utilizaiton_baseline
- 出网利用率峰值 - peak_outbound_utilizaiton
- 出网带宽 - out_bandwidth
- 出网平均ACK时延 - outbound_avg_ack_delay
- 出网数据包 - outbound_packet
- 出网数据包90P基线 - outbound_packet_baseline_90p
- 出网数据包基线 - outbound_packet_baseline
- 出网数据包峰值 - peak_outbound_packet
- 出网数据包峰值基线 - peak_outbound_packet_baseline
- 出网数据包数（有效载荷） - outbound_payload_packet
- 出网数据包谷值 - valley_outbound_packet
- 出网数据包谷值基线 - valley_outbound_packet_baseline
- 出网最大ACK时延 - outbound_max_ack_delay
- 出网最小ACK时延 - outbound_min_ack_delay
- 出网每秒数据包数 - outbound_packetps
- 出网每秒数据包数90P基线 - outbound_packetps_baseline_90p
- 出网每秒数据包数基线 - outbound_packetps_baseline
- 出网每秒数据包数峰值 - peak_outbound_packetps
- 出网每秒数据包数峰值基线 - peak_outbound_packetps_baseline
- 出网每秒数据包数谷值 - valley_outbound_packetps
- 出网每秒数据包数谷值基线 - valley_outbound_packetps_baseline
- 出网比特率 - outbound_bitps
- 出网比特率90P基线 - outbound_bitps_baseline_90p
- 出网比特率95峰值 - peak_outbound_bitps_95p
- 出网比特率基线 - outbound_bitps_baseline
- 出网比特率峰值 - peak_outbound_bitps
- 出网比特率峰值基线 - peak_outbound_bitps_baseline
- 出网比特率谷值 - valley_outbound_bitps
- 出网比特率谷值基线 - valley_outbound_bitps_baseline
- 出网流量 - outbound_byte
- 出网流量90P基线 - outbound_byte_baseline_90p
- 出网流量95峰值 - peak_outbound_byte_95p
- 出网流量基线 - outbound_byte_baseline
- 出网流量峰值 - peak_outbound_byte
- 出网流量峰值基线 - peak_outbound_byte_baseline
- 出网流量谷值 - valley_outbound_byte
- 出网流量谷值基线 - valley_outbound_byte_baseline
- 分析丢失数据包 - analyse_drop_packet
- 分析丢失数据包 - analyse_drop_packet1
- 分析丢失比特率 - analyse_drop_bitps
- 分析丢失流量 - analyse_drop_byte
- 分析丢失流量 - analyse_drop_byte1
- 分类 - category
- 创建会话数 - create_flow_count
- 创建会话数基线 - create_flow_count_baseline
- 协议 - protocol
- 原始客户端 - raw_client_ip_addr
- 原始客户端网段 - raw_client_netsegment_id
- 发件人 - mail_sender
- 发送ARP响应数据包数 - tx_arp_res_packet
- 发送ARP请求数据包数 - tx_arp_req_packet
- 发送ICMP数据包数 - tx_icmp_packet
- 发送TCP分段丢失包 - tx_tcp_segment_lost_packet
- 发送TCP分段丢失率 - tx_tcp_segment_lost_packet_rate
- 发送TCP分段丢失率 - tx_tcp_segment_lost_rate
- 发送TCP同步包 - tx_tcp_syn_packet
- 发送TCP同步确认包 - tx_tcp_synack_packet
- 发送TCP同步确认重传包 - tx_tcp_synack_retrans_packet
- 发送TCP同步重传包 - tx_tcp_syn_retrans_packet
- 发送TCP字节数（有效载荷） - tx_tcp_effective_payload_byte
- 发送TCP数据包数（有效载荷） - tx_tcp_effective_payload_packet
- 发送TCP结束包 - tx_tcp_fin_packet
- 发送TCP负载数据包数 - tx_tcp_payload_packet
- 发送TCP重传包 - tx_tcp_retransmission_packet
- 发送TCP重传率 - tx_tcp_retransmission_rate
- 发送TCP重传率 - tx_total_packet_retrans_rate
- 发送TCP重置包 - tx_tcp_rst_packet
- 发送严重拥塞占比 - serious_congestion_rate_send
- 发送中度拥塞占比 - middle_congestion_rate_send
- 发送字节数 - tx_byte
- 发送字节数90P基线 - tx_byte_baseline_90p
- 发送字节数95峰值 - peak_tx_byte_95p
- 发送字节数基线 - tx_byte_baseline
- 发送字节数峰值 - peak_tx_byte
- 发送字节数峰值基线 - peak_tx_byte_baseline
- 发送字节数谷值 - valley_tx_byte
- 发送字节数谷值基线 - valley_tx_byte_baseline
- 发送字节数（有效载荷） - tx_payload_byte
- 发送平均包长 - tx_avg_pkt_size
- 发送数据包峰值 - peak_tx_packet
- 发送数据包峰值基线 - peak_tx_packet_baseline
- 发送数据包数 - tx_packet
- 发送数据包数90P基线 - tx_packet_baseline_90p
- 发送数据包数基线 - tx_packet_baseline
- 发送数据包数（有效载荷） - tx_payload_packet
- 发送数据包谷值 - valley_tx_packet
- 发送数据包谷值基线 - valley_tx_packet_baseline
- 发送每秒数据包数 - tx_packetps
- 发送每秒数据包数90P基线 - tx_packetps_baseline_90p
- 发送每秒数据包数基线 - tx_packetps_baseline
- 发送每秒数据包数峰值 - peak_tx_packetps
- 发送每秒数据包数峰值基线 - peak_tx_packetps_baseline
- 发送每秒数据包数谷值 - valley_tx_packetps
- 发送每秒数据包数谷值基线 - valley_tx_packetps_baseline
- 发送比特率 - tx_bitps
- 发送比特率90P基线 - tx_bitps_baseline_90p
- 发送比特率95峰值 - peak_tx_bitps_95p
- 发送比特率基线 - tx_bitps_baseline
- 发送比特率峰值 - peak_tx_bitps
- 发送比特率峰值基线 - peak_tx_bitps_baseline
- 发送比特率谷值 - valley_tx_bitps
- 发送比特率谷值基线 - valley_tx_bitps_baseline
- 发送比特率（有效载荷） - tx_payload_bitps
- 可疑域名警报数 - dns_alarm_count
- 名称 - msg_id
- 名称 - name
- 呼叫ID - call_id
- 呼叫总数 - summary_calls
- 呼叫数量(已关闭) - total_calls_close
- 呼叫数量(活动中) - total_calls_curr
- 呼叫时长 - total_duration
- 响应传输时间 - response_transport_time
- 响应内容 - server_data
- 响应发起端 - res_flow_sender_ip_addr
- 响应发起端地理位置 - res_flow_sender_ip_location
- 响应发起端端口 - res_flow_sender_port
- 响应字节数 - response_byte
- 响应开始时间 - first_res_pkt_time
- 响应接收端 - res_flow_receiver_ip_addr
- 响应接收端地理位置 - res_flow_receiver_ip_location
- 响应接收端端口 - res_flow_receiver_port
- 响应数据包开始序列号 - first_res_pkt_seq
- 响应数据包数 - response_packet
- 响应数据包结束序列号 - last_res_pkt_seq
- 响应时间 - response_rtt
- 响应比特率 - response_bitps
- 响应状态 - res_status
- 响应结束时间 - last_res_pkt_time
- 响应虚拟网标识 - multi_servertoclient_vn_id
- 响应虚拟网标识 - servertoclient_vn_id
- 响应评价 - res_evaluation
- 响应超时的TCP交易次数 - tcp_transaction_overtime_count
- 响应超时的TCP交易次数占比 - tcp_transaction_overtime_rate
- 响应超时的TCP交易次数基线 - tcp_transaction_worse_count_baseline
- 地理位置 - location
- 域名 - dns_domain
- 基线警报数 - baseline_alarm_count
- 失败 - status_failed
- 失败(已关闭) - status_failed_close
- 失败(活动中) - status_failed_curr
- 失败次数 - failed_count
- 好(视频) - mos_video_good
- 好(视频已关闭) - mos_video_good_close
- 好(视频活动中) - mos_video_good_curr
- 好(通话时长) - duration_good
- 好(音频) - mos_audio_good
- 好(音频已关闭) - mos_audio_good_close
- 好(音频活动中) - mos_audio_good_curr
- 好响应% - response_good_rate
- 好的TCP交易次数 - tcp_transaction_good_count
- 好的TCP交易次数占比 - tcp_transaction_good_rate
- 好的交易次数 - transaction_good_count
- 好的交易次数% - transaction_good_rate
- 好的呼叫次数 - call_good
- 好的呼叫次数(已关闭) - call_good_close
- 好的呼叫次数(活动中) - call_good_curr
- 好的响应次数 - response_good_count
- 媒体信令流警报个数 - flow_alarm_count
- 子链路id - sub_netlink_id
- 字段值 - stat_field_value
- 字段值1 - stat_field_value1
- 字段值2 - stat_field_value2
- 字段值3 - stat_field_value3
- 字段名称 - stat_field_name
- 字段名称1 - stat_field_name1
- 字段名称2 - stat_field_name2
- 字段名称3 - stat_field_name3
- 字节发收比 - byte_tr_ratio
- 客户端 - client_ip_addr
- 客户端0窗口总时延 - total_client_0WindowRTT
- 客户端DSCP - client_dscp
- 客户端DSCP Codepoint Name - client_dscp_name
- 客户端MAC地址 - client_mac
- 客户端RTT - client_rtt
- 客户端TCP乱序包数 - client_tcp_disorder_packet
- 客户端TCP分段丢失包 - client_tcp_segment_lost_packet
- 客户端TCP分段丢失率 - client_tcp_segment_loss_rate
- 客户端TCP分段丢失率 - client_tcp_segment_lost_packet_rate
- 客户端TCP初始窗口 - client_first_tcp_window
- 客户端TCP小窗口数 - client_small_window
- 客户端TCP窗口为0次数 - client_tcp_window_0
- 客户端TCP结束包 - client_tcp_fin_packet
- 客户端TCP重传包 - client_tcp_retransmission_packet
- 客户端TCP重传率 - client_tcp_retransmission_rate
- 客户端TCP重置包 - client_tcp_rst_packet
- 客户端三次握手平均RTT - client_tcp_three_handshake_avg_rtt
- 客户端三次握手平均RTT - endpoint1_tcp_three_handshake_avg_rtt
- 客户端三次握手总时延 - client_total_rtt
- 客户端个数 - client_count
- 客户端地理位置 - client_ip_location
- 客户端字节数 - client_total_byte
- 客户端字节数（有效载荷） - client_payload_byte
- 客户端字节数（有效载荷） - client_tcp_effective_payload_byte
- 客户端平均ACK时延 - client_avg_ack_delay
- 客户端平均包长 - client_avg_pkt_size
- 客户端平均空闲时间 - client_avg_idle_time
- 客户端平均窗口大小 - client_tcp_window_avg_size
- 客户端平均重传时延 - client_tcp_avg_retrans_time
- 客户端总重传时延 - client_tcp_total_retrans_time
- 客户端慢连接次数 - client_slowconnect
- 客户端数据包数 - client_total_packet
- 客户端数据包数（有效载荷） - client_tcp_effective_payload_packet
- 客户端最大ACK时延 - client_max_ack_delay
- 客户端最大空闲时间 - client_max_idle_time
- 客户端最大重传时延 - client_tcp_max_retrans_time
- 客户端最小ACK时延 - client_min_ack_delay
- 客户端最小空闲时间 - client_min_idle_time
- 客户端最小窗口大小 - client_tcp_window_min_size
- 客户端每秒数据包数 - client_total_packetps
- 客户端比特率 - client_bitps
- 客户端比特率峰值 - peak_client_bitps
- 客户端比特率（有效载荷） - client_payload_bitps
- 客户端比特率（有效载荷） - client_tcp_effective_payload_bitps
- 客户端目标地址(SRv6) - client_srv6_addr
- 客户端端口 - client_port
- 客户端类型 - client_type
- 客户端累计字节数 - client_cumulative_byte
- 客户端累计数据包数 - client_cumulative_packet
- 客户端累计负载数据包数 - client_cumulative_payload_packet
- 客户端网段 - client_netsegment_id
- 客户端警报数 - application_client_alarm_count
- 客户端请求平均传输时间 - tcp_transaction_avg_request_trans_time
- 客户端请求总传输时间 - tcp_transaction_total_request_trans_time
- 客户端请求最大传输时间 - tcp_transaction_max_request_trans_time
- 客户端负载数据包数 - client_payload_packet
- 客户端重传次数 - client_tcp_retrans_count
- 峰值数据包 - peak_total_packet
- 峰值流量 - peak_total_byte
- 差(视频) - mos_video_poor
- 差(视频已关闭) - mos_video_poor_close
- 差(视频活动中) - mos_video_poor_curr
- 差(通话时长) - duration_bad
- 差(音频) - mos_audio_poor
- 差(音频已关闭) - mos_audio_poor_close
- 差(音频活动中) - mos_audio_poor_curr
- 差响应% - response_bad_rate
- 差的TCP交易次数 - tcp_transaction_bad_count
- 差的交易次数 - transaction_bad_count
- 差的交易次数% - transaction_bad_rate
- 差的呼叫次数 - call_bad
- 差的呼叫次数(已关闭) - call_bad_close
- 差的呼叫次数(活动中) - call_bad_curr
- 差的响应次数 - response_bad_count
- 已接通 - status_conned
- 已接通(已关闭) - status_conned_close
- 已接通(活动中) - status_conned_curr
- 平均0窗口时延 - avg_0WindowRTT
- 平均丢包率 - avg_lost_packet_rate
- 平均交易处理时间 - trans_handle_avg_rtt
- 平均包长 - avg_pkt_size
- 平均响应传输时间 - trans_response_avg_rtt
- 平均响应时间 - response_avg_rtt
- 平均响应时间 - tcp_transaction_avg_rtt
- 平均响应时间90P基线 - response_avg_rtt_baseline_90p
- 平均响应时间基线 - response_avg_rtt_baseline
- 平均响应时间基线 - tcp_transaction_avg_rtt_baseline
- 平均客户端0窗口时延 - avg_client_0WindowRTT
- 平均抖动 - jitter_avg
- 平均服务器0窗口时延 - avg_server_0WindowRTT
- 平均用户响应时间 - tcp_trade_avg_res_time
- 平均视频MoS - video_mos_avg
- 平均请求传输时间 - trans_request_avg_rtt
- 平均连接建立时间 - establish_avg_rtt
- 平均通话时长 - avg_duration
- 平均音频MoS - audio_mos_avg
- 平均首次响应时延 - tcp_first_res_avg_rtt
- 广播数据包占比 - broadcast_packet_rate
- 广播数据包数 - broadcast_packet
- 广播数据包数基线 - broadcast_packet_baseline
- 广播流量 - broadcast_byte
- 应用 - application_id
- 应用交易基线警报数 - transaction_baseline_alarm_count
- 应用交易突发警报数 - transaction_emergency_alarm_count
- 应用分组 - application_group_id
- 应用名称 - application_name
- 应用基线警报数 - application_baseline_alarm_count
- 应用服务器突发警报数 - application_server_emergency_alarm_count
- 应用监控警报数 - application_monitor_alarm_count
- 应用突发警报数 - application_emergency_alarm_count
- 应用警报个数 - application_alarm_count
- 开始时间 - trans_start_time
- 异常IP数据包 - ip_abnormal_packet
- 异常IP流量 - ip_abnormal_byte
- 异常IP流量占比 - ip_abnormal_byte_rate
- 异常访问警报数 - black_alarm_count
- 异常非IP数据包 - non_ip_abnormal_packet
- 异常非IP流量 - non_ip_abnormal_byte
- 很差响应% - response_worse_rate
- 很差的TCP交易次数 - tcp_transaction_worse_count
- 很差的TCP交易次数占比 - tcp_transaction_worse_rate
- 很差的交易次数 - transaction_worse_count
- 很差的交易次数% - transaction_worse_rate
- 很差的响应次数 - response_worse_count
- 总利用率 - total_utilization
- 总利用率90P基线 - total_utilization_baseline_90p
- 总利用率基线 - total_utilization_baseline
- 总利用率峰值 - peak_total_utilization
- 总响应时间 - response_total_rtt
- 总响应时间90P基线 - response_total_rtt_baseline_90p
- 总响应时间基线 - response_total_rtt_baseline
- 总字节数 - total_byte
- 总字节数90P基线 - total_byte_baseline_90p
- 总字节数基线 - total_byte_baseline
- 总字节数（有效载荷） - total_payload_byte
- 总带宽 - total_bandwidth
- 总数据包 - total_packet
- 总数据包90P基线 - total_packet_baseline_90p
- 总数据包基线 - total_packet_baseline
- 总数据包峰值基线 - peak_total_packet_baseline
- 总数据包数（有效载荷） - total_payload_packet
- 总数据包谷值基线 - valley_total_packet_baseline
- 总流量峰值基线 - peak_total_byte_baseline
- 总流量峰值时间 - peak_total_byte_timestamp
- 总流量谷值基线 - valley_total_byte_baseline
- 总的首次响应时延 - tcp_first_res_total_rtt
- 总计(视频MoS) - mos_video_total
- 总计(音频MoS) - mos_audio_total
- 慢连接占比 - slowconnect_ratio
- 慢连接次数 - slowconnect
- 成功 - status_success
- 成功(已关闭) - status_success_close
- 成功(活动中) - status_success_curr
- 成功次数 - success_count
- 成功次数基线 - success_count_baseline
- 成功的TCP交易次数 - successful_tcp_transaction_total_count
- 成功的TCP交易次数基线 - successful_tcp_transaction_total_count_baseline
- 所有交易总数 - all_total_count
- 所有警报个数 - total_alarm_count
- 抄送 - mail_cc
- 抖动总值 - jitter_total
- 抖动计数 - jitter_count
- 拒绝 - status_reject
- 拒绝(已关闭) - status_reject_close
- 拒绝(活动中) - status_reject_curr
- 持续时间 - trigger_span
- 挖掘交易日志ID - drillUniqueId
- 接口标识 - netflow_id
- 接收ICMP数据包数 - rx_icmp_packet
- 接收TCP分段丢失包 - rx_tcp_segment_lost_packet
- 接收TCP分段丢失率 - rx_tcp_segment_lost_packet_rate
- 接收TCP分段丢失率 - rx_tcp_segment_lost_rate
- 接收TCP同步包 - rx_tcp_syn_packet
- 接收TCP同步确认包 - rx_tcp_synack_packet
- 接收TCP同步确认重传包 - rx_tcp_synack_retrans_packet
- 接收TCP同步重传包 - rx_tcp_syn_retrans_packet
- 接收TCP字节数（有效载荷） - rx_tcp_effective_payload_byte
- 接收TCP数据包数（有效载荷） - rx_tcp_effective_payload_packet
- 接收TCP结束包 - rx_tcp_fin_packet
- 接收TCP负载数据包数 - rx_tcp_payload_packet
- 接收TCP重传包 - rx_tcp_retransmission_packet
- 接收TCP重传率 - rx_tcp_retransmission_rate
- 接收TCP重传率 - rx_total_packet_retrans_rate
- 接收TCP重置包 - rx_tcp_rst_packet
- 接收严重拥塞占比 - serious_congestion_rate_rec
- 接收中度拥塞占比 - middle_congestion_rate_rec
- 接收字节数 - rx_byte
- 接收字节数90P基线 - rx_byte_baseline_90p
- 接收字节数95峰值 - peak_rx_byte_95p
- 接收字节数基线 - rx_byte_baseline
- 接收字节数峰值 - peak_rx_byte
- 接收字节数峰值基线 - peak_rx_byte_baseline
- 接收字节数谷值 - valley_rx_byte
- 接收字节数谷值基线 - valley_rx_byte_baseline
- 接收字节数（有效载荷） - rx_payload_byte
- 接收平均包长 - rx_avg_pkt_size
- 接收数据包峰值 - peak_rx_packet
- 接收数据包数 - rx_packet
- 接收数据包数90P基线 - rx_packet_baseline_90p
- 接收数据包数基线 - rx_packet_baseline
- 接收数据包数峰值基线 - peak_rx_packet_baseline
- 接收数据包数谷值基线 - valley_rx_packet_baseline
- 接收数据包数（有效载荷） - rx_payload_packet
- 接收数据包谷值 - valley_rx_packet
- 接收每秒数据包数 - rx_packetps
- 接收每秒数据包数90P基线 - rx_packetps_baseline_90p
- 接收每秒数据包数基线 - rx_packetps_baseline
- 接收每秒数据包数峰值 - peak_rx_packetps
- 接收每秒数据包数峰值基线 - peak_rx_packetps_baseline
- 接收每秒数据包数谷值 - valley_rx_packetps
- 接收每秒数据包数谷值基线 - valley_rx_packetps_baseline
- 接收比特率 - rx_bitps
- 接收比特率90P基线 - rx_bitps_baseline_90p
- 接收比特率95峰值 - peak_rx_bitps_95p
- 接收比特率基线 - rx_bitps_baseline
- 接收比特率峰值 - peak_rx_bitps
- 接收比特率峰值基线 - peak_rx_bitps_baseline
- 接收比特率谷值 - valley_rx_bitps
- 接收比特率谷值基线 - valley_rx_bitps_baseline
- 接收比特率（有效载荷） - rx_payload_bitps
- 接通中 - status_conning
- 接通中(已关闭) - status_conning_close
- 接通中(活动中) - status_conning_curr
- 接通率 - conn_ratio
- 描述 - description
- 提取任务ID - task_id
- 收件人 - mail_recipients
- 放弃 - status_giveup
- 放弃(已关闭) - status_giveup_close
- 放弃(活动中) - status_giveup_curr
- 数据包发收比 - packet_tr_ratio
- 数据包开始时间 - packet_start_time
- 数据包结束时间 - packet_end_time
- 数据流特征值警报数 - signature_alarm_count
- 新建会话数 - new_flow_count
- 新建会话数90p基线 - new_flow_count_baseline_90p
- 新建会话数基线 - new_flow_count_baseline
- 日志id - unique_id
- 时间 - time
- 最后访问时间 - server_last_time
- 最大0窗口时延 - max_tcp_0WindowRTT
- 最大交易处理时间 - trans_handle_max_rtt
- 最大响应传输时间 - response_trans_max_rtt
- 最大响应时间 - response_max_rtt
- 最大响应时间 - tcp_transaction_max_rtt
- 最大响应时间基线 - response_max_rtt_baseline
- 最大响应时间基线 - tcp_transaction_max_rtt_baseline
- 最大客户端0窗口时延 - max_client_0WindowRTT
- 最大服务器0窗口时延 - max_server_0WindowRTT
- 最大用户响应时间 - tcp_trade_max_res_time
- 最大视频MoS - video_mos_max
- 最大请求传输时间 - request_trans_max_rtt
- 最大连接建立时间 - establish_max_rtt
- 最大音频MoS - audio_mos_max
- 最大首次响应时延 - tcp_first_res_max_rtt
- 最小0窗口时延 - min_tcp_0WindowRTT
- 最小交易处理时间 - trans_handle_min_rtt
- 最小响应传输时间 - response_trans_min_rtt
- 最小响应时间 - response_min_rtt
- 最小响应时间 - tcp_transaction_min_rtt
- 最小响应时间基线 - response_min_rtt_baseline
- 最小响应时间基线 - tcp_transaction_min_rtt_baseline
- 最小客户端0窗口时延 - min_client_0WindowRTT
- 最小服务器0窗口时延 - min_server_0WindowRTT
- 最小视频MoS - video_mos_min
- 最小请求传输时间 - request_trans_min_rtt
- 最小连接建立时间 - establish_min_rtt
- 最小音频MoS - audio_mos_min
- 服务器 - server_ip_addr
- 服务器0窗口总时延 - total_server_0WindowRTT
- 服务器DSCP - server_dscp
- 服务器DSCP Codepoint Name - server_dscp_name
- 服务器MAC地址 - server_mac
- 服务器RTT - server_rtt
- 服务器TCP乱序包数 - server_tcp_disorder_packet
- 服务器TCP分段丢失包 - server_tcp_segment_lost_packet
- 服务器TCP分段丢失率 - server_tcp_segment_loss_rate
- 服务器TCP分段丢失率 - server_tcp_segment_lost_packet_rate
- 服务器TCP初始窗口 - server_first_tcp_window
- 服务器TCP小窗口数 - server_small_window
- 服务器TCP窗口为0次数 - server_tcp_window_0
- 服务器TCP结束包 - server_tcp_fin_packet
- 服务器TCP重传包 - server_tcp_retransmission_packet
- 服务器TCP重传率 - server_tcp_retransmission_rate
- 服务器TCP重置包 - server_tcp_rst_packet
- 服务器三次握手平均RTT - endpoint2_tcp_three_handshake_avg_rtt
- 服务器三次握手平均RTT - server_tcp_three_handshake_avg_rtt
- 服务器三次握手总时延 - server_total_rtt
- 服务器响应平均传输时间 - tcp_transaction_avg_response_trans_time
- 服务器响应总传输时间 - tcp_transaction_total_response_trans_time
- 服务器响应最大传输时间 - tcp_transaction_max_response_trans_time
- 服务器地理位置 - server_ip_location
- 服务器字节数 - server_total_byte
- 服务器字节数（有效载荷） - server_payload_byte
- 服务器字节数（有效载荷） - server_tcp_effective_payload_byte
- 服务器平均ACK时延 - server_avg_ack_delay
- 服务器平均包长 - server_avg_pkt_size
- 服务器平均响应时间 - tcp_transaction_avg_response_time
- 服务器平均窗口大小 - server_tcp_window_avg_size
- 服务器平均重传时延 - server_tcp_avg_retrans_time
- 服务器总响应时间 - tcp_transaction_total_response_time
- 服务器总响应时间 - tcp_transaction_total_rtt
- 服务器总重传时延 - server_tcp_total_retrans_time
- 服务器慢连接次数 - server_slowconnect
- 服务器数据包数 - server_total_packet
- 服务器数据包数（有效载荷） - server_tcp_effective_payload_packet
- 服务器最大ACK时延 - server_max_ack_delay
- 服务器最大重传时延 - server_tcp_max_retrans_time
- 服务器最小ACK时延 - server_min_ack_delay
- 服务器最小窗口大小 - server_tcp_window_min_size
- 服务器每秒数据包数 - server_total_packetps
- 服务器比特率 - server_bitps
- 服务器比特率峰值 - peak_server_bitps
- 服务器比特率（有效载荷） - server_payload_bitps
- 服务器比特率（有效载荷） - server_tcp_effective_payload_bitps
- 服务器目标地址(SRv6) - server_srv6_addr
- 服务器端口 - server_port
- 服务器累计字节数 - server_cumulative_byte
- 服务器累计数据包数 - server_cumulative_packet
- 服务器累计负载数据包数 - server_cumulative_payload_packet
- 服务器网段 - server_netsegment_id
- 服务器负载数据包数 - server_payload_packet
- 服务器重传次数 - server_tcp_retrans_count
- 服务开始时间 - server_start_time
- 服务端警报数 - application_server_alarm_count
- 服务访问警报数 - application_service_access_alarm_count
- 期望数据包数 - expect_packet
- 校验和错误 - crc_err_packets
- 段内平均抖动 - internal_jitter_avg
- 段内抖动总值 - internal_jitter_total
- 段内抖动计数 - internal_jitter_count
- 段内最小抖动 - internal_jitter_min
- 段内视频MoS总值 - internal_video_mos_total
- 段内视频MoS计数 - internal_video_mos_count
- 段内音频MoS总值 - internal_audio_mos_total
- 段内音频MoS计数 - internal_audio_mos_count
- 每秒交易数量（平均） - trans_avg_count_per_second
- 每秒交易笔数峰值 - peak_total_count
- 每秒交易笔数峰值基线 - peak_total_count_baseline
- 每秒关闭会话数 - close_flow_countps
- 每秒出数据包数 - out_packetps
- 每秒出网流量 - outbound_byteps
- 每秒出网流量90P基线 - outbound_byteps_baseline_90p
- 每秒出网流量基线 - outbound_byteps_baseline
- 每秒分析丢失数据包 - analyse_drop_packetps
- 每秒创建会话数 - create_flow_countps
- 每秒发送字节数 - tx_byteps
- 每秒响应字节数 - response_byteps
- 每秒响应数据包数 - response_packetps
- 每秒字节数 - total_byteps
- 每秒字节数90P基线 - total_byteps_baseline_90p
- 每秒字节数基线 - total_byteps_baseline
- 每秒广播数据包 - broadcast_packetps
- 每秒接收字节数 - rx_byteps
- 每秒数据包数 - total_packetps
- 每秒数据包数90P基线 - total_packetps_baseline_90p
- 每秒数据包数基线 - total_packetps_baseline
- 每秒数据包数峰值 - peak_total_packetps
- 每秒数据包数峰值基线 - peak_total_packetps_baseline
- 每秒数据包数谷值 - valley_total_packetps
- 每秒数据包数谷值基线 - valley_total_packetps_baseline
- 每秒活动会话数 - alive_flow_countps
- 每秒组播数据包 - multicast_packetps
- 每秒请求字节数 - request_byteps
- 每秒请求数据包数 - request_packetps
- 每秒进数据包数 - in_packetps
- 每秒进网流量 - inbound_byteps
- 每秒进网流量90P基线 - inbound_byteps_baseline_90p
- 每秒进网流量基线 - inbound_byteps_baseline
- 每秒驱动丢失数据包 - adapter_drop_packetps
- 比特率 - total_bitps
- 比特率90P基线 - total_bitps_baseline_90p
- 比特率95峰值 - peak_total_bitps_95p
- 比特率基线 - total_bitps_baseline
- 比特率峰值 - peak_total_bitps
- 比特率峰值基线 - peak_total_bitps_baseline
- 比特率谷值 - valley_total_bitps
- 比特率谷值基线 - valley_total_bitps_baseline
- 比特率（有效载荷） - total_payload_bitps
- 比特率（有效载荷） - total_tcp_effective_payload_bitps
- 毫秒级流量警报数 - millisecond_alarm_count
- 活动会话数 - alive_flow_count
- 流类型 - flow_type
- 流量95峰值 - peak_total_byte_95p
- 流量警报数 - traffic_alarm_count
- 源IP地址 - src_ip_addr
- 源IP地址地理位置 - src_ip_location
- 源IP的网段id - src_netsegment_id
- 源端口 - src_port
- 物理会话端点1 - mac_endpoint1
- 物理会话端点2 - mac_endpoint2
- 物理地址 - mac
- 状态 - status
- 状态 - tcp_status
- 用户总响应时间 - tcp_trade_total_res_time
- 白名单警报数 - white_alarm_count
- 目标IP地址 - dst_ip_addr
- 目标IP地址地理位置 - dst_ip_location
- 目标IP的网段id - dst_netsegment_id
- 目标端口 - dst_port
- 码率 - total_code_bitps
- 码率峰值 - peak_total_code_bitps
- 突发警报数 - emergency_alarm_count
- 端口复用失败次数 - tcp_port_reused_failed
- 端点1 - ip_endpoint1
- 端点1ACK时延总时长 - endpoint1_total_ack_delay
- 端点1ACK时延总次数 - endpoint1_ack_delay_count
- 端点1DSCP - endpoint1_dscp
- 端点1DSCP Codepoint Name - endpoint1_dscp_name
- 端点1ICMP丢包数 - endpoint1_icmp_lost_packets
- 端点1ICMP丢包率 - endpoint1_icmp_packets_loss_rate
- 端点1ICMP响应数 - endpoint1_icmp_res_count
- 端点1ICMP平均回应时间 - endpoint1_icmp_avg_res_time
- 端点1ICMP最大回应时间 - endpoint1_icmp_max_res_time
- 端点1ICMP最小回应时间 - endpoint1_icmp_min_res_time
- 端点1MAC地址 - endpoint1_mac
- 端点1TCP乱序包数 - endpoint1_tcp_disorder_packet
- 端点1TCP窗口为0次数 - ep1_tcp_window_0
- 端点1上行R值 - ep1_uplink_Rvalue
- 端点1上行R值 - seg1_uplink_Rvalue
- 端点1上行丢包数 - ep1_uplink_lost_packet
- 端点1上行丢包数 - seg1_uplink_lost_packet
- 端点1上行丢包率 - ep1_uplink_lost_packet_rate
- 端点1上行丢包率 - seg1_uplink_lost_packet_rate
- 端点1上行乱序包数 - ep1_uplink_wrong_seq_packet
- 端点1上行乱序包数 - seg1_uplink_wrong_seq_packet
- 端点1上行字节数 - ep1_uplink_bytes
- 端点1上行字节数 - seg1_uplink_bytes
- 端点1上行平均抖动 - seg1_uplink_jitter_avg
- 端点1上行平均视频MoS - ep1_uplink_video_mos_avg
- 端点1上行平均视频MoS - seg1_uplink_video_mos_avg
- 端点1上行平均音频MoS - ep1_uplink_audio_mos_avg
- 端点1上行平均音频MoS - seg1_uplink_audio_mos_avg
- 端点1上行延时 - ep1_uplink_rtt
- 端点1上行延时 - seg1_uplink_rtt
- 端点1上行抖动 - ep1_uplink_jitter
- 端点1上行抖动 - seg1_uplink_jitter
- 端点1上行抖动总值 - seg1_uplink_jitter_total
- 端点1上行抖动计数 - seg1_uplink_jitter_count
- 端点1上行数据包 - ep1_uplink_packet
- 端点1上行数据包 - seg1_uplink_packet
- 端点1上行最大抖动 - ep1_uplink_jitter_max
- 端点1上行最大抖动 - seg1_uplink_jitter_max
- 端点1上行最大视频MoS - ep1_uplink_video_mos_max
- 端点1上行最大视频MoS - seg1_uplink_video_mos_max
- 端点1上行最大音频MoS - ep1_uplink_audio_mos_max
- 端点1上行最大音频MoS - seg1_uplink_audio_mos_max
- 端点1上行最小抖动 - seg1_uplink_jitter_min
- 端点1上行最小视频MoS - ep1_uplink_video_mos_min
- 端点1上行最小视频MoS - seg1_uplink_video_mos_min
- 端点1上行最小音频MoS - ep1_uplink_audio_mos_min
- 端点1上行最小音频MoS - seg1_uplink_audio_mos_min
- 端点1上行期望数据包 - ep1_uplink_expect_packet
- 端点1上行期望数据包 - seg1_uplink_expect_packet
- 端点1上行每秒字节数 - ep1_uplink_byteps
- 端点1上行每秒字节数 - seg1_uplink_byteps
- 端点1上行码率 - ep1_uplink_code_bitps
- 端点1上行码率 - seg1_uplink_code_bitps
- 端点1上行视频MoS - ep1_uplink_video_mos
- 端点1上行视频MoS - seg1_uplink_video_mos
- 端点1上行视频MoS总值 - seg1_uplink_video_mos_total
- 端点1上行视频MoS计数 - seg1_uplink_video_mos_count
- 端点1上行音频MoS - ep1_uplink_audio_mos
- 端点1上行音频MoS - seg1_uplink_audio_mos
- 端点1上行音频MoS总值 - seg1_uplink_audio_mos_total
- 端点1上行音频MoS计数 - seg1_uplink_audio_mos_count
- 端点1分段丢失率 - endpoint1_packet_lost_rate
- 端点1发送ICMP数据包 - endpoint1_tx_icmp_packets
- 端点1发送TCP分段丢失包 - endpoint1_tx_tcp_segment_lost_packet
- 端点1发送TCP同步包 - endpoint1_tx_tcp_syn_packet
- 端点1发送TCP同步确认包 - endpoint1_tx_tcp_synack_packet
- 端点1发送TCP同步确认重传包 - endpoint1_tx_tcp_synack_retrans_packet
- 端点1发送TCP同步重传包 - endpoint1_tx_tcp_syn_retrans_packet
- 端点1发送TCP数据包数（有效载荷） - endpoint1_tcp_effective_payload_packet
- 端点1发送TCP重传包 - endpoint1_tx_tcp_retransmission_packet
- 端点1发送TCP重复确认包 - endpoint1_tx_tcp_duplicate_ack_packet
- 端点1发送TCP重置包 - endpoint1_tx_tcp_rst_packet
- 端点1发送字节数 - endpoint1_tx_byte
- 端点1发送带负载的TCP数据包数 - endpoint1_tx_tcp_all_payload_packet
- 端点1发送数据包数 - endpoint1_tx_packet
- 端点1发送比特率 - endpoint1_tx_bitps
- 端点1发送比特率峰值 - peak_endpoint1_tx_bitps
- 端点1发送负载数据包数 - endpoint1_tx_payload_packet
- 端点1回送应答包 - endpoint1_icmp_echo_response_packets
- 端点1回送请求包 - endpoint1_icmp_echo_request_packets
- 端点1地理位置 - location1
- 端点1平均ACK时延 - endpoint1_avg_ack_delay
- 端点1平均窗口大小 - ep1_tcp_window_avg_size
- 端点1接收ICMP数据包 - endpoint1_rx_icmp_packets
- 端点1最小窗口大小 - ep1_tcp_window_min_size
- 端点1每秒发送字节数 - endpoint1_tx_byteps
- 端点1每秒发送数据包数 - endpoint1_tx_packetps
- 端点1重传率 - endpoint1_packet_retrans_rate
- 端点2 - ip_endpoint2
- 端点2ACK时延总时长 - endpoint2_total_ack_delay
- 端点2ACK时延总次数 - endpoint2_ack_delay_count
- 端点2DSCP - endpoint2_dscp
- 端点2DSCP Codepoint Name - endpoint2_dscp_name
- 端点2ICMP丢包数 - endpoint2_icmp_lost_packets
- 端点2ICMP丢包率 - endpoint2_icmp_packets_loss_rate
- 端点2ICMP响应数 - endpoint2_icmp_res_count
- 端点2ICMP平均回应时间 - endpoint2_icmp_avg_res_time
- 端点2ICMP最大回应时间 - endpoint2_icmp_max_res_time
- 端点2ICMP最小回应时间 - endpoint2_icmp_min_res_time
- 端点2MAC地址 - endpoint2_mac
- 端点2TCP乱序包数 - endpoint2_tcp_disorder_packet
- 端点2TCP窗口为0次数 - ep2_tcp_window_0
- 端点2上行R值 - ep2_uplink_Rvalue
- 端点2上行R值 - seg2_uplink_Rvalue
- 端点2上行丢包数 - ep2_uplink_lost_packet
- 端点2上行丢包数 - seg2_uplink_lost_packet
- 端点2上行丢包率 - ep2_uplink_lost_packet_rate
- 端点2上行丢包率 - seg2_uplink_lost_packet_rate
- 端点2上行字节数 - ep2_uplink_bytes
- 端点2上行字节数 - seg2_uplink_bytes
- 端点2上行平均抖动 - seg2_uplink_jitter_avg
- 端点2上行平均视频MoS - ep2_uplink_video_mos_avg
- 端点2上行平均视频MoS - seg2_uplink_video_mos_avg
- 端点2上行平均音频MoS - ep2_uplink_audio_mos_avg
- 端点2上行平均音频MoS - seg2_uplink_audio_mos_avg
- 端点2上行延时 - ep2_uplink_rtt
- 端点2上行延时 - seg2_uplink_rtt
- 端点2上行抖动 - ep2_uplink_jitter
- 端点2上行抖动 - seg2_uplink_jitter
- 端点2上行抖动总值 - seg2_uplink_jitter_total
- 端点2上行抖动计数 - seg2_uplink_jitter_count
- 端点2上行数据包 - ep2_uplink_packet
- 端点2上行数据包 - seg2_uplink_packet
- 端点2上行最大抖动 - ep2_uplink_jitter_max
- 端点2上行最大抖动 - seg2_uplink_jitter_max
- 端点2上行最大视频MoS - ep2_uplink_video_mos_max
- 端点2上行最大视频MoS - seg2_uplink_video_mos_max
- 端点2上行最大音频MoS - ep2_uplink_audio_mos_max
- 端点2上行最大音频MoS - seg2_uplink_audio_mos_max
- 端点2上行最小抖动 - seg2_uplink_jitter_min
- 端点2上行最小视频MoS - ep2_uplink_video_mos_min
- 端点2上行最小视频MoS - seg2_uplink_video_mos_min
- 端点2上行最小音频MoS - ep2_uplink_audio_mos_min
- 端点2上行最小音频MoS - seg2_uplink_audio_mos_min
- 端点2上行期望数据包 - ep2_uplink_expect_packet
- 端点2上行期望数据包 - seg2_uplink_expect_packet
- 端点2上行每秒字节数 - ep2_uplink_byteps
- 端点2上行每秒字节数 - seg2_uplink_byteps
- 端点2上行码率 - ep2_uplink_code_bitps
- 端点2上行码率 - seg2_uplink_code_bitps
- 端点2上行视频MoS - ep2_uplink_video_mos
- 端点2上行视频MoS - seg2_uplink_video_mos
- 端点2上行视频MoS总值 - seg2_uplink_video_mos_total
- 端点2上行视频MoS计数 - seg2_uplink_video_mos_count
- 端点2上行音频MoS - ep2_uplink_audio_mos
- 端点2上行音频MoS - seg2_uplink_audio_mos
- 端点2上行音频MoS总值 - seg2_uplink_audio_mos_total
- 端点2上行音频MoS计数 - seg2_uplink_audio_mos_count
- 端点2下行乱序包数 - ep2_uplink_wrong_seq_packet
- 端点2下行乱序包数 - seg2_uplink_wrong_seq_packet
- 端点2分段丢失率 - endpoint2_packet_lost_rate
- 端点2发送ICMP数据包 - endpoint2_tx_icmp_packets
- 端点2发送TCP分段丢失包 - endpoint2_tx_tcp_segment_lost_packet
- 端点2发送TCP同步包 - endpoint2_tx_tcp_syn_packet
- 端点2发送TCP同步确认包 - endpoint2_tx_tcp_synack_packet
- 端点2发送TCP同步确认重传包 - endpoint2_tx_tcp_synack_retrans_packet
- 端点2发送TCP同步重传包 - endpoint2_tx_tcp_syn_retrans_packet
- 端点2发送TCP数据包数（有效载荷） - endpoint2_tcp_effective_payload_packet
- 端点2发送TCP重传包 - endpoint2_tx_tcp_retransmission_packet
- 端点2发送TCP重复确认包 - endpoint2_tx_tcp_duplicate_ack_packet
- 端点2发送TCP重置包 - endpoint2_tx_tcp_rst_packet
- 端点2发送字节数 - endpoint2_tx_byte
- 端点2发送带负载的TCP数据包数 - endpoint2_tx_tcp_all_payload_packet
- 端点2发送数据包数 - endpoint2_tx_packet
- 端点2发送比特率 - endpoint2_tx_bitps
- 端点2发送比特率峰值 - peak_endpoint2_tx_bitps
- 端点2发送负载数据包数 - endpoint2_tx_payload_packet
- 端点2回送应答包 - endpoint2_icmp_echo_response_packets
- 端点2回送请求包 - endpoint2_icmp_echo_request_packets
- 端点2地理位置 - location2
- 端点2平均ACK时延 - endpoint2_avg_ack_delay
- 端点2平均窗口大小 - ep2_tcp_window_avg_size
- 端点2接收ICMP数据包 - endpoint2_rx_icmp_packets
- 端点2最小窗口大小 - ep2_tcp_window_min_size
- 端点2每秒发送字节数 - endpoint2_tx_byteps
- 端点2每秒发送数据包数 - endpoint2_tx_packetps
- 端点2重传率 - endpoint2_packet_retrans_rate
- 端的1目标地址(SRv6) - endpoint1_srv6_addr
- 端的2目标地址(SRv6) - endpoint2_srv6_addr
- 第一个负载包IP标识号 - first_payload_pkt_ipid
- 第一个负载包TCP序列号 - first_payload_pkt_seq
- 第一秒活动会话数 - alive_flow_count_pre
- 类型 - application_group_type
- 类型 - msg_type
- 类型 - netsegment_type
- 累计字节数 - total_cumulative_byte
- 累计数据包数 - total_cumulative_packet
- 繁忙 - status_busy
- 繁忙(已关闭) - status_busy_close
- 繁忙(活动中) - status_busy_curr
- 组播数据包占比 - multicast_packet_rate
- 组播数据包数 - multicast_packet
- 组播流量 - multicast_byte
- 终端 - endpoint
- 终端1 - endpoint1
- 终端1 - endpoint1_name
- 终端1上行平均抖动 - ep1_uplink_jitter_avg
- 终端1上行抖动总值 - ep1_uplink_jitter_total
- 终端1上行抖动计数 - ep1_uplink_jitter_count
- 终端1上行最小抖动 - ep1_uplink_jitter_min
- 终端1上行视频MoS总值 - ep1_uplink_video_mos_total
- 终端1上行视频MoS计数 - ep1_uplink_video_mos_count
- 终端1上行音频MoS总值 - ep1_uplink_audio_mos_total
- 终端1上行音频MoS计数 - ep1_uplink_audio_mos_count
- 终端1地址 - endpoint1_ip
- 终端2 - endpoint2
- 终端2 - endpoint2_name
- 终端2上行平均抖动 - ep2_uplink_jitter_avg
- 终端2上行抖动总值 - ep2_uplink_jitter_total
- 终端2上行抖动计数 - ep2_uplink_jitter_count
- 终端2上行最小抖动 - ep2_uplink_jitter_min
- 终端2上行视频MoS总值 - ep2_uplink_video_mos_total
- 终端2上行视频MoS计数 - ep2_uplink_video_mos_count
- 终端2上行音频MoS总值 - ep2_uplink_audio_mos_total
- 终端2上行音频MoS计数 - ep2_uplink_audio_mos_count
- 终端2地址 - endpoint2_ip
- 终端ID - deviceSN_str
- 结束时间 - trans_end_time
- 统计时间 - stat_time
- 维度过滤器 - dimensionFilter
- 编码1 - codec_1
- 编码1(已关闭) - codec_1_close
- 编码1(活动中) - codec_1_curr
- 编码2 - codec_2
- 编码2(已关闭) - codec_2_close
- 编码2(活动中) - codec_2_curr
- 编码3 - codec_3
- 编码3(已关闭) - codec_3_close
- 编码3(活动中) - codec_3_curr
- 编码4 - codec_4
- 编码4(已关闭) - codec_4_close
- 编码4(活动中) - codec_4_curr
- 编码5 - codec_5
- 编码5(已关闭) - codec_5_close
- 编码5(活动中) - codec_5_curr
- 编码总数 - codec_total
- 网内R值 - internal_Rvalue
- 网内TCP分段丢失包 - internal_tcp_segment_lost_packet
- 网内TCP同步包 - internal_tcp_syn_packet
- 网内TCP同步确认包 - internal_tcp_synack_packet
- 网内TCP同步确认重传包 - internal_tcp_synack_retrans_packet
- 网内TCP同步重传包 - internal_tcp_syn_retrans_packet
- 网内TCP数据包数（有效载荷） - internal_tcp_effective_payload_packet
- 网内TCP负载数据包数 - internal_tcp_payload_packet
- 网内TCP重置包 - internal_tcp_rst_packet
- 网内丢包数 - internal_lost_packet
- 网内丢包率 - internal_lost_packet_rate
- 网内乱序包数 - internal_wrong_seq_packet
- 网内字节数 - internal_bytes
- 网内平均视频MoS - internal_video_mos_avg
- 网内平均音频MoS - internal_audio_mos_avg
- 网内延时 - internal_rtt
- 网内抖动 - internal_jitter
- 网内数据包 - internal_packet
- 网内数据包90P基线 - internal_packet_baseline_90p
- 网内数据包基线 - internal_packet_baseline
- 网内数据包数（有效载荷） - internal_payload_packet
- 网内最大抖动 - internal_jitter_max
- 网内最大视频MoS - internal_video_mos_max
- 网内最大音频MoS - internal_audio_mos_max
- 网内最小视频MoS - internal_video_mos_min
- 网内最小音频MoS - internal_audio_mos_min
- 网内期望数据包数 - internal_expect_packet
- 网内每秒字节数 - internal_byteps
- 网内每秒数据包 - internal_packetps
- 网内比特率 - internal_bitps
- 网内流量 - internal_byte
- 网内流量90P基线 - internal_byte_baseline_90p
- 网内流量基线 - internal_byte_baseline
- 网内码率 - internal_code_bitps
- 网内音频MoS - internal_audio_mos
- 网内音频MoS - internal_video_mos
- 网外TCP分段丢失包 - external_tcp_segment_lost_packet
- 网外TCP同步包 - external_tcp_syn_packet
- 网外TCP同步确认包 - external_tcp_synack_packet
- 网外TCP同步确认重传包 - external_tcp_synack_retrans_packet
- 网外TCP同步重传包 - external_tcp_syn_retrans_packet
- 网外TCP数据包数（有效载荷） - external_tcp_effective_payload_packet
- 网外TCP负载数据包数 - external_tcp_payload_packet
- 网外TCP重置包 - external_tcp_rst_packet
- 网外数据包 - external_packet
- 网外数据包数（有效载荷） - external_payload_packet
- 网外每秒数据包 - external_packetps
- 网外比特率 - external_bitps
- 网外流量 - external_byte
- 网段 - netsegment_id
- 网段1 - netsegment_id1
- 网段2 - netsegment_id2
- 网段分组 - netsegment_group_id
- 网段分组警报数 - application_netsegment_group_alarm_count
- 网段警报数 - application_segment_alarm_count
- 自定义字段值1 - stat_field_value_str
- 自定义字段值2 - stat_field_value2_str
- 自定义字段值3 - stat_field_value3_str
- 自定义字段名称1 - stat_field_name_str
- 自定义字段名称2 - stat_field_name2_str
- 自定义字段名称3 - stat_field_name3_str
- 虚拟网标识 - multi_vlan_id
- 虚拟网标识 - vlan_id
- 虚拟网类型 - vnType
- 虚拟网类型 - vn_type
- 被叫 - callee
- 被叫名称 - callee_name
- 被叫端口 - callee_port
- 被叫端点 - callee_ip
- 被叫端点MAC地址 - callee_mac
- 被叫网段 - caller_netsegment_id2
- 视频MoS - video_mos
- 视频MoS总值 - video_mos_total
- 视频MoS计数 - video_mos_count
- 视频Mos评估值 - video_mos_assessment
- 视频编码 - video_codec
- 触发时间 - trigger_time
- 触发条件 - trigger_condition
- 触发维度 - dimensionText
- 警报类型 - type
- 警报级别 - level
- 设备 - device_addr
- 访问次数 - visit_count
- 请求传输时间 - request_transport_time
- 请求内容 - client_data
- 请求发起端 - req_flow_sender_ip_addr
- 请求发起端地理位置 - req_flow_sender_ip_location
- 请求发起端端口 - req_flow_sender_port
- 请求字节数 - request_byte
- 请求开始时间 - first_req_pkt_time
- 请求接收端 - req_flow_receiver_ip_addr
- 请求接收端地理位置 - req_flow_receiver_ip_location
- 请求接收端端口 - req_flow_receiver_port
- 请求数据包开始序列号 - first_req_pkt_seq
- 请求数据包数 - request_packet
- 请求数据包结束序列号 - last_req_pkt_seq
- 请求比特率 - request_bitps
- 请求结束时间 - last_req_pkt_time
- 请求虚拟网标识 - clienttoserver_vn_id
- 请求虚拟网标识 - multi_clienttoserver_vn_id
- 谷值数据包 - valley_total_packet
- 谷值流量 - valley_total_byte
- 返回码 - return_code_name_str
- 返回码值 - return_code_value_str
- 进字节数 - in_byte
- 进数据包数 - in_packet
- 进网ICMP丢失包数 - inbound_icmp_lost_packets
- 进网ICMP丢失包率 - inbound_icmp_lost_packets_rate
- 进网RTT - inbound_rtt
- 进网TCP分段丢失包 - inbound_tcp_segment_lost_packet
- 进网TCP分段丢失率 - inbound_tcp_segment_lost_packet_rate
- 进网TCP同步包 - inbound_tcp_syn_packet
- 进网TCP同步确认包 - inbound_tcp_synack_packet
- 进网TCP同步确认重传包 - inbound_tcp_synack_retrans_packet
- 进网TCP同步重传包 - inbound_tcp_syn_retrans_packet
- 进网TCP数据包数（有效载荷） - inbound_tcp_effective_payload_packet
- 进网TCP确认包 - inbound_tcp_ack_packet
- 进网TCP结束包 - inbound_tcp_fin_packet
- 进网TCP负载数据包数 - inbound_tcp_payload_packet
- 进网TCP重传包 - inbound_tcp_retransmission_packet
- 进网TCP重传率 - inbound_tcp_retransmission_packet_rate
- 进网利用率 - inbound_utilizaiton
- 进网利用率90P基线 - inbound_utilizaiton_baseline_90p
- 进网利用率基线 - inbound_utilizaiton_baseline
- 进网利用率峰值 - peak_inbound_utilizaiton
- 进网带宽 - in_bandwidth
- 进网平均ACK时延 - inbound_avg_ack_delay
- 进网数据包 - inbound_packet
- 进网数据包90P基线 - inbound_packet_baseline_90p
- 进网数据包基线 - inbound_packet_baseline
- 进网数据包峰值 - peak_inbound_packet
- 进网数据包峰值基线 - peak_inbound_packet_baseline
- 进网数据包数（有效载荷） - inbound_payload_packet
- 进网数据包谷值 - valley_inbound_packet
- 进网数据包谷值基线 - valley_inbound_packet_baseline
- 进网最大ACK时延 - inbound_max_ack_delay
- 进网最小ACK时延 - inbound_min_ack_delay
- 进网每秒数据包数 - inbound_packetps
- 进网每秒数据包数90P基线 - inbound_packetps_baseline_90p
- 进网每秒数据包数基线 - inbound_packetps_baseline
- 进网每秒数据包数峰值 - peak_inbound_packetps
- 进网每秒数据包数峰值基线 - peak_inbound_packetps_baseline
- 进网每秒数据包数谷值 - valley_inbound_packetps
- 进网每秒数据包数谷值基线 - valley_inbound_packetps_baseline
- 进网比特率 - inbound_bitps
- 进网比特率90P基线 - inbound_bitps_baseline_90p
- 进网比特率95峰值 - peak_inbound_bitps_95p
- 进网比特率基线 - inbound_bitps_baseline
- 进网比特率峰值 - peak_inbound_bitps
- 进网比特率峰值基线 - peak_inbound_bitps_baseline
- 进网比特率谷值 - valley_inbound_bitps
- 进网比特率谷值基线 - valley_inbound_bitps_baseline
- 进网流量 - inbound_byte
- 进网流量90P基线 - inbound_byte_baseline_90p
- 进网流量95峰值 - peak_inbound_byte_95p
- 进网流量基线 - inbound_byte_baseline
- 进网流量峰值 - peak_inbound_byte
- 进网流量峰值基线 - peak_inbound_byte_baseline
- 进网流量谷值 - valley_inbound_byte
- 进网流量谷值基线 - valley_inbound_byte_baseline
- 连接失败次数 - tcp_shakehands_failed_count
- 连接失败率 - tcp_connect_failure_rate
- 连接建立客户端无响应次数 - client_connection_noresponse
- 连接建立客户端无响应率 - client_connection_noresponse_rate
- 连接建立客户端重置次数 - client_connection_rst
- 连接建立客户端重置率 - client_connection_rst_rate
- 连接建立总时间 - establish_total_rtt
- 连接建立成功次数 - connection_succ_count
- 连接建立无响应次数 - connection_noresponse
- 连接建立无响应率 - connection_noresponse_rate
- 连接建立时间 - establish_rtt
- 连接建立服务器无响应次数 - server_connection_noresponse
- 连接建立服务器无响应率 - server_connection_noresponse_rate
- 连接建立服务器重置次数 - server_connection_rst
- 连接建立服务器重置率 - server_connection_rst_rate
- 连接建立重置次数 - connection_rst
- 连接建立重置率 - connection_rst_rate
- 连接无响应率 - tcp_connect_noresponse_rate
- 连接类型 - connection_type
- 连接请求总数 - tcp_shakehands_total_count
- 通话中 - status_active
- 通话中(已关闭) - status_active_close
- 通话中(活动中) - status_active_curr
- 通话占比 - call_rate
- 通话次数 - total_calls
- 邀请时间 - invite_time
- 邮件敏感字警报数 - mail_alarm_count
- 配置员 - person
- 采集数据包 - total_packet_actual
- 采集数据包（含驱动和分析丢包） - total_packet_actual_and_drop
- 采集每秒数据包数（含驱动和分析丢包） - total_pps_actual_and_drop
- 采集比特率（含驱动和分析丢包） - total_bps_actual_and_drop
- 采集流量 - total_byte_actual
- 采集流量（含驱动和分析丢包） - total_byte_actual_and_drop
- 金额 - customAccField1
- 链路id - netlink_id
- 链路警报个数 - netlink_alarm_count
- 非IP单播数据包 - non_ip_unicast_packet
- 非IP单播流量 - non_ip_unicast_byte
- 非IP广播数据包 - non_ip_broadcast_packet
- 非IP广播数据包基线 - non_ip_broadcast_packet_baseline
- 非IP广播流量 - non_ip_broadcast_byte
- 非IP总流量 - non_ip_total_byte
- 非IP组播流量 - non_ip_multicast_byte
- 非IP组播流量 - non_ip_multicast_packet
- 音频MoS - audio_mos
- 音频MoS总值 - audio_mos_total
- 音频MoS计数 - audio_mos_count
- 音频MoS评估值 - audio_mos_assessment
- 音频编码 - audio_codec
- 首次响应时延 - tcp_first_res_rtt
- 驱动丢失字节数 - adapter_drop_byte
- 驱动丢失字节数 - adapter_drop_byte1
- 驱动丢失数据包 - adapter_drop_packet
- 驱动丢失数据包 - adapter_drop_packet1
- 驱动丢失比特率 - adapter_drop_bitps