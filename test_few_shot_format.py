#!/usr/bin/env python3
"""
测试few-shot示例格式
"""

import asyncio
import json
from experience_manager import ExperienceManager, IterationStep, ToolExecution
from datetime import datetime


async def test_few_shot_format():
    """测试few-shot示例格式"""
    print("=== 测试Few-shot示例格式 ===")
    
    # 初始化经验管理器
    manager = ExperienceManager()
    
    # 创建一个简单的经验
    tool_exec = ToolExecution(
        tool_name="stats-query-mcp_mcp_tool_query_statistics_table",
        parameters={"table": "summary", "time_range": "2025-07-17"},
        response='{"success": true, "data": "total_byte,total_packet\\n615721309429,900069199"}',
        execution_time=1.2,
        success=True,
        timestamp=datetime.now().isoformat()
    )
    
    iteration = IterationStep(
        iteration=1,
        thought="查询统计表获取数据",
        action="stats-query-mcp_mcp_tool_query_statistics_table",
        action_input={"table": "summary", "time_range": "2025-07-17"},
        observation="成功获取统计数据",
        tool_executions=[tool_exec],
        duration=2.0
    )
    
    # 保存经验
    await manager.save_experience(
        query="查询192.168.163.209的统计信息",
        final_answer='{"success": true, "data": "total_byte,total_packet\\n615721309429,900069199"}',
        execution_time=5.0,
        token_usage=100,
        tools_used=["stats-query-mcp_mcp_tool_query_statistics_table"],
        success=True,
        iterations=[iteration]
    )
    
    # 测试生成few-shot示例
    if manager.experiences:
        exp = manager.experiences[-1]
        few_shot_example = manager.generate_detailed_few_shot_example(exp, 0.95)
        
        print("生成的Few-shot示例:")
        print("=" * 60)
        print(few_shot_example)
        print("=" * 60)
        
        # 检查示例是否简洁且不包含可能干扰JSON的内容
        print("\n示例分析:")
        print(f"- 长度: {len(few_shot_example)} 字符")
        print(f"- 行数: {few_shot_example.count(chr(10)) + 1}")
        print(f"- 包含JSON: {'json' in few_shot_example.lower()}")
        print(f"- 包含Action: {'action' in few_shot_example.lower()}")
        print(f"- 包含代码块: {'```' in few_shot_example}")
        
        # 模拟系统提示词
        print("\n模拟系统提示词片段:")
        system_prompt_fragment = f"""
You have access to the following tools:
[tool1, tool2, tool3]

Use a json blob to specify a tool by providing an action key (tool name) and an action_input key (tool input).

## Reference Example
{few_shot_example}Remember to always respond with valid JSON format as specified above.

Begin! Reminder to ALWAYS respond with a valid json blob of a single action.
"""
        print(system_prompt_fragment)


if __name__ == "__main__":
    asyncio.run(test_few_shot_format())
