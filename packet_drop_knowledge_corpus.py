#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跳包诊断知识语料库
基于真实运维文档格式：问题现象、排查步骤、诊断结果
"""

# 跳包诊断知识文档 - 按真实运维文档格式编写
PACKET_DROP_KNOWLEDGE_CORPUS = [
    """
    ## 跳包
    问题现象：
    长时间出现重传率100%，下载数据包后分析是正常的

    排查步骤：
    1. 首先确定会话持续时间，区分短期会话（≤2秒）和长期会话（>2秒）
    2. 短期会话场景：使用第1秒的`在线解码`判断seq号跳变
    3. 长期会话场景：使用`查询统计表工具`，二分查找定位第一个出现100%重传前的时间点（秒）

    诊断结果：
    如果发现序列号跳变，确认为跳包；否则可能是统计算法问题
    """,
]

# 真实案例研究 - 基于实际运维场景
PACKET_DROP_CASE_STUDIES = [
    """
    ## 案例1：短期TCP会话序列号跳变
    问题现象：
    服务器 ip:***********, 服务器端口:80, 客户端ip:***********, 客户端端口:10800, 在2025-06-16 14:00:00触发TCP重传率100%告警
    会话持续时间仅2秒，第2秒触发告警

    排查步骤：
    1. 查询TCP会话基本信息（tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和会话结束时间）
    2. 发现会话在13:59:58-14:00:00期间，持续2秒
    3. 使用在线解码分析13:59:59-14:00:00的数据包
    4. 发现第1秒最后一个包seq=1000000，正常，第2秒开始出现seq=4294967295的远大于正常seq的异常大包，如果出现则为序列号跳变导致的跳包，否则不是这个问题导致的

    诊断结果：
    如果存在序列号跳变，则确认为序列号跳变导致的跳包。设备在处理序列号时出现溢出，导致后续包被认为是重传。
    """,
    
    """
    ## 案例2：长期会话连续重传
    问题现象：
    服务器 ip:***********, 服务器端口:80, 客户端ip:***********, 客户端端口:10800, 在2025-06-16 15:30:00触发告警
    会话已持续30分钟，从15:20开始连续10分钟重传率100%

    排查步骤：
    1. 查询TCP会话基本信息（tcp_flow表，timeunit=0，默认key，字段只查会话开始时间和会话结束时间）
    2. 发现TCP会话持续30分钟，持续时间为2025-06-16 15:00:00-2025-06-16 15:30:00
    3. 使用二分查找定位到异常时间点，比如这个是30分钟持续，则先指定时间桶为10分钟，查询会话开始时间到会话结束时间，检查tcp_retransmission_rate是否不为100%，找不到则退出判断，找到则继续下一步，在第最后一个不为100%时间范围继续按照刚才步骤排查，直到时间桶指定为1秒
    举例说明：查询出来的数据可能为：
    2025-06-16 15:00:00-2025-06-16 15:10:00 重传率10%
    2025-06-16 15:10:00-2025-06-16 15:20:00 重传率95%
    2025-06-16 15:20:00-2025-06-16 15:30:00 重传率100%
    则需要继续在2025-06-16 15:10:00-2025-06-16 15:20:00这个时间范围，指定时间桶为5分钟，继续排查（5分钟，1分钟，10秒，1秒），直到1s桶
    4. 找到15:19:45为重传率不为100%的时间点，则继续诊断
    5. 对15:19:45进行详细包解码
    6. 检查Seq是否存在跳变，如果存在则确认为序列号跳变导致的跳包，否则不是这个问题导致的

    诊断结果：
    如果存在序列号跳变，则确认为序列号跳变导致的跳包。设备在处理序列号时出现溢出，导致后续包被认为是重传。
    """,
]

def get_knowledge_corpus():
    """获取完整的知识语料库"""
    return PACKET_DROP_KNOWLEDGE_CORPUS + PACKET_DROP_CASE_STUDIES

def get_chunked_knowledge(chunk_size=500):
    """将知识语料库分块，适合HyperGraphRAG处理"""
    all_content = "\n\n".join(get_knowledge_corpus())
    
    # 简单的按字符数分块
    chunks = []
    start = 0
    while start < len(all_content):
        end = start + chunk_size
        if end < len(all_content):
            # 找到最近的句号或换行符
            while end > start and all_content[end] not in '.。\n':
                end -= 1
            if end == start:
                end = start + chunk_size
        else:
            end = len(all_content)
        
        chunk = all_content[start:end].strip()
        if chunk:
            chunks.append(chunk)
        start = end
    
    return chunks 