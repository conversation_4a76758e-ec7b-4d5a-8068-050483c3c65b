# Experience Management System

## 概述

这个经验管理系统为MCP ReAct策略提供了智能的查询经验学习和复用能力。系统能够：

1. **自动保存**成功的查询经验
2. **智能检索**相似的历史查询
3. **生成few-shot示例**指导后续查询
4. **提供执行指导**优化查询性能
5. **分析查询模式**持续改进系统

## 核心组件

### 1. ExperienceManager (experience_manager.py)

主要的经验管理类，负责：
- 保存和加载查询经验
- 计算查询相似度（支持embedding和文本相似度）
- 生成详细的few-shot示例
- 提供执行指导和优化建议

### 2. LLMStepSummarizer (llm_step_summarizer.py) **[新增]**

基于大模型的智能步骤总结器，能够：
- 分析查询执行过程中的每个迭代步骤
- 使用LLM智能生成步骤描述
- 根据上下文和参数推断操作意图
- 生成更准确和有意义的few-shot示例

### 3. mcpReActAgentStrategy (strategies/mcpReAct.py)

集成了经验管理的ReAct策略，能够：
- 在查询开始时检索相似经验
- 将经验转化为few-shot示例和执行指导
- 支持LLM生成的智能步骤总结
- 在查询结束时保存新的经验

### 4. 分析工具

- **experience_analyzer.py**: 分析现有经验数据
- **improve_few_shot.py**: 改进few-shot示例生成
- **test_llm_summarizer.py**: 测试LLM步骤总结功能

## 配置说明

### experience_config.json

```json
{
  "experience_manager": {
    "enabled": true,                    // 是否启用经验管理
    "similarity_threshold": 0.6,        // 相似度阈值
    "top_k_similar": 2,                // 返回的相似经验数量
    "min_execution_time": 1.0,         // 最小执行时间（秒）
    "few_shot_config": {
      "max_examples": 1,               // 最大few-shot示例数
      "include_execution_guidance": true,  // 包含执行指导
      "include_parameter_examples": true,  // 包含参数示例
      "include_performance_hints": true    // 包含性能提示
    },
    "use_llm_for_step_summary": false       // 是否启用LLM生成步骤总结
  },
  "embedding_config": {
    "chat_api_url": "https://open.bigmodel.cn/api/paas/v4/chat/completions"  // LLM聊天API端点
  }
}
```

## LLM智能步骤总结 **[新功能]**

### 工作原理

系统现在支持使用大模型智能分析查询执行过程，生成更准确的步骤描述：

1. **分析执行上下文**: 结合工具名称、参数、思考过程和执行结果
2. **智能推断意图**: 根据具体参数推断操作的实际目的
3. **生成精确描述**: 生成准确反映步骤作用的中文描述
4. **保持格式一致**: 维持原有的action JSON格式

### 启用方法

在 `experience_config.json` 中设置：
```json
{
  "experience_manager": {
    "use_llm_for_step_summary": true
  }
}
```

### LLM vs 传统方法对比

**传统方法**（硬编码映射）：
- `setup_api_connection` → "登录API"
- `get_config` → "查询名称链路一对应的id，从xml中找对应的id"

**LLM方法**（智能分析）：
- 分析参数 `{'url': 'https://192.168.163.209:8080/'}` → "登录到192.168.163.209的API服务器"
- 分析参数 `{'config_type': 'netlink', 'netlink_id': 64}` → "查询netlink配置获取链路一的ID信息"

## Few-shot示例格式

系统生成的few-shot示例遵循以下格式：

```
## Example
Example 1：查询192.168.163.209 上链路名称为链路一的2025-07-17一天的概要统计
Step:
1. 登录API
{'action': 'stats-query-mcp_mcp_tool_setup_api_connection', 'action_input': {'url': 'https://192.168.163.209:8080/'}}
2. 查询名称链路一对应的id，从xml中找对应的id
{'action': 'stats-query-mcp_mcp_tool_get_config', 'action_input': {'config_type': 'netlink', 'netlink_id': 64}}
3. 查询概要统计是对应什么表
{'action': 'stats-query-mcp_mcp_tool_list_statistics_tables', 'action_input': {}}
4. 查询概要统计表下有哪些字段
{'action': 'stats-query-mcp_mcp_tool_list_table_fields', 'action_input': {'table_name': 'summary'}}
5. 选取其中几个字段查询
{'action': 'stats-query-mcp_mcp_tool_query_statistics_table', 'action_input': {'table': 'summary', 'begintime': '2025-07-17 00:00:00', 'endtime': '2025-07-17 23:59:59', 'fields': 'total_byte,total_packet,total_bitps,total_packetps,total_utilization,new_flow_count', 'keys': 'time', 'timeunit': 0, 'netlink': 1}}

**Expected Result**: {"success": true, "error_code": 0, "message": "查询成功", "data": "total_byte,total_packet,total_bitps,total_packetps,total_utilization..."}

**Execution Tips**: This query took 21.3s with 5 iterations.
```

## 使用方法

### 1. 基本使用

经验管理系统会自动集成到MCP ReAct策略中：

```python
# 在mcpReActAgentStrategy中自动初始化
self.experience_manager = ExperienceManager()

# 查询时自动检索相似经验
similar_experiences = await self.experience_manager.find_similar_experiences(query)

# 查询结束时自动保存经验
await self.experience_manager.save_experience(
    query=query,
    final_answer=final_answer,
    execution_time=total_duration,
    token_usage=total_tokens,
    tools_used=tools_used,
    success=success,
    iterations=detailed_iterations
)
```

### 2. 分析现有经验

```bash
# 显示经验摘要
python experience_analyzer.py summary

# 显示模式分析
python experience_analyzer.py patterns

# 查找相似查询
python experience_analyzer.py similar "查询统计数据"

# 生成few-shot示例
python experience_analyzer.py example "查询链路统计"

# 导出分析报告
python experience_analyzer.py export
```

### 3. 使用LLM步骤总结器

```bash
# 测试LLM步骤总结功能
python test_llm_summarizer.py test

# 批量改进现有经验（使用LLM）
python llm_step_summarizer.py improve

# 对比传统方法和LLM方法
python test_llm_summarizer.py compare

# 测试单个经验的LLM总结
python llm_step_summarizer.py test
```

### 4. 改进few-shot示例

```bash
# 分析成功模式
python improve_few_shot.py analyze

# 生成改进模板
python improve_few_shot.py template

# 导出改进模板
python improve_few_shot.py export
```

## 数据结构

### QueryExperience

```python
@dataclass
class QueryExperience:
    query: str                          # 用户查询
    query_hash: str                     # 查询哈希
    final_answer: str                   # 最终答案
    execution_time: float               # 执行时间
    token_usage: int                    # Token使用量
    tools_used: List[str]              # 使用的工具
    success: bool                       # 是否成功
    timestamp: str                      # 时间戳
    iterations: List[IterationStep]     # 详细迭代步骤
    total_iterations: int               # 总迭代次数
    embedding: Optional[List[float]]    # 查询向量
    metadata: Optional[Dict[str, Any]]  # 元数据
```

### IterationStep

```python
@dataclass
class IterationStep:
    iteration: int                      # 迭代次数
    thought: str                        # 思考过程
    action: Optional[str]               # 执行的动作
    action_input: Optional[Dict[str, Any]]  # 动作输入
    observation: str                    # 观察结果
    tool_executions: List[ToolExecution]    # 工具执行详情
    duration: float                     # 持续时间
```

## 性能优化

1. **相似度阈值调整**: 根据实际效果调整`similarity_threshold`
2. **经验数量限制**: 通过`max_experiences`控制内存使用
3. **Embedding缓存**: 启用embedding以提高相似度计算精度
4. **自动清理**: 定期清理低质量经验数据

## 故障排除

### 常见问题

1. **经验未保存**: 检查执行时间和token使用量是否达到最小阈值
2. **相似度过低**: 调整`similarity_threshold`或改进查询描述
3. **Few-shot示例过长**: 减少`max_examples`或简化示例格式
4. **性能问题**: 启用embedding或优化文本相似度计算

### 调试信息

系统会输出详细的调试信息：

```
[EXPERIENCE] 经验管理器初始化成功
[EXPERIENCE] Found 3 similar experiences
[EXPERIENCE] Successfully saved query experience
[EXPERIENCE] Added execution guidance to system prompt
```

## 扩展功能

1. **自定义相似度算法**: 实现自定义的相似度计算方法
2. **经验质量评估**: 添加经验质量评分机制
3. **多模态支持**: 支持图像、表格等多模态查询经验
4. **分布式存储**: 支持分布式经验存储和共享

## 贡献指南

1. 遵循现有代码风格
2. 添加适当的测试用例
3. 更新相关文档
4. 提交前运行完整测试

## 许可证

本项目采用MIT许可证。
