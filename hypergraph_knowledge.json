{"entities": {"tcp_session": {"id": "tcp_session", "name": "TCP会话", "type": "场景对象", "attributes": null}, "retransmit_rate_100": {"id": "retransmit_rate_100", "name": "重传率100%", "type": "异常症状", "attributes": null}, "short_session": {"id": "short_session", "name": "短期会话", "type": "时间特征", "attributes": {"duration": "<=2s"}}, "long_session": {"id": "long_session", "name": "长期会话", "type": "时间特征", "attributes": {"duration": ">2s"}}, "online_decode": {"id": "online_decode", "name": "在线解码", "type": "诊断工具", "attributes": null}, "stats_query": {"id": "stats_query", "name": "统计表查询", "type": "诊断工具", "attributes": null}, "time_bucket_analysis": {"id": "time_bucket_analysis", "name": "时间桶分析", "type": "分析方法", "attributes": null}, "seq_jump": {"id": "seq_jump", "name": "Seq号跳变", "type": "关键指标", "attributes": null}, "continuous_100_rate": {"id": "continuous_100_rate", "name": "连续100%重传", "type": "异常模式", "attributes": null}, "large_seq_number": {"id": "large_seq_number", "name": "很大Seq号", "type": "异常特征", "attributes": null}, "server_ip": {"id": "server_ip", "name": "服务器IP", "type": "网络标识", "attributes": null}, "link_id": {"id": "link_id", "name": "链路ID", "type": "网络标识", "attributes": null}, "trigger_time": {"id": "trigger_time", "name": "触发时间", "type": "时间标识", "attributes": null}, "threshold_90": {"id": "threshold_90", "name": "阈值90%", "type": "配置参数", "attributes": null}, "packet_drop_confirmed": {"id": "packet_drop_confirmed", "name": "确认跳包", "type": "诊断结论", "attributes": null}, "binary_search_needed": {"id": "binary_search_needed", "name": "需要二分查找", "type": "诊断步骤", "attributes": null}, "first_non_100_second": {"id": "first_non_100_second", "name": "第1秒非100%", "type": "关键时间点", "attributes": null}}, "hyperedges": {"short_session_diagnosis": {"id": "short_session_diagnosis", "entities": ["tcp_session", "retransmit_rate_100", "short_session", "large_seq_number", "online_decode", "seq_jump"], "relation_type": "诊断路径", "description": "短期会话中出现重传率100%时，需检查是否有很大Seq号的包，通过在线解码分析Seq号跳变来判断跳包", "confidence": 0.9, "metadata": {"priority": "high", "complexity": "medium"}}, "long_session_diagnosis": {"id": "long_session_diagnosis", "entities": ["tcp_session", "retransmit_rate_100", "long_session", "stats_query", "time_bucket_analysis", "continuous_100_rate"], "relation_type": "诊断路径", "description": "长期会话中出现重传率100%时，通过统计表查询分析多个时间桶是否连续出现100%情况", "confidence": 0.9, "metadata": {"priority": "high", "complexity": "high"}}, "binary_search_optimization": {"id": "binary_search_optimization", "entities": ["continuous_100_rate", "binary_search_needed", "stats_query", "first_non_100_second", "online_decode"], "relation_type": "优化方法", "description": "连续出现100%重传时，使用二分查找找到第1秒不为100%的时间点，再进行在线解码确认", "confidence": 0.95, "metadata": {"optimization": true}}, "alert_context": {"id": "alert_context", "entities": ["server_ip", "link_id", "trigger_time", "threshold_90", "retransmit_rate_100"], "relation_type": "警报上下文", "description": "警报触发时的完整上下文信息，包含网络标识、时间和阈值配置", "confidence": 1.0, "metadata": {"context_type": "alert"}}, "mcp_tools_integration": {"id": "mcp_tools_integration", "entities": ["stats_query", "time_bucket_analysis", "server_ip", "link_id", "trigger_time"], "relation_type": "工具集成", "description": "通过MCP统计查询工具获取指定服务器、链路、时间范围的统计数据进行分析", "confidence": 0.95, "metadata": {"tool_type": "mcp"}}, "packet_drop_confirmation": {"id": "packet_drop_confirmation", "entities": ["seq_jump", "continuous_100_rate", "large_seq_number", "packet_drop_confirmed"], "relation_type": "确认条件", "description": "同时满足Seq号跳变、连续100%重传、出现很大Seq号等条件时确认为跳包", "confidence": 0.98, "metadata": null}}}