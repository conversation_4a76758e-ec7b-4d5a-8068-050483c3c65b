##########################################################
#作者:               ZhangHua                             #
#创建日期:            2017/06/11                           #
#更新作者:            ZhouXiaoQiang                        #
#更新日期:            2021/4/22                            #
#更新内容:            基于python2改为python3环境下可用        #
#                    解决回溯密码中存在特殊字符登录不成功的问题 #
########Modify : Lics#####################################

import json
import traceback
import urllib.request
import time
import struct
import types
import parse_api_data
import sys
import ssl
import toolkit
import os
import xml.etree.ElementTree as ET
import csv
from proc import process_csv_files

class api_request:
    def __init__(self, url, netlink,sorttype, begintime , endtime, timeunit, filter, savedir, ver):
        self.url = url
        self.session=''
        self.netlink=netlink
        self.sorttype=sorttype
        self.begintime = self.strtime_to_inttime(begintime) * 1000
        self.endtime =  self.strtime_to_inttime(endtime) * 1000
        self.timeunit = timeunit
        self.filter = filter
        if os.path.exists(savedir) == False:
            os.mkdir(savedir)
        self.savedir = savedir
        self.ver = ver
        
    def strtime_to_inttime(self, strtime):
        strtime=strtime
        tupletime=time.strptime(strtime,'%Y-%m-%d %H:%M:%S')
        itime=time.mktime(tupletime)
        return int(itime)
        
    def __del__(self):
        pass
        
    def login(self, user, pwd):    
        if self.ver>='5.4':
            page =  "csras_api/login"
        else:
            page =  "login.php"
            
        param = {"username":user,"password":pwd}
        resdata = self.requesdata(page, param)
        print('resdata', resdata)

        try:
            js_obj = json.loads(resdata)
            if js_obj['login_errcode'] == 0:
                self.session = js_obj['session']
                print(js_obj['login_errmsg'])
            else:
                print('登录失败：'+js_obj['login_errmsg'])
        except Exception as e:
            print(e)
        
    def loginout(self):    
        if self.ver>='5.4':
            page =  "csras_api/%s/logout/" % (self.session)
        else:
             page =  "logout.php?session=%s" % (self.session)
            
        param = ''
        resdata=self.requesdata(page, param)
        dc = json.loads(resdata)
        print (dc["logout_msg"])

    def gettime(self,timeType):
        request_url = "csras_api/%s/time/%s/%s" % (self.session, timeType, self.netlink)
        param = ""
        resdata = self.requesdata(request_url, param)
        dc = json.loads(resdata)
        print(dc)
        
    def savefile(self, filename, data):
        filepath = self.savedir + '/' + filename
        f = open(filepath, 'wb')
        f.write(data)
        f.close()

    def export_to_csv(self, apifile, csvfile):
        apifilepath = self.savedir + '/' + apifile
        csvfilepath = self.savedir + '/' + csvfile
        parseobj = parse_api_data.resovle_data(apifilepath,csvfilepath)
        parseobj.resolve()
        parseobj.save_data_to_file()
        del parseobj

    #获取请求状态码
    def  get_api_data_status(self, data) :
        #服务器返回的时json格式字符串
        if data[0:1] == '{'.encode():
            try:
                js_obj = json.loads(data)
                return js_obj['errcode']
            except Exception as e:
                print(e)
        else:
            strcode = data[0:2]
            rndata = struct.unpack("!H", strcode)
            return rndata[0]
        
    def query_statistic(self,  param, datafile, csvfile):    
        if self.ver >='5.4':
            page =  "csras_api/%s/stats_data" % (self.session)
        else:
            page ="statsquery.php?session=%s" %  (self.session)
            
        resdata=self.requesdata(page,  param)
        #print('resdata',resdata)

        #获取请求状态码
        return_code=self.get_api_data_status(resdata)
        toolkit.api4ShowStatuMsg(return_code)
        #print('strMsg', resdata[6:].decode())

        #请求数据失败，退出函数
        if return_code != 0:
            return

        #保存查询二进制数据到文件
        self.savefile(datafile, resdata)
        self.export_to_csv(datafile,csvfile)    
        return return_code
        
    def summary(self):
        param={}
        param["netlink"] = self.netlink
        param["table"] = "summary"
        param["keys"] = ["time"]
        param["fields"] = ["time","total_byte","total_byteps","total_bitps","inbound_byte","inbound_byteps",\
                                     "inbound_bitps","outbound_byte","outbound_byteps","outbound_bitps",\
                                     "internal_byte","external_byte","internal_bitps","external_bitps","ip_total_byte","ip_unicast_byte","ip_broadcast_byte",\
                                     "ip_multicast_byte","ip_abnormal_byte","non_ip_total_byte","non_ip_multicast_byte","non_ip_abnormal_byte","total_packet",\
                                     "total_packetps","inbound_packet","inbound_packetps","outbound_packet","outbound_packetps","internal_packet",\
                                     "external_packet","internal_packetps","external_packetps","ip_unicast_packet","ip_broadcast_packet","ip_multicast_packet",\
                                     "ip_abnormal_packet","non_ip_unicast_packet","non_ip_broadcast_packet","non_ip_multicast_packet","non_ip_abnormal_packet",\
                                    "peak_total_byte","peak_total_bitps","peak_total_byte_timestamp","peak_inbound_byte","peak_inbound_bitps",\
                                    "peak_outbound_byte","peak_outbound_bitps","peak_total_packet","peak_total_packetps","peak_inbound_packet",\
                                    "peak_inbound_packetps","peak_outbound_packet","peak_outbound_packetps","broadcast_packet","multicast_packet",\
                                    "total_utilization","inbound_utilizaiton","outbound_utilizaiton","adapter_drop_packet","analyse_drop_packet",\
                                   "real_total_packet","analyse_drop_byte","real_total_byte","real_total_bitps","analyse_drop_bitps",\
                                   "real_total_packetps","analyse_drop_packetps","adapter_drop_packetps","concurrent_flow_count"]
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        self.query_statistic(param, 'summary.dat', 'summary.csv')
        
    def phys_addr(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "phys_addr"
        param["keys"] = ["mac"]
        param["fields"] = ["mac","tx_byte","rx_byte","total_byte","total_byteps","tx_packet","rx_packet","total_packet","total_packetps", \
                                   "broadcast_packet","multicast_packet","tx_arp_req_packet","tx_arp_res_packet"]
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'phys_addr.dat', 'phys_addr.csv')
        print (rcode)
        
        
    def phys_flow(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "phys_flow"
        param["keys"] = ["protocol","mac_endpoint1","mac_endpoint2"]
        param["fields"] = ["protocol","mac_endpoint1","mac_endpoint2","endpoint1_tx_byte","endpoint2_tx_byte","total_byte","total_byteps","total_bitps","endpoint1_tx_bitps", \
                                    "endpoint2_tx_bitps","endpoint1_tx_packet","endpoint2_tx_packet","total_packet","total_packetps"] 
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'phys_flow.dat', 'phys_flow.csv')
        print (rcode)
    
    def internal_ip_addr(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "internal_ip_addr"
        param["keys"] = ["ip_addr"]
        param["fields"] = ["ip_addr","netsegment_id","tx_byte","rx_byte","total_byte","total_byteps","tx_byteps","rx_byteps","total_bitps","tx_bitps","rx_bitps","byte_tr_ratio", \
                                    "tx_packet","rx_packet","total_packet","total_packetps","packet_tr_ratio","avg_pkt_size","tx_avg_pkt_size","rx_avg_pkt_size","broadcast_packet",\
                                    "multicast_packet","tx_tcp_syn_packet","rx_tcp_syn_packet","tx_tcp_synack_packet","rx_tcp_synack_packet","tx_tcp_rst_packet","rx_tcp_rst_packet",\
                                    "create_flow_count","close_flow_count","alive_flow_count","connection_rst","connection_noresponse"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'internal_ip_addr.dat', 'internal_ip_addr.csv')
        print (rcode)
        
    def application(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "application"
        param["keys"] = ["application_id"]
        param["fields"] = ["application_id","application_name","uplink_byte","downlink_byte","total_byte","total_byteps","uplink_packet",\
                                     "downlink_packet","total_packet","total_packetps","avg_pkt_size","pktsize0_64","pktsize65_127","pktsize128_255",\
                                     "pktsize256_511","pktsize512_1023","pktsize1024_1518","pktsize1519","tcp_syn_packet","tcp_synack_packet",\
                                     "uplink_tcp_rst_packet","downlink_tcp_rst_packet","tcp_rst_packet","uplink_tcp_duplicate_ack_packet",\
                                     "downlink_tcp_duplicate_ack_packet","tcp_duplicate_ack_packet","uplink_tcp_retransmission_packet",\
                                     "downlink_tcp_retransmission_packet","tcp_retransmission_packet","uplink_tcp_segment_lost_packet",\
                                     "downlink_tcp_segment_lost_packet","tcp_segment_lost_packet","tcp_three_handshake","tcp_shakehands_total_count",\
                                     "tcp_connect_failure_rate","tcp_three_handshake_avg_duration","server_tcp_three_handshake_avg_rtt",\
                                     "tcp_three_handshake_max_rtt","tcp_three_handshake_min_rtt","tcp_three_handshake_avg_rtt","client_max_rtt",\
                                     "client_min_rtt","client_avg_rtt","server_max_rtt","server_min_rtt","server_avg_rtt","server_tcp_window_0",\
                                     "server_tcp_window_min_size","server_tcp_window_avg_size","client_tcp_window_0","client_tcp_window_min_size",\
                                     "client_tcp_window_avg_size","create_flow_count","close_flow_count","alive_flow_count","concurrent_flow_count",\
                                     "client_avg_ack_delay","client_max_ack_delay","client_min_ack_delay","server_avg_ack_delay","server_max_ack_delay",\
                                     "server_min_ack_delay","connection_rst","connection_noresponse","transaction_log_alarm_count","transaction_stat_alarm_count",\
                                     "transaction_alarm_count","application_monitor_alarm_count","application_client_alarm_count","application_server_alarm_count",\
                                     "application_ip_flow_alarm_count","application_segment_alarm_count","application_tcp_alarm_count","application_alarm_count",\
                                     "tcp_transaction_total_count","tcp_transaction_good_count","tcp_transaction_normal_count","tcp_transaction_bad_count",\
                                     "tcp_transaction_worse_count","tcp_transaction_request_count","tcp_transaction_response_count","tcp_transaction_max_rtt",\
                                     "tcp_transaction_min_rtt","tcp_transaction_avg_rtt","total_alarm_count","uplink_payload_byte","uplink_payload_packet",\
                                     "downlink_payload_byte","downlink_payload_packet","total_payload_byte","total_payload_packet","uplink_has_payload_packet",\
                                     "downlink_has_payload_packet","total_has_payload_packet","uplink_packet_lost_rate","downlink_packet_lost_rate",\
                                     "total_packet_lost_rate","uplink_packet_retrans_rate","downlink_packet_retrans_rate","total_packet_retrans_rate",\
                                     "uplink_packet_trans_rate","downlink_packet_trans_rate","total_packet_trans_rate","client_avg_idle_time","client_max_idle_time",\
                                     "client_min_idle_time","uplink_tcp_fin_packet","downlink_tcp_fin_packet" \
                                     ]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'application.dat', 'application.csv')
        print (rcode)
       
    def external_ip_addr(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "external_ip_addr"
        param["keys"] = ["ip_addr"]
        param["fields"] = ["ip_addr","netsegment_id","tx_byte","rx_byte","total_byte","total_byteps","tx_byteps","rx_byteps","total_bitps","tx_bitps","rx_bitps","byte_tr_ratio", \
                                    "tx_packet","rx_packet","total_packet","total_packetps","packet_tr_ratio","avg_pkt_size","tx_avg_pkt_size","rx_avg_pkt_size","broadcast_packet",\
                                    "multicast_packet","tx_tcp_syn_packet","rx_tcp_syn_packet","tx_tcp_synack_packet","rx_tcp_synack_packet","tx_tcp_rst_packet","rx_tcp_rst_packet",\
                                    "create_flow_count","close_flow_count","alive_flow_count","connection_rst","connection_noresponse"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'external_ip_addr.dat', 'external_ip_addr.csv')
        print (rcode)



    def ip_flow(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "ip_flow"
        #param["keys"] = ["vn_type","vlan_id","ip_endpoint1","ip_endpoint2","netsegment_id1","netsegment_id2"]
        param["keys"] = ["ip_endpoint1","ip_endpoint2"]
        param["fields"] = ["vn_type","vlan_id","ip_endpoint1","ip_endpoint2","protocol",\
                                     "endpoint1_tx_byte","endpoint2_tx_byte","total_byte","total_byteps","total_bitps","endpoint1_tx_packet",\
                                     "endpoint2_tx_packet","endpoint1_tx_payload_packet","endpoint2_tx_payload_packet","total_packet","total_packetps",\
                                     "avg_pkt_size","connection_rst","connection_noresponse","tcp_syn_packet","tcp_synack_packet","tcp_rst_packet",\
                                     "endpoint1_tx_tcp_retransmission_packet","endpoint2_tx_tcp_retransmission_packet","tcp_retransmission_packet",\
                                     "endpoint1_tx_tcp_segment_lost_packet","endpoint2_tx_tcp_segment_lost_packet","tcp_segment_lost_packet",\
                                     "endpoint1_tx_tcp_duplicate_ack_packet","endpoint2_tx_tcp_duplicate_ack_packet","tcp_duplicate_ack_packet",\
                                     "endpoint1_tx_tcp_all_payload_packet","endpoint2_tx_tcp_all_payload_packet","endpoint1_packet_retrans_rate",\
                                     "endpoint2_packet_retrans_rate","endpoint1_packet_lost_rate","endpoint2_packet_lost_rate","tcp_three_handshake",\
                                     "tcp_three_handshake_avg_duration","endpoint1_tcp_three_handshake_avg_rtt","endpoint2_tcp_three_handshake_avg_rtt",\
                                     "endpoint1_avg_ack_delay","endpoint2_avg_ack_delay"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'ip_flow.dat', 'ip_flow.csv')
        print (rcode)
                 
    def tcp_flow(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "tcp_flow"
        param["keys"] = ["client_ip_addr","server_ip_addr","client_port","server_port"]
        param["fields"] = ["client_ip_addr","server_ip_addr","client_port","server_port","client_netsegment_id",\
                                    "server_netsegment_id","application_id","protocol","client_total_byte","server_total_byte",\
                                    "total_byte","total_byteps","total_bitps","client_bitps","server_bitps","client_total_packet",\
                                    "server_total_packet","total_packet","total_packetps","client_cumulative_byte","server_cumulative_byte",\
                                    "total_cumulative_byte","client_cumulative_packet","server_cumulative_packet","total_cumulative_packet",\
                                    "client_payload_packet","server_payload_packet","client_cumulative_payload_packet","server_cumulative_payload_packet",\
                                    "avg_pkt_size","client_rtt","server_rtt","establish_rtt","flow_start_time","flow_end_time","flow_duration","tcp_status",\
                                    "tcp_syn_packet","tcp_synack_packet","client_tcp_rst_packet","server_tcp_rst_packet","client_tcp_fin_packet","server_tcp_fin_packet",\
                                    "client_tcp_retransmission_packet","server_tcp_retransmission_packet","client_tcp_segment_lost_packet",\
                                    "server_tcp_segment_lost_packet","client_min_ack_delay","client_max_ack_delay","client_avg_ack_delay","server_min_ack_delay",\
                                    " server_max_ack_delay","server_avg_ack_delay","connection_rst","connection_noresponse","client_tcp_window_0","server_tcp_window_0"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'tcp_flow.dat', 'tcp_flow.csv')
        print (rcode)
 
    def netsegment(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "netsegment"
        param["keys"] = ["netsegment_id"]
        param["fields"] = ["netsegment_id","tx_byte","rx_byte","tx_packet","rx_packet","tx_tcp_syn_packet","rx_tcp_syn_packet",\
                                    "tx_tcp_synack_packet","rx_tcp_synack_packet","tx_tcp_rst_packet","rx_tcp_rst_packet","internal_byte",\
                                    "internal_packet","internal_tcp_syn_packet","internal_tcp_synack_packet","internal_tcp_rst_packet",\
                                    "total_byte","total_byteps","tx_byteps","rx_byteps","byte_tr_ratio","total_utilization","inbound_utilizaiton",\
                                    "outbound_utilizaiton","total_bitps","tx_bitps","rx_bitps","total_packet","total_packetps","packet_tr_ratio",\
                                    "avg_pkt_size","tx_avg_pkt_size","rx_avg_pkt_size","tcp_syn_packet","tcp_synack_packet","tcp_rst_packet"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'netsegment.dat', 'netsegment.csv')
        print (rcode)
        
        
    def netsegment_flow(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "netsegment_flow"
        param["keys"] = ["netsegment_id1","netsegment_id2","application_id","protocol"]
        param["fields"] = ["netsegment_id1","netsegment_id2","application_id","protocol","endpoint1_tx_byte","endpoint2_tx_byte",\
                                    "total_byte","total_byteps","total_bitps","endpoint1_tx_packet","endpoint2_tx_packet","endpoint1_tx_payload_packet",
                                    "endpoint2_tx_payload_packet","total_packet","total_packetps","avg_pkt_size","connection_rst","connection_noresponse",\
                                    "tcp_syn_packet","tcp_synack_packet","tcp_rst_packet","endpoint1_tx_tcp_retransmission_packet",\
                                    "endpoint2_tx_tcp_retransmission_packet","tcp_retransmission_packet","endpoint1_tx_tcp_segment_lost_packet",\
                                    "endpoint2_tx_tcp_segment_lost_packet","tcp_segment_lost_packet","endpoint1_tx_tcp_duplicate_ack_packet",\
                                    "endpoint2_tx_tcp_duplicate_ack_packet","tcp_duplicate_ack_packet","endpoint1_tx_tcp_all_payload_packet",\
                                    "endpoint2_tx_tcp_all_payload_packet","endpoint1_packet_retrans_rate","endpoint2_packet_retrans_rate",\
                                    "endpoint1_packet_lost_rate","tcp_three_handshake_avg_duration","endpoint1_tcp_three_handshake_avg_rtt",\
                                    "endpoint2_tcp_three_handshake_avg_rtt","endpoint1_avg_ack_delay","endpoint2_avg_ack_delay"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'netsegment_flow.dat', 'netsegment_flow.csv')
        print (rcode)


    def tcp_server_port(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "tcp_server_port"
        param["keys"] = ["server_ip_addr","server_port","application_id","protocol"]
        param["fields"] = ["server_ip_addr","server_port","application_id","protocol","server_netsegment_id",\
                                    "client_total_byte","client_total_packet","server_total_byte","server_total_packet",\
                                    "total_byte","total_byteps","total_bitps","client_bitps","server_bitps","total_packet",\
                                    "total_packetps","server_start_time","server_last_time","visit_count","tcp_syn_packet",\
                                    "client_tcp_rst_packet","server_tcp_rst_packet","tcp_rst_packet"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'tcp_server_port.dat', 'tcp_server_port.csv')
        print (rcode)
                
                
                
    def udp_server_port(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "udp_server_port"
        param["keys"] = ["server_ip_addr","server_port","application_id","protocol"]
        param["fields"] = ["server_ip_addr","server_port","application_id","protocol","server_netsegment_id","client_total_byte",\
                                    "client_total_packet","server_total_byte","server_total_packet","total_byte","total_byteps","total_bitps",\
                                    "client_bitps","server_bitps","total_packet","total_packetps","server_start_time","server_last_time","visit_count"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'udp_server_port.dat', 'udp_server_port.csv')
        print (rcode)
 

    def alarm_log(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "alarm_log"
        param["keys"] = ["unique_id"]
        param["fields"] = ["server_ip_addr","server_port","application_id","protocol","server_netsegment_id",\
                                    "client_total_byte","client_total_packet","server_total_byte","server_total_packet",\
                                    "total_byte","total_byteps","total_bitps","client_bitps","server_bitps","total_packet",\
                                    "total_packetps","server_start_time","server_last_time","visit_count"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'alarm_log.dat', 'alarm_log.csv')
        print (rcode)
        
        
    def mpls_vpn_stat(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "mpls_vpn_stat"
        param["keys"] = ["mpls_vpn_label"]
        param["fields"] = ["mpls_vpn_label","total_byte","total_packet","tcp_syn_packet","tcp_synack_packet","tcp_rst_packet","total_byteps","total_bitps","total_packetps"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'mpls_vpn_stat.dat', 'mpls_vpn_stat.csv')
        print (rcode)
    

    def vxlan_stat(self):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "vxlan_stat"
        param["keys"] = ["vxlan_id"]
        param["fields"] = ["vxlan_id","total_byte","total_packet","tcp_syn_packet","tcp_synack_packet","tcp_rst_packet","total_byteps","total_bitps","total_packetps"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'vxlan_stat.dat', 'vxlan_stat.csv')
        print (rcode)
        
        
    def app_monitor_server(self, appid):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "app_monitor_server"
        param["keys"] = ["application_id","server_ip_addr"]
        param["fields"] = ["server_netsegment_id","uplink_byte","downlink_byte","uplink_packet","downlink_packet","total_byte","total_packet",\
                                     "total_byteps","uplink_byteps","downlink_byteps","total_bitps","uplink_bitps","downlink_bitps",\
                                     "total_packetps","uplink_packetps","downlink_packetps","time","create_flow_count","alive_flow_count",\
                                     "close_flow_count","concurrent_flow_count","tcp_synack_packet","uplink_tcp_rst_packet","downlink_tcp_rst_packet",\
                                     "tcp_rst_packet","uplink_tcp_retransmission_packet","downlink_tcp_retransmission_packet",\
                                     "tcp_retransmission_packet","uplink_tcp_segment_lost_packet","downlink_tcp_segment_lost_packet","tcp_segment_lost_packet",\
                                     "uplink_tcp_duplicate_ack_packet","downlink_tcp_duplicate_ack_packet","tcp_duplicate_ack_packet","uplink_tcp_fin_packet","downlink_tcp_fin_packet",\
                                     "tcp_fin_packet","client_min_ack_delay","client_max_ack_delay","client_avg_ack_delay","server_min_ack_delay","server_max_ack_delay",
                                    "server_avg_ack_delay","client_max_idle_time","client_min_idle_time","client_avg_idle_time","connection_rst","connection_noresponse","tcp_shakehands_total_count",\
                                     "tcp_connect_failure_rate","tcp_connect_noresponse_rate","uplink_tcp_effective_payload_packet","downlink_tcp_effective_payload_packet",\
                                    "total_tcp_effective_payload_packet","tcp_effective_payload_packet_tr_ratio","uplink_tcp_effective_payload_byte","downlink_tcp_effective_payload_byte",\
                                    "total_tcp_effective_payload_byte","tcp_effective_payload_byte_tr_ratio","uplink_tcp_all_payload_packet","downlink_tcp_all_payload_packet",
                                   "total_tcp_all_payload_packet","uplink_packet_lost_rate","downlink_packet_lost_rate","total_packet_lost_rate","uplink_packet_retrans_rate",\
                                   "downlink_packet_retrans_rate","total_packet_retrans_rate","uplink_packet_trans_rate","downlink_packet_trans_rate","total_packet_trans_rate",\
                                  "client_tcp_window_0","client_tcp_window_avg_size","client_tcp_window_min_size","server_tcp_window_0","server_tcp_window_avg_size","server_tcp_window_min_size",\
                                  "tcp_three_handshake","tcp_three_handshake_max_rtt","tcp_three_handshake_min_rtt","tcp_three_handshake_avg_rtt","client_max_rtt","client_min_rtt",\
                                  "client_avg_rtt","server_max_rtt","server_min_rtt","server_avg_rtt","establish_max_rtt","establish_min_rtt","establish_avg_rtt","tcp_transaction_total_count",\
                                 "tcp_transaction_good_count","tcp_transaction_normal_count","tcp_transaction_bad_count","tcp_transaction_worse_count","tcp_transaction_request_count",\
                                 "tcp_transaction_response_count","tcp_transaction_no_response_count","tcp_transaction_max_rtt","tcp_transaction_min_rtt","tcp_transaction_total_rtt",\
                                "tcp_transaction_avg_rtt","tcp_transaction_good_rate","tcp_transaction_worse_rate","tcp_transaction_no_response_rate","tcp_transaction_response_rate",\
                                "transaction_apdex","client_tcp_total_retrans_time","client_tcp_retrans_count","client_tcp_avg_retrans_time","client_tcp_max_retrans_time",\
                                "server_tcp_total_retrans_time","server_tcp_retrans_count","server_tcp_avg_retrans_time","server_tcp_max_retrans_time",\
                                "tcp_transaction_total_request_trans_time","tcp_transaction_total_response_trans_time","tcp_transaction_avg_request_trans_time",\
                                "tcp_transaction_avg_response_trans_time","tcp_transaction_max_response_trans_time","tcp_transaction_max_request_trans_time",\
                                "tcp_first_res_total_rtt","tcp_first_res_avg_rtt","tcp_first_res_max_rtt","tcp_trade_total_res_time","tcp_trade_avg_res_time","tcp_trade_max_res_time"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_monitor_server.dat', 'app_monitor_server.csv')
        print (rcode)
        
        
    def app_monitor_client(self, appid):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "app_monitor_client"
        param["keys"] = ["application_id","client_ip_addr"]
        param["fields"] = ["application_id","client_ip_addr","client_netsegment_id","uplink_byte","downlink_byte","uplink_packet",\
                                    "downlink_packet","total_byte","total_packet","total_byteps","uplink_byteps","downlink_byteps","total_bitps",\
                                    "uplink_bitps","downlink_bitps","total_packetps","uplink_packetps","downlink_packetps","time","create_flow_count",\
                                    "alive_flow_count","close_flow_count","concurrent_flow_count","tcp_syn_packet","tcp_synack_packet","uplink_tcp_rst_packet",\
                                    "downlink_tcp_rst_packet","tcp_rst_packet","uplink_tcp_retransmission_packet","downlink_tcp_retransmission_packet",\
                                    "tcp_retransmission_packet","uplink_tcp_segment_lost_packet","downlink_tcp_segment_lost_packet",\
                                    "tcp_segment_lost_packet","uplink_tcp_duplicate_ack_packet","downlink_tcp_duplicate_ack_packet",\
                                    "tcp_duplicate_ack_packet","uplink_tcp_fin_packet","downlink_tcp_fin_packet","tcp_fin_packet","client_min_ack_delay",\
                                    "client_max_ack_delay","client_avg_ack_delay","server_min_ack_delay","server_max_ack_delay","server_avg_ack_delay",\
                                    "client_max_idle_time","client_min_idle_time","client_avg_idle_time","connection_rst","connection_noresponse",\
                                    "tcp_shakehands_total_count","tcp_connect_failure_rate","tcp_connect_noresponse_rate","uplink_tcp_effective_payload_packet",\
                                    "downlink_tcp_effective_payload_packet","total_tcp_effective_payload_packet","tcp_effective_payload_packet_tr_ratio",\
                                    "uplink_tcp_effective_payload_byte","downlink_tcp_effective_payload_byte","total_tcp_effective_payload_byte",\
                                    "tcp_effective_payload_byte_tr_ratio","uplink_tcp_all_payload_packet","downlink_tcp_all_payload_packet",\
                                    "total_tcp_all_payload_packet","uplink_packet_lost_rate","downlink_packet_lost_rate","total_packet_lost_rate",\
                                    "uplink_packet_retrans_rate","downlink_packet_retrans_rate","total_packet_retrans_rate","uplink_packet_trans_rate",\
                                    "downlink_packet_trans_rate","total_packet_trans_rate","client_tcp_window_0","client_tcp_window_avg_size",\
                                    "client_tcp_window_min_size","server_tcp_window_0","server_tcp_window_avg_size","server_tcp_window_min_size",\
                                    "tcp_three_handshake","tcp_three_handshake_max_rtt","tcp_three_handshake_min_rtt","tcp_three_handshake_avg_rtt",\
                                    "client_max_rtt","client_min_rtt","client_avg_rtt","server_max_rtt","server_min_rtt","server_avg_rtt","establish_max_rtt",\
                                    "establish_min_rtt","establish_avg_rtt","tcp_transaction_total_count","tcp_transaction_good_count","tcp_transaction_normal_count",\
                                    "tcp_transaction_bad_count","tcp_transaction_worse_count","tcp_transaction_request_count","tcp_transaction_response_count",\
                                    "tcp_transaction_no_response_count","tcp_transaction_max_rtt","tcp_transaction_min_rtt","tcp_transaction_total_rtt",\
                                    "tcp_transaction_avg_rtt","tcp_transaction_good_rate","tcp_transaction_worse_rate","tcp_transaction_no_response_rate",\
                                    "tcp_transaction_response_rate","transaction_apdex","client_tcp_total_retrans_time","client_tcp_retrans_count","client_tcp_avg_retrans_time",\
                                    "client_tcp_max_retrans_time","server_tcp_total_retrans_time","server_tcp_retrans_count","server_tcp_avg_retrans_time",\
                                    "server_tcp_max_retrans_time","tcp_transaction_total_request_trans_time","tcp_transaction_total_response_trans_time",\
                                    "tcp_transaction_avg_request_trans_time","tcp_transaction_avg_response_trans_time","tcp_transaction_max_response_trans_time",\
                                    "tcp_transaction_max_request_trans_time","tcp_first_res_total_rtt","tcp_first_res_avg_rtt","tcp_first_res_max_rtt",\
                                    "tcp_trade_total_res_time","tcp_trade_avg_res_time","tcp_trade_max_res_time"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_monitor_client.dat', 'app_monitor_client.csv')
        print (rcode)
                
                
                
    def app_monitor_net_seg(self, appid):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "app_monitor_net_seg"
        param["keys"] = ["application_id","netsegment_id"]
        param["fields"] = ["netsegment_id","internal_byte","internal_packet","internal_tcp_syn_packet","internal_tcp_synack_packet","internal_tcp_rst_packet",\
                                    "uplink_byte","downlink_byte","uplink_packet","downlink_packet","total_byte","total_packet","total_byteps","total_bitps",\
                                    "uplink_bitps","downlink_bitps","total_packetps","time","create_flow_count","alive_flow_count","close_flow_count","concurrent_flow_count",\
                                    "tcp_syn_packet","tcp_synack_packet","uplink_tcp_rst_packet","downlink_tcp_rst_packet","tcp_rst_packet","uplink_tcp_retransmission_packet",\
                                    "downlink_tcp_retransmission_packet","tcp_retransmission_packet","uplink_tcp_segment_lost_packet","downlink_tcp_segment_lost_packet",\
                                     "tcp_segment_lost_packet","uplink_tcp_duplicate_ack_packet","downlink_tcp_duplicate_ack_packet","tcp_duplicate_ack_packet",\
                                    "uplink_tcp_fin_packet","downlink_tcp_fin_packet","tcp_fin_packet","client_min_ack_delay","client_max_ack_delay","client_avg_ack_delay",\
                                    "server_min_ack_delay","server_max_ack_delay","server_avg_ack_delay","client_max_idle_time","client_min_idle_time","client_avg_idle_time",\
                                    "connection_rst","connection_noresponse","tcp_shakehands_total_count","tcp_connect_failure_rate","tcp_connect_noresponse_rate",\
                                    "uplink_tcp_effective_payload_packet","downlink_tcp_effective_payload_packet","total_tcp_effective_payload_packet",\
                                    "tcp_effective_payload_packet_tr_ratio","uplink_tcp_effective_payload_byte","downlink_tcp_effective_payload_byte",\
                                    "total_tcp_effective_payload_byte","tcp_effective_payload_byte_tr_ratio","uplink_tcp_all_payload_packet","downlink_tcp_all_payload_packet",\
                                    "total_tcp_all_payload_packet","uplink_packet_lost_rate","downlink_packet_lost_rate","total_packet_lost_rate","uplink_packet_retrans_rate",\
                                    "downlink_packet_retrans_rate","total_packet_retrans_rate","uplink_packet_trans_rate","downlink_packet_trans_rate","total_packet_trans_rate",\
                                    "client_tcp_window_0","client_tcp_window_avg_size","client_tcp_window_min_size","server_tcp_window_0","server_tcp_window_avg_size",\
                                    "server_tcp_window_min_size","tcp_three_handshake","tcp_three_handshake_max_rtt","tcp_three_handshake_min_rtt","tcp_three_handshake_avg_rtt",\
                                    "client_max_rtt","client_min_rtt","client_avg_rtt","server_max_rtt","server_min_rtt","server_avg_rtt","establish_max_rtt",\
                                    "establish_min_rtt","establish_avg_rtt","tcp_transaction_total_count","tcp_transaction_good_count","tcp_transaction_normal_count",\
                                   "tcp_transaction_bad_count","tcp_transaction_worse_count","tcp_transaction_request_count","tcp_transaction_response_count",\
                                  "tcp_transaction_no_response_count","tcp_transaction_max_rtt","tcp_transaction_min_rtt","tcp_transaction_total_rtt","tcp_transaction_avg_rtt",\
                                  "tcp_transaction_good_rate","tcp_transaction_worse_rate","tcp_transaction_no_response_rate","tcp_transaction_response_rate","transaction_apdex",\
                                  "client_tcp_total_retrans_time","client_tcp_retrans_count","client_tcp_avg_retrans_time","client_tcp_max_retrans_time","server_tcp_total_retrans_time",\
                                    "server_tcp_retrans_count","server_tcp_avg_retrans_time","server_tcp_max_retrans_time","tcp_transaction_total_request_trans_time",\
                                    "tcp_transaction_total_response_trans_time","tcp_transaction_avg_request_trans_time","tcp_transaction_avg_response_trans_time",\
                                    "tcp_transaction_max_response_trans_time","tcp_transaction_max_request_trans_time","tcp_first_res_total_rtt","tcp_first_res_avg_rtt",\
                                    "tcp_first_res_max_rtt","tcp_trade_total_res_time","tcp_trade_avg_res_time",\
                                   "tcp_trade_max_res_time","netsegment_type","total_bandwidth","in_bandwidth","out_bandwidth"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_monitor_net_seg.dat', 'app_monitor_net_seg.csv')
        print (rcode)
                                

        
        
    def app_monitor_ip_flow(self,appid ):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "app_monitor_ip_flow"
        param["keys"] = ["application_id","client_ip_addr","server_ip_addr"]
        param["fields"] = ["application_id","client_ip_addr","server_ip_addr","client_netsegment_id","server_netsegment_id",\
                                    "internal_packet","uplink_byte","downlink_byte","uplink_packet","downlink_packet","total_byte",\
                                    "total_packet","total_byteps","uplink_byteps","downlink_byteps","total_bitps","uplink_bitps",\
                                    "downlink_bitps","total_packetps","uplink_packetps","downlink_packetps","time","create_flow_count",\
                                    "alive_flow_count","close_flow_count","concurrent_flow_count","tcp_syn_packet","tcp_synack_packet",\
                                    "uplink_tcp_rst_packet","downlink_tcp_rst_packet","tcp_rst_packet","uplink_tcp_retransmission_packet",\
                                    "downlink_tcp_retransmission_packet","tcp_retransmission_packet","uplink_tcp_segment_lost_packet",\
                                    "downlink_tcp_segment_lost_packet","tcp_segment_lost_packet","uplink_tcp_duplicate_ack_packet",\
                                    "downlink_tcp_duplicate_ack_packet","tcp_duplicate_ack_packet","uplink_tcp_fin_packet","downlink_tcp_fin_packet",\
                                    "tcp_fin_packet","client_min_ack_delay","client_max_ack_delay","client_avg_ack_delay","server_min_ack_delay",\
                                    "server_max_ack_delay","server_avg_ack_delay","client_max_idle_time","client_min_idle_time","client_avg_idle_time",\
                                    "connection_rst","connection_noresponse","tcp_shakehands_total_count","tcp_connect_failure_rate","tcp_connect_noresponse_rate",\
                                    "uplink_tcp_effective_payload_packet","downlink_tcp_effective_payload_packet","total_tcp_effective_payload_packet",\
                                    "tcp_effective_payload_packet_tr_ratio","uplink_tcp_effective_payload_byte","downlink_tcp_effective_payload_byte",\
                                    "total_tcp_effective_payload_byte","tcp_effective_payload_byte_tr_ratio","uplink_tcp_all_payload_packet",\
                                    "downlink_tcp_all_payload_packet","total_tcp_all_payload_packet","uplink_packet_lost_rate","downlink_packet_lost_rate",\
                                    "total_packet_lost_rate","uplink_packet_retrans_rate","downlink_packet_retrans_rate","total_packet_retrans_rate",\
                                    "uplink_packet_trans_rate","downlink_packet_trans_rate","total_packet_trans_rate","client_tcp_window_0",\
                                    "client_tcp_window_avg_size","client_tcp_window_min_size","server_tcp_window_0","server_tcp_window_avg_size",\
                                    "server_tcp_window_min_size","tcp_three_handshake","tcp_three_handshake_max_rtt","tcp_three_handshake_min_rtt",\
                                    "tcp_three_handshake_avg_rtt","client_max_rtt","client_min_rtt","client_avg_rtt","server_max_rtt","server_min_rtt",\
                                    "server_avg_rtt","establish_max_rtt","establish_min_rtt","establish_avg_rtt","tcp_transaction_total_count",\
                                    "tcp_transaction_good_count","tcp_transaction_normal_count","tcp_transaction_bad_count","tcp_transaction_worse_count",\
                                    "tcp_transaction_request_count","tcp_transaction_response_count","tcp_transaction_no_response_count",\
                                    "tcp_transaction_max_rtt","tcp_transaction_min_rtt","tcp_transaction_total_rtt","tcp_transaction_avg_rtt",\
                                    "tcp_transaction_good_rate","tcp_transaction_worse_rate","tcp_transaction_no_response_rate","tcp_transaction_response_rate",\
                                    "transaction_apdex","client_tcp_total_retrans_time","client_tcp_retrans_count","client_tcp_avg_retrans_time",\
                                    "client_tcp_max_retrans_time","server_tcp_total_retrans_time","server_tcp_retrans_count","server_tcp_avg_retrans_time",\
                                    "server_tcp_max_retrans_time","tcp_transaction_total_request_trans_time","tcp_transaction_total_response_trans_time",\
                                    "tcp_transaction_avg_request_trans_time","tcp_transaction_avg_response_trans_time","tcp_transaction_max_response_trans_time",\
                                    "tcp_transaction_max_request_trans_time","tcp_first_res_total_rtt","tcp_first_res_avg_rtt","tcp_first_res_max_rtt",\
                                    "tcp_trade_total_res_time","tcp_trade_avg_res_time","tcp_trade_max_res_time"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_monitor_ip_flow.dat', 'app_monitor_ip_flow.csv')
        print (rcode)
                                        
                                        
                                        
                                        
    def app_monitor_tcp_flow(self, appid):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "app_monitor_tcp_flow"
        param["keys"] = ["client_ip_addr","server_ip_addr","client_port","server_port"]
        param["fields"] = ["client_ip_addr","server_ip_addr","client_port","server_port","client_netsegment_id",\
                                    "server_netsegment_id","name","application_id","protocol","client_total_byte","server_total_byte","total_byte",\
                                    "total_byteps","total_bitps","client_bitps","server_bitps","client_total_packet","server_total_packet",\
                                    "total_packet","total_packetps","client_cumulative_byte","server_cumulative_byte","total_cumulative_byte",\
                                    "client_cumulative_packet","server_cumulative_packet","total_cumulative_packet","avg_pkt_size","client_rtt","server_rtt",\
                                    "flow_start_time","flow_end_time","flow_duration","tcp_status","tcp_syn_packet","tcp_synack_packet","client_tcp_rst_packet",\
                                    "server_tcp_rst_packet","client_tcp_fin_packet","server_tcp_fin_packet","client_tcp_retransmission_packet",\
                                    "server_tcp_retransmission_packet","client_tcp_segment_lost_packet","server_tcp_segment_lost_packet",\
                                    "client_min_ack_delay","client_max_ack_delay","client_avg_ack_delay","server_min_ack_delay",\
                                    "server_max_ack_delay","server_avg_ack_delay","connection_rst","connection_noresponse","client_tcp_window_0",\
                                    "server_tcp_window_0","client_tcp_effective_payload_packet","tcp_transaction_max_request_trans_time",\
                                    "tcp_transaction_request_count","tcp_transaction_response_count","tcp_transaction_total_request_trans_time",\
                                    "tcp_transaction_avg_request_trans_time","server_tcp_effective_payload_packet","tcp_transaction_max_response_trans_time",\
                                    "tcp_transaction_total_response_trans_time","tcp_transaction_avg_response_trans_time","tcp_transaction_total_rtt",\
                                    "tcp_handshake_third_ack_packet","tcp_shakehands_total_count","tcp_connect_noresponse_rate","first_payload_pkt_ipid",\
                                    "first_payload_pkt_seq","clienttoserver_vn_id","servertoclient_vn_id","vnType","client_dscp","server_dscp",\
                                    "server_tcp_effective_payload_byte","client_tcp_effective_payload_byte","tcp_transaction_worse_count",\
                                    "tcp_transaction_total_count","tcp_transaction_avg_rtt","tcp_transaction_max_rtt","establish_rtt",\
                                    "server_cumulative_payload_packet","client_cumulative_payload_packet","server_payload_packet","client_payload_packet",\
                                    "establish_max_rtt","establish_total_rtt","tcp_three_handshake","establish_avg_rtt","client_tcp_total_retrans_time",\
                                    "client_tcp_retrans_count","client_tcp_avg_retrans_time","client_tcp_max_retrans_time","server_tcp_total_retrans_time",\
                                    "server_tcp_retrans_count","server_tcp_avg_retrans_time","server_tcp_max_retrans_time","tcp_first_res_total_rtt","tcp_first_res_avg_rtt"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_monitor_tcp_flow.dat', 'app_monitor_tcp_flow.csv')
        print (rcode)
        
        
    def app_trans_stat(self, appid):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "app_trans_stat"
        param["keys"] = ["application_id","transaction_id"]
        param["fields"] = ["application_id","transaction_id","ip_ver","drilldownKey_client_ip_addr","drilldownKey_server_ip_addr",\
                                    "drilldown_netsegment_id1","drilldown_netsegment_id2","stat_field_name1","stat_field_value1","stat_field_name2",\
                                    "stat_field_value2","stat_field_name3","stat_field_value3","return_code_name","return_code_value","time","request_byte",\
                                    "response_byte","request_packet","response_packet","request_byteps","request_bitps","request_packetps","response_byteps",\
                                    "response_bitps","response_packetps","total_count_baseline","total_count","request_count_baseline","request_count",\
                                    "response_count_baseline","response_count","trans_avg_count_per_second","success_count_baseline","success_count",\
                                    "failed_count","success_rate_baseline","success_rate","response_max_rtt_baseline","response_max_rtt","response_min_rtt_baseline",\
                                    "response_min_rtt","response_avg_rtt_baseline","response_avg_rtt_baseline_90p","response_avg_rtt","trans_handle_max_rtt",\
                                    "trans_handle_min_rtt","trans_handle_avg_rtt","trans_request_avg_rtt","trans_response_avg_rtt","request_trans_max_rtt",\
                                    "request_trans_min_rtt","response_trans_max_rtt","response_trans_min_rtt","transaction_good_count","transaction_normal_count",\
                                    "transaction_bad_count","transaction_worse_count","response_good_count","response_normal_count","response_bad_count","response_worse_count",\
                                    "transaction_no_response_count","transaction_good_rate","transaction_normal_rate","transaction_bad_rate","transaction_worse_rate",\
                                    "response_good_rate","response_normal_rate","response_bad_rate","response_worse_rate","transaction_no_response_rate",\
                                    "transaction_response_rate_baseline","transaction_response_rate","transaction_apdex","total_byte","total_packet","total_byteps","total_bitps","total_packetps"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_trans_stat.dat', 'app_trans_stat.csv')
        print (rcode)



    def app_trans_ipflow(self, appid):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "app_trans_ipflow"
        param["keys"] = ["application_id","transaction_id","client_ip_addr","server_ip_addr"]
        param["fields"] = ["application_id","transaction_id","client_ip_addr","server_ip_addr","client_netsegment_id","server_netsegment_id",\
                                    "stat_field_name","stat_field_value","stat_field_name2","stat_field_value2","stat_field_name3","stat_field_value3",\
                                    "return_code_name","return_code_value","time","request_byte","response_byte","request_packet","response_packet",\
                                    "request_byteps","request_bitps","request_packetps","response_byteps","response_bitps","response_packetps",\
                                    "total_count_baseline","total_count","request_count_baseline","request_count","response_count_baseline","response_count",\
                                    "trans_avg_count_per_second","success_count_baseline","success_count","failed_count","success_rate_baseline","success_rate",\
                                    "response_max_rtt_baseline","response_max_rtt","response_min_rtt_baseline","response_min_rtt","response_avg_rtt_baseline",\
                                    "response_avg_rtt_baseline_90p","response_avg_rtt","trans_handle_max_rtt","trans_handle_min_rtt","trans_handle_avg_rtt",\
                                    "trans_request_avg_rtt","trans_response_avg_rtt","request_trans_max_rtt","request_trans_min_rtt","response_trans_max_rtt",\
                                    "response_trans_min_rtt","transaction_good_count","transaction_normal_count","transaction_bad_count","transaction_worse_count",\
                                    "response_good_count","response_normal_count","response_bad_count","response_worse_count","transaction_no_response_count",\
                                    "transaction_good_rate","transaction_normal_rate","transaction_bad_rate","transaction_worse_rate","response_good_rate",\
                                    "response_normal_rate","response_bad_rate","response_worse_rate","transaction_no_response_rate","transaction_response_rate_baseline",\
                                    "transaction_response_rate","transaction_apdex","total_byte","total_packet","total_byteps","total_bitps","total_packetps"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_trans_ipflow.dat', 'app_trans_ipflow.csv')
        print (rcode)


    def app_trans_client(self, appid):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "app_trans_client"
        param["keys"] = ["application_id","transaction_id","client_ip_addr"]
        param["fields"] = ["application_id","transaction_id","client_ip_addr","client_netsegment_id","ip_ver",
                                    "server_ip_addr","stat_field_name1","stat_field_value1","stat_field_name2",\
                                    "stat_field_value2","stat_field_name3","stat_field_value3","return_code_name",\
                                    "return_code_value","time","request_byte","response_byte","request_packet","response_packet",\
                                    "request_byteps","request_bitps","request_packetps","response_byteps","response_bitps","response_packetps",\
                                    "total_count_baseline","total_count","request_count_baseline","request_count","response_count_baseline",\
                                    "response_count","trans_avg_count_per_second","success_count_baseline","success_count","failed_count",\
                                    "success_rate_baseline","success_rate","response_max_rtt_baseline","response_max_rtt","response_min_rtt_baseline",\
                                    "response_min_rtt","response_avg_rtt_baseline","response_avg_rtt_baseline_90p","response_avg_rtt","trans_handle_max_rtt",\
                                    "trans_handle_min_rtt","trans_handle_avg_rtt","trans_request_avg_rtt","trans_response_avg_rtt","request_trans_max_rtt",\
                                    "request_trans_min_rtt","response_trans_max_rtt","response_trans_min_rtt","transaction_good_count","transaction_normal_count",\
                                    "transaction_bad_count","transaction_worse_count","response_good_count","response_normal_count","response_bad_count",\
                                    "response_worse_count","transaction_no_response_count","transaction_good_rate","transaction_normal_rate","transaction_bad_rate",\
                                    "transaction_worse_rate","response_good_rate","response_normal_rate","response_bad_rate","response_worse_rate",\
                                    "transaction_no_response_rate","transaction_response_rate_baseline","transaction_response_rate","transaction_apdex",\
                                    "total_byte","total_packet","total_byteps","total_bitps","total_packetps"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_trans_client.dat', 'app_trans_client.csv')
        print (rcode)

    def app_trans_server(self, appid):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "app_trans_client"
        param["keys"] = ["application_id","transaction_id","server_ip_addr"]
        param["fields"] = ["application_id","transaction_id","server_ip_addr","server_netsegment_id","ip_ver",\
                                     "client_ip_addr","stat_field_name1","stat_field_value1","stat_field_name2","stat_field_value2",\
                                     "stat_field_name3","stat_field_value3","return_code_name","return_code_value","time","request_byte",\
                                     "response_byte","request_packet","response_packet","request_byteps","request_bitps","request_packetps",\
                                     "response_byteps","response_bitps","response_packetps","total_count_baseline","total_count",\
                                     "request_count_baseline","request_count","response_count_baseline","response_count","trans_avg_count_per_second",\
                                     "success_count_baseline","success_count","failed_count","success_rate_baseline","success_rate","response_max_rtt_baseline",\
                                     "response_max_rtt","response_min_rtt_baseline","response_min_rtt","response_avg_rtt_baseline","response_avg_rtt_baseline_90p",\
                                     "response_avg_rtt","trans_handle_max_rtt","trans_handle_min_rtt","trans_handle_avg_rtt","trans_request_avg_rtt",\
                                     "trans_response_avg_rtt","request_trans_max_rtt","request_trans_min_rtt","response_trans_max_rtt","response_trans_min_rtt",\
                                     "transaction_good_count","transaction_normal_count","transaction_bad_count","transaction_worse_count","response_good_count",\
                                     "response_normal_count","response_bad_count","response_worse_count","transaction_no_response_count","transaction_good_rate",\
                                     "transaction_normal_rate","transaction_bad_rate","transaction_worse_rate","response_good_rate","response_normal_rate",\
                                     "response_bad_rate","response_worse_rate","transaction_no_response_rate","transaction_response_rate_baseline",\
                                     "transaction_response_rate","transaction_apdex","total_byte","total_packet","total_byteps","total_bitps","total_packetps"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_trans_server.dat', 'app_trans_server.csv')
        print (rcode)
        
        
    def app_trans_net_segment(self, appid):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "app_trans_net_segment"
        param["keys"] = ["application_id","transaction_id","netsegment_id"]
        param["fields"] = ["application_id","transaction_id","server_ip_addr","server_netsegment_id","ip_ver","client_ip_addr",\
                                    "stat_field_name1","stat_field_value1","stat_field_name2","stat_field_value2","stat_field_name3",\
                                    "stat_field_value3","return_code_name","return_code_value","time","request_byte","response_byte",\
                                    "request_packet","response_packet","request_byteps","request_bitps","request_packetps","response_byteps",\
                                    "response_bitps","response_packetps","total_count_baseline","total_count","request_count_baseline",\
                                    "request_count","response_count_baseline","response_count","trans_avg_count_per_second","success_count_baseline",\
                                    "success_count","failed_count","success_rate_baseline","success_rate","response_max_rtt_baseline","response_max_rtt",\
                                    "response_min_rtt_baseline","response_min_rtt","response_avg_rtt_baseline","response_avg_rtt_baseline_90p",\
                                    "response_avg_rtt","trans_handle_max_rtt","trans_handle_min_rtt","trans_handle_avg_rtt","trans_request_avg_rtt",\
                                    "trans_response_avg_rtt","request_trans_max_rtt","request_trans_min_rtt","response_trans_max_rtt","response_trans_min_rtt",\
                                    "transaction_good_count","transaction_normal_count","transaction_bad_count","transaction_worse_count","response_good_count",\
                                    "response_normal_count","response_bad_count","response_worse_count","transaction_no_response_count","transaction_good_rate",\
                                    "transaction_normal_rate","transaction_bad_rate","transaction_worse_rate","response_good_rate","response_normal_rate",\
                                    "response_bad_rate","response_worse_rate","transaction_no_response_rate","transaction_response_rate_baseline",\
                                    "transaction_response_rate","transaction_apdex","total_byte","total_packet","total_byteps","total_bitps","total_packetps"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_trans_net_segment.dat', 'app_trans_net_segment.csv')
        print (rcode)

    def app_trans_field_stat(self, netlinkId, appid, begintime, endtime, topcount=1000):      
     
        param={}
        param["netlink"] = netlinkId
        param["table"] = "app_trans_field_stat"
        param["keys"] = ["stat_field_name","stat_field_value","stat_field_name2", "stat_field_value2", "stat_field_name3", "stat_field_value3","transaction_id", "application_id"]
        
        # param["fields"] = ["stat_field_name","stat_field_value","application_id","stat_field_name2","stat_field_value2","stat_field_name3","stat_field_value3","return_code_name","return_code_value","transaction_id","response_count","response_byte","trans_response_avg_rtt"]
        param["fields"] = ["transaction_id_str","response_count","response_byte", "trans_response_avg_rtt","stat_field_name_str","stat_field_value_str","stat_field_name2_str","stat_field_value2_str","stat_field_name3_str", "stat_field_value3_str"]
        # param["fields"] = ["transaction_id_str","stat_field_name1_str","stat_field_name2_str","stat_field_name3_str", "stat_field_name","stat_field_value","stat_field_name2","stat_field_value2",\
        #                             "stat_field_name3","stat_field_value3","return_code_name","return_code_value",\
        #                             "application_id","transaction_id","time","request_byte","response_byte","request_packet",\
        #                             "response_packet","request_byteps","request_bitps","request_packetps","response_byteps",\
        #                             "response_bitps","response_packetps","total_count_baseline","total_count","request_count_baseline",\
        #                             "request_count","response_count_baseline","response_count","trans_avg_count_per_second",\
        #                             "success_count_baseline","success_count","failed_count","success_rate_baseline","success_rate",\
        #                             "response_max_rtt_baseline","response_max_rtt","response_min_rtt_baseline","response_min_rtt",\
        #                             "response_avg_rtt_baseline","response_avg_rtt_baseline_90p","response_avg_rtt","trans_handle_max_rtt",\
        #                             "trans_handle_min_rtt","trans_handle_avg_rtt","trans_request_avg_rtt","trans_response_avg_rtt",\
        #                             "request_trans_max_rtt","request_trans_min_rtt","response_trans_max_rtt","response_trans_min_rtt",\
        #                             "transaction_good_count","transaction_normal_count","transaction_bad_count","transaction_worse_count",\
        #                             "response_good_count","response_normal_count","response_bad_count","response_worse_count",\
        #                             "transaction_no_response_count","transaction_good_rate","transaction_normal_rate","transaction_bad_rate",\
        #                             "transaction_worse_rate","response_good_rate","response_normal_rate","response_bad_rate","response_worse_rate",\
        #                             "transaction_no_response_rate","transaction_response_rate_baseline","transaction_response_rate","transaction_apdex",\
        #                             "ip_ver","drilldownKey_client_ip_addr","drilldownKey_server_ip_addr","drilldown_netsegment_id1",\
        #                             "drilldown_netsegment_id2","total_byte","total_packet","total_byteps","total_bitps","total_packetps"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=topcount
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=begintime
        param["endtime"]=endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_trans_field_stat.dat', f'{self.netlink}-{appid}.csv')
        print (rcode)
        
    def new_id_string_mapping_log(self, netlinkId, begintime, endtime, topcount=1000):      
     
        param={}
        param["netlink"] = netlinkId
        param["table"] = "id_string_mapping_log"
        param["keys"] = ["mapping_id"]
        param["fields"] = ["mapping_id", "mapping_string"]
        param["sorttype"]=self.sorttype
        param["sortfield"]="total_byte"
        param["filter"]=""
        param["topcount"]=topcount
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=begintime
        param["endtime"]=endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'id_string_mapping_log.dat', f'{netlinkId}-id_string_mapping_log.csv')
        print (rcode)
        return f'{netlinkId}-id_string_mapping_log.csv'
        
    def new_app_trans_stat(self, netlinkId, appid, begintime, endtime, topcount=1000):      
     
        param={}
        param["netlink"] = netlinkId
        param["table"] = "app_trans_stat"
        param["keys"] = ["application_id","transaction_id"]
        param["fields"] = ["transaction_id","response_count","response_byte", "trans_response_avg_rtt"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="response_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=topcount
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=begintime
        param["endtime"]=endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_trans_stat.dat', f'{self.netlink}-{appid}.csv')
        print (rcode)
        return f'{self.netlink}-{appid}.csv'
    
    def new_app_trans_server(self, netlinkId, appid, begintime, endtime, topcount=1000):      
     
        param={}
        param["netlink"] = netlinkId
        param["table"] = "app_trans_server"
        param["keys"] = ["application_id","transaction_id","server_ip_addr"]
        param["fields"] = ["transaction_id","response_count","response_byte", "trans_response_avg_rtt", "request_byte", "server_ip_addr", "total_bitps"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="response_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=topcount
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=begintime
        param["endtime"]=endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_trans_server.dat', f'{netlinkId}-{appid}-app_trans_server.csv')
        
        return f'{netlinkId}-{appid}-app_trans_server.csv'

 

    def app_trans_log(self, appid):      
     
        param={}
        param["netlink"] = self.netlink
        param["table"] = "app_trans_log"
        param["keys"] = ["unique_id","trans_start_time"]
        param["fields"] = ["transaction_id","response_byte","res_flow_sender_ip_addr","total_bitps"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="response_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=1000
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=self.begintime
        param["endtime"]=self.endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_trans_log.dat', 'app_trans_log.csv')
        print (rcode)
    def new_app_trans_log(self, netlinkId, appid, begintime, endtime, topcount=1000):      
     
        param={}
        param["netlink"] = netlinkId
        param["table"] = "app_trans_log"
        param["keys"] = ["unique_id","trans_start_time"]
        param["fields"] = ["transaction_id","response_byte","res_flow_sender_ip_addr","total_bitps"]
                                   
        param["sorttype"]=self.sorttype
        param["sortfield"]="response_byte"
        param["filter"]="application_id=%s" % (appid)
        param["topcount"]=topcount
        param["keycount"]=None
        param["fieldcount"]=None
        param["begintime"]=begintime
        param["endtime"]=endtime
        param["timeunit"]=self.timeunit
        
        rcode=self.query_statistic(param, 'app_trans_log.dat', f'{netlinkId}-{appid}-app_trans_log.csv')
        print (rcode)
        
        return f'{netlinkId}-{appid}-app_trans_log.csv'
        
    def  get_service_info(self,  param, datafile, csvfile):    
        if self.ver =='5.4':
            page =  "csras_api/%s/service_infos" % (self.session)
        else:
            page ="statsquery.php?session=%s" %  (self.session)
            
        request_url=self.url  + page       
        
        req=urllib2.Request(request_url,'')
        response=urllib2.urlopen(req)
        txt = response.read()
        return_code=self.get_api_data_status(txt)    
        print (return_code)

    def requesdata(self, page, param):
        request_url=self.url  + page       
        param = json.dumps(param, sort_keys=False)
        # 对参数进行编码，防止密码或者用户名中含有特殊字符导致登录失败
        param = 'param=' + urllib.parse.quote(param)
        req=urllib.request.Request(request_url,param.encode('utf-8'))
        response=urllib.request.urlopen(req)
        txt = response.read()
        return txt
                
if __name__=='__main__': 
    #the example of get_api_param.txt that refer to get_api_data.py
    url = "https://172.15.104.60:8080/"
    netlink=29
    sorttype=2                            #sort desc when sorttype is 2; see api doc for other value
    begintime = '2024-03-20 16:35:00'
    endtime =  '2024-05-20 16:36:00'
    timeunit = 0                          #merge all records as keys param of Json when timeunit is 0; see api doc for other value
    filter=''                             #API filter
    ver = '6.1'
    # 获取当前时间
    timeStr = time.strftime("%Y_%m_%d_%H_%M_%S", time.localtime())
    
    savedir=f'table_stat-{timeStr}'        #get_api_data.py --> .dat file;   parse_api_data.py parse .dat file to .csv file
    appid='50003'                         #for app monitor stat records
    user = 'admin'
    pwd = 'D&^4Vs!('
    ssl._create_default_https_context = ssl._create_unverified_context

    # Open and parse the XML file
    with open('export.xml', 'r') as file:  # Replace 'input_file.xml' with your actual file name
        xml_content = file.read()
        root = ET.fromstring(xml_content)

        # Extract the attributes from the 'Export' element
        url = root.get('url')
        user = root.get('usr')
        pwd = root.get('password')
        ssl._create_default_https_context = ssl._create_unverified_context

        # Create the api_request object with the extracted URL, user, and password
        ar = api_request(url, netlink, sorttype, begintime, endtime, timeunit, filter, savedir, ver)
        ar.login(user, pwd)
        ar.gettime("lastAnalysisTime")
        file_paths = []
        
        # Iterate over each 'Netlink' element
        for netlink_element in root.findall('Netlink'):
            id_attr = netlink_element.get('id')
            appId_attr = netlink_element.get('appId')
            beginTime_attr = ar.strtime_to_inttime(netlink_element.get('beginTime')) * 1000
            endTime_attr = ar.strtime_to_inttime(netlink_element.get('endTime')) * 1000
            topCount_attr = netlink_element.get('topCount')
            table = netlink_element.get('table')

            # Validate the input and convert to the appropriate data type as needed
            try:
                # Assuming id and topCount are numeric and times are in the correct format
                netlink_id = int(id_attr)
                app_ids = [int(app_id) for app_id in appId_attr.split(',')]
                begin_time = beginTime_attr  # Validate or convert to datetime if necessary
                end_time = endTime_attr      # Validate or convert to datetime if necessary
                top_count = int(topCount_attr) if topCount_attr else 1000
                
                if begin_time > end_time:
                    raise ValueError('Begin time cannot be later than end time.')
                # Now, you can call the method with the extracted and validated information
                # This is a placeholder for the actual method call; you'll need to replace it with the correct call.
                # For example, if you have a method like ar.fetch_data(id, app_ids, begin_time, end_time, top_count):
                # ar.fetch_data(netlink_id, app_ids, begin_time, end_time, top_count)
                id_string_file = ar.new_id_string_mapping_log(netlink_id, begin_time, end_time, 500000)
                                
                mapping_dict = {}
                with open(savedir + '/' + id_string_file, mode='r', errors='ignore') as mapping_file:
                    csv_reader = csv.reader(mapping_file)
                    for row in csv_reader:
                        mapping_id = row[0]
                        mapping_string = row[1].replace('\x00', '')  # Remove the null character
                        mapping_dict[mapping_id] = mapping_string
                for app_id in app_ids:
                    sheet3_file_name = ""
                    if table == 'app_trans_stat':
                        file_name = ar.new_app_trans_stat(netlink_id, app_id, begin_time, end_time, top_count)
                    else:
                        file_name = ar.new_app_trans_server(netlink_id, app_id, begin_time, end_time, top_count)
                        sheet3_file_name = ar.new_app_trans_log(netlink_id, app_id, begin_time, end_time, top_count)
                        # 读取文件并按照transaction_id进行数据合并
                        # transaction_data = {}
                        # with open(savedir + '/' + file_name, mode='r') as file:
                        #     csv_reader = csv.DictReader(file)
                            # for row in csv_reader:
                            #     transaction_id = row['transaction_id']
                            #     if transaction_id in transaction_data:
                            #         # 累加response_byte和response_count
                            #         transaction_data[transaction_id]['response_byte'] += int(row['response_byte'])
                            #         transaction_data[transaction_id]['response_count'] += int(row['response_count'])
                            #         # 追加server_ip_addr和对应的response_byte
                            #         if 'server_ip_addr' in transaction_data[transaction_id]:
                            #             transaction_data[transaction_id]['server_ip_addr'] += ',' + row['server_ip_addr']
                            #             transaction_data[transaction_id]['server_ip_response_byte'] += ',' + row['response_byte']
                            #         else:
                            #             transaction_data[transaction_id]['server_ip_addr'] = row['server_ip_addr']
                            #             transaction_data[transaction_id]['server_ip_response_byte'] = row['response_byte']
                            #         # 计算trans_response_avg_rtt的平均值
                            #         transaction_data[transaction_id]['trans_response_avg_rtt'] = (
                            #             transaction_data[transaction_id]['trans_response_avg_rtt'] * transaction_data[transaction_id]['count'] + float(row['trans_response_avg_rtt'])
                            #         ) / (transaction_data[transaction_id]['count'] + 1)
                            #         transaction_data[transaction_id]['count'] += 1
                            #     else:
                            #         transaction_data[transaction_id] = {
                            #             'response_byte': int(row['response_byte']),
                            #             'response_count': int(row['response_count']),
                            #             'server_ip_addr': row['server_ip_addr'],
                            #             'server_ip_response_byte': row['response_byte'],
                            #             'trans_response_avg_rtt': float(row['trans_response_avg_rtt']),
                            #             'count': 1  # 用于计算平均值
                            #         }

                        # 将合并后的数据写回到CSV文件
                        # fieldnames = ['transaction_id', 'response_byte', 'response_count', 'trans_response_avg_rtt', 'server_ip_addr', 'server_ip_response_byte']
                        # with open(savedir + '/' + file_name, mode='w', newline='') as file:
                        #     csv_writer = csv.DictWriter(file, fieldnames=fieldnames)
                        #     csv_writer.writeheader()
                        #     for transaction_id, data in transaction_data.items():
                        #         csv_writer.writerow({
                        #             'transaction_id': transaction_id,
                        #             'response_byte': data['response_byte'],
                        #             'response_count': data['response_count'],
                        #             'trans_response_avg_rtt': data['trans_response_avg_rtt'],
                        #             'server_ip_addr': data['server_ip_addr'],  # 将server_ip_addr写到最后一个列
                        #             'server_ip_response_byte': data['server_ip_response_byte']
                        #         })
                    # 替换id-string
                    
                    modified_transactions = []
                    with open(savedir + '/' + file_name, mode='r') as transaction_file:
                        csv_reader = csv.reader(transaction_file)
                        for row in csv_reader:
                            transaction_id = row[0]
                            # Replace the transaction_id with the corresponding mapping_string
                            if transaction_id in mapping_dict:
                                row[0] = mapping_dict[transaction_id]
                            modified_transactions.append(row)
                    
                    headers = modified_transactions[0]
                    headers.extend(['response_byte_mb', 'response_byte_band', 'page_size'])
                    modified_transactions[0] = headers
                    
                    # Replace the transaction_id and calculate new column values
                    for row in modified_transactions[1:]:  # Skip the header row
                        transaction_id = row[0]
                        if transaction_id in mapping_dict:
                            row[0] = mapping_dict[transaction_id]
                        
                        # Perform calculations for the new columns
                        response_byte = int(row[2])
                        response_count = int(row[1])
                        row.append(response_byte / 1000000)  # reponse_byte_mb
                        row.append(response_byte / 1000000 / ((end_time - begin_time)/1000) * 8)  # response_byte_band
                        if response_count != 0:
                            row.append(response_byte / response_count / 1000)  # page_size
                        else:
                            row.append(0)
                            

                    # Step 3: Write the modified transaction data back to the CSV file
                    # This will overwrite the existing transactions.csv file
                    with open(savedir + '/' + file_name, mode='w', newline='') as outfile:
                        csv_writer = csv.writer(outfile)
                        csv_writer.writerows(modified_transactions)
                    
                    file_paths.append(savedir + '/' + file_name)       
                
                    # proc sheet3
                    if sheet3_file_name and sheet3_file_name != "":
                        modified_transactions = []
                        with open(savedir + '/' + sheet3_file_name, mode='r') as transaction_file:
                            csv_reader = csv.reader(transaction_file)
                            for row in csv_reader:
                                transaction_id = row[0]
                                # Replace the transaction_id with the corresponding mapping_string
                                if transaction_id in mapping_dict:
                                    row[0] = mapping_dict[transaction_id]
                                modified_transactions.append(row)
                        
                        headers = modified_transactions[0]
                        headers.extend(['response_byte_mb'])
                        modified_transactions[0] = headers
                        
                        # Replace the transaction_id and calculate new column values
                        for row in modified_transactions[1:]:  # Skip the header row
                            transaction_id = row[0]
                            if transaction_id in mapping_dict:
                                row[0] = mapping_dict[transaction_id]
                            
                            # Perform calculations for the new columns
                            response_byte = int(row[1])
                            row.append(response_byte / 1000000)  # reponse_byte_mb

                        # Step 3: Write the modified transaction data back to the CSV file
                        # This will overwrite the existing transactions.csv file
                        with open(savedir + '/' + sheet3_file_name, mode='w', newline='') as outfile:
                            csv_writer = csv.writer(outfile)
                            csv_writer.writerows(modified_transactions)
                        
                        file_paths.append(savedir + '/' + sheet3_file_name)  
                        
                try:
                    process_csv_files(file_paths, savedir + '/output.xlsx')
                except Exception as e:
                    # 完整输出异常信息
                    print(f"处理csv出问题，请联系售后: {e}\n{traceback.format_exc()}")
                                        
                    
                
            except Exception as e:
                print(f"Invalid input found: {e}\n{traceback.format_exc()}")
                # Handle the error appropriately, e.g., skip this element, log the error, etc.
    
        ar.loginout()  # Assuming there's a logout method to call at the end
    