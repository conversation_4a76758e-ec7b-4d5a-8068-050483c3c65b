#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stats_mcp_server_official import StatsApiClient, setup_api_connection

def enumerate_all_table_fields():
    """枚举所有表的字段并输出为中文-英文格式文件"""
    
    # 设置API连接
    api_url = "https://192.168.163.209:8080/"
    print(f"正在连接到API: {api_url}")
    
    setup_api_connection("http://192.168.163.209:8080")
    
    # 创建API客户端 (使用测试环境的默认账号)
    api_client = StatsApiClient(api_url, "admin", "D&^4Vs!(", "6.1")
    
    # 用于存储所有字段的集合，自动去重
    all_fields = set()
    
    try:
        # 登录
        login_result = api_client.login()
        if not login_result:
            print(f"登录失败 {login_result}")
            return
        
        print("登录成功")
        
        # 获取所有统计表
        print("正在获取所有统计表...")
        url = f"csras_api/{api_client.session}/tables"
        resdata = api_client.request_data(url, '')
        tables_data = json.loads(resdata)
        
        if tables_data.get('errcode', 0) != 0:
            print(f"获取表列表失败: {tables_data.get('errmsg', '未知错误')}")
            return
        
        tables = tables_data.get('tables', [])
        print(f"找到 {len(tables)} 个统计表")
        
        # 遍历每个表
        for i, table in enumerate(tables):
            table_name = table['name']
            table_desc = table['desc']
            
            print(f"正在处理表 {i+1}/{len(tables)}: {table_name} ({table_desc})")
            
            try:
                # 获取表字段
                fields_url = f"csras_api/{api_client.session}/tables/{table_name}"
                fields_resdata = api_client.request_data(fields_url, '')
                fields_data = json.loads(fields_resdata)
                
                if fields_data.get('errcode', 0) != 0:
                    print(f"  获取表 {table_name} 字段失败: {fields_data.get('errmsg', '未知错误')}")
                    continue
                
                fields = fields_data.get('fields', [])
                print(f"  找到 {len(fields)} 个字段")
                
                # 收集所有字段
                for field in fields:
                    field_name = field['name']
                    field_caption = field.get('caption', field_name)
                    
                    # 格式：中文描述 - 英文字段名
                    field_info = f"{field_caption} - {field_name}"
                    all_fields.add(field_info)
                
            except Exception as e:
                print(f"  处理表 {table_name} 时发生错误: {str(e)}")
        
        # 准备输出文件
        output_lines = []
        output_lines.append("# 科来网络分析系统 - 统计表字段枚举")
        output_lines.append("# 格式: 中文描述 - 英文字段名")
        output_lines.append("# 生成时间: " + str(json.dumps({"timestamp": "auto"}, ensure_ascii=False)))
        output_lines.append("")
        output_lines.append("## 所有字段汇总 (已去重)")
        output_lines.append(f"字段总数: {len(all_fields)}")
        output_lines.append("")
        
        # 按字段名排序并输出
        sorted_fields = sorted(all_fields)
        for field_info in sorted_fields:
            output_lines.append(f"- {field_info}")
        
        # 写入输出文件
        output_file = "统计表字段枚举_中英文对照.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(output_lines))
        
        print(f"\n枚举完成！结果已保存到: {output_file}")
        print(f"共处理了 {len(tables)} 个统计表")
        print(f"去重后字段总数: {len(all_fields)}")
        
    except Exception as e:
        print(f"枚举过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 断开连接
        if api_client:
            try:
                api_client.logout()
                print("已断开API连接")
            except:
                pass

if __name__ == "__main__":
    enumerate_all_table_fields() 