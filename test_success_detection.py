#!/usr/bin/env python3
"""
测试成功判断逻辑
"""

import re


def _is_query_successful(final_answer: str) -> bool:
    """判断查询是否成功

    Args:
        final_answer: 最终答案

    Returns:
        是否成功
    """
    if not final_answer or len(final_answer) < 10:
        return False

    final_answer_lower = final_answer.lower()

    # 检查明确的成功标识
    success_indicators = [
        '"success": true',
        '"success":true',
        'success: true',
        '查询成功',
        '执行成功',
        '操作成功'
    ]

    for indicator in success_indicators:
        if indicator in final_answer_lower:
            return True

    # 检查明确的失败标识
    failure_indicators = [
        '"success": false',
        '"success":false',
        'success: false',
        '查询失败',
        '执行失败',
        '操作失败',
        'error occurred',
        'exception',
        'failed to',
        'cannot',
        'unable to'
    ]

    for indicator in failure_indicators:
        if indicator in final_answer_lower:
            return False

    # 如果包含error_code，检查其值
    if 'error_code' in final_answer_lower:
        try:
            # 查找error_code的值
            error_code_match = re.search(r'"error_code":\s*(\d+)', final_answer)
            if error_code_match:
                error_code = int(error_code_match.group(1))
                return error_code == 0  # 0表示成功
        except:
            pass

    # 默认情况：如果答案足够长且没有明显的错误标识，认为成功
    return len(final_answer) > 50 and 'error' not in final_answer_lower


def test_success_detection():
    """测试成功判断逻辑"""
    print("=== 测试成功判断逻辑 ===")
    
    # 测试用例
    test_cases = [
        # 成功案例
        {
            "answer": '{"success": true, "error_code": 0, "message": "查询成功", "data": "some data"}',
            "expected": True,
            "description": "JSON格式成功响应"
        },
        {
            "answer": '查询成功，返回了以下数据：total_byte,total_packet,total_bitps...',
            "expected": True,
            "description": "中文成功消息"
        },
        {
            "answer": 'Operation completed successfully with the following results...',
            "expected": True,
            "description": "英文成功消息"
        },
        {
            "answer": '{"success":true,"error_code":0,"data":"result"}',
            "expected": True,
            "description": "紧凑JSON格式"
        },
        
        # 失败案例
        {
            "answer": '{"success": false, "error_code": 1, "message": "查询失败"}',
            "expected": False,
            "description": "JSON格式失败响应"
        },
        {
            "answer": 'Error occurred while processing the request',
            "expected": False,
            "description": "错误消息"
        },
        {
            "answer": '查询失败，无法连接到数据库',
            "expected": False,
            "description": "中文失败消息"
        },
        {
            "answer": 'Failed to execute the command',
            "expected": False,
            "description": "英文失败消息"
        },
        {
            "answer": 'Cannot process this request',
            "expected": False,
            "description": "无法处理消息"
        },
        
        # 边界案例
        {
            "answer": '',
            "expected": False,
            "description": "空答案"
        },
        {
            "answer": 'OK',
            "expected": False,
            "description": "过短答案"
        },
        {
            "answer": '这是一个很长的答案，包含了详细的信息，但没有明确的成功或失败标识，应该被认为是成功的',
            "expected": True,
            "description": "长答案无明确标识"
        },
        {
            "answer": '这是一个包含error关键词但实际成功的答案，比如error_code为0表示成功',
            "expected": False,
            "description": "包含error但无error_code"
        }
    ]
    
    # 执行测试
    passed = 0
    failed = 0

    for i, case in enumerate(test_cases, 1):
        result = _is_query_successful(case["answer"])
        expected = case["expected"]
        
        status = "✓ PASS" if result == expected else "✗ FAIL"
        print(f"{i:2d}. {status} - {case['description']}")
        print(f"    答案: {case['answer'][:80]}{'...' if len(case['answer']) > 80 else ''}")
        print(f"    预期: {expected}, 实际: {result}")
        print()
        
        if result == expected:
            passed += 1
        else:
            failed += 1
    
    print(f"测试结果: {passed} 通过, {failed} 失败")
    
    # 测试实际的MCP响应
    print("\n=== 测试实际MCP响应 ===")
    actual_response = """{
  "success": true,
  "error_code": 0,
  "message": "查询成功",
  "data": "total_byte,total_packet,total_bitps,total_packetps,total_utilization,new_flow_count\\n615721309429,900069199,57011892.214400,10417.588150,0.057012,110328351",
  "raw_data_size": 197,
  "query_info": {
    "table": "summary",
    "time_range": "2025-07-17 00:00:00 到 2025-07-17 23:59:59",
    "fields_count": 6,
    "keys_count": 1,
    "filter": "无",
    "topcount": 1000,
    "keycount": null,
    "fieldcount": null
  }
}"""
    
    result = strategy._is_query_successful(actual_response)
    print(f"实际MCP响应判断结果: {result}")
    print(f"预期结果: True")
    print(f"判断{'正确' if result else '错误'}")


if __name__ == "__main__":
    test_success_detection()
