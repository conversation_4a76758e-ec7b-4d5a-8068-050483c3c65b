# 经验管理器使用说明

## 概述

经验管理器是一个智能的查询经验保存和复用系统，能够：
- 自动保存成功的查询经验
- 使用embedding向量进行相似度检索
- 为新查询提供few-shot学习示例
- 提高查询效率和准确性

## 功能特性

### 1. 自动经验保存
- 自动保存成功的查询及其结果
- 记录执行时间、token使用量、工具使用情况
- 支持自定义保存条件（最小执行时间、token使用量等）

### 2. 智能相似度检索
- 支持embedding向量相似度计算
- 支持文本相似度计算（Jaccard、字符、长度相似度）
- 可配置相似度阈值和返回数量

### 3. Few-shot学习
- 自动为新查询检索相似经验
- 将相似经验作为示例添加到系统提示词中
- 提高LLM的推理能力和一致性

## 配置说明

### experience_config.json 配置文件

```json
{
  "experience_manager": {
    "enabled": true,                    // 是否启用经验管理器
    "experience_file": "react_experiences.json",  // 经验存储文件
    "embedding_model": "embedding-3",   // embedding模型
    "similarity_threshold": 0.7,        // 相似度阈值
    "max_experiences": 1000,            // 最大经验数量
    "auto_save": true,                  // 是否自动保存
    "use_embedding": true,              // 是否使用embedding
    "top_k_similar": 3,                 // 返回相似经验数量
    "min_execution_time": 1.0,          // 最小执行时间（秒）
    "min_token_usage": 0                // 最小token使用量
  },
  "embedding_config": {
    "provider": "openai",               // embedding服务提供商
    "model": "embedding-3",             // embedding模型
    "base_url": "https://...",          // API地址
    "api_key": "your_api_key",          // API密钥
    "batch_size": 32,                   // 批处理大小
    "timeout": 30                       // 超时时间
  }
}
```

## 使用方法

### 1. 在mcpReAct策略中的集成

经验管理器已经集成到`mcpReAct.py`中，会自动：

1. **查询开始时**：检索相似的历史经验
2. **生成提示词时**：将相似经验作为few-shot示例添加到系统提示词
3. **查询结束时**：保存成功的查询经验

### 2. 手动使用经验管理器

```python
from experience_manager import ExperienceManager
import asyncio

async def main():
    # 初始化经验管理器
    manager = ExperienceManager()
    
    # 保存经验
    await manager.save_experience(
        query="用户查询",
        final_answer="最终答案",
        execution_time=10.5,
        token_usage=150,
        tools_used=["tool1", "tool2"],
        success=True,
        metadata={"model": "gpt-4"}
    )
    
    # 查找相似经验
    similar_experiences = await manager.find_similar_experiences("相似查询")
    
    for exp, similarity in similar_experiences:
        print(f"相似度: {similarity:.3f}")
        print(f"查询: {exp.query}")
        print(f"答案: {exp.final_answer}")
        print(f"工具: {exp.tools_used}")

asyncio.run(main())
```

### 3. 经验统计

```python
# 获取经验统计信息
summary = manager.get_experience_summary()
print(f"总经验数: {summary['total_experiences']}")
print(f"成功率: {summary['success_rate']:.2%}")
print(f"平均执行时间: {summary['avg_execution_time']:.2f}s")
print(f"最常用工具: {summary['most_used_tools']}")
```

## 经验数据结构

每条经验包含以下信息：

```python
{
    "query": "用户查询",
    "query_hash": "查询哈希值",
    "final_answer": "最终答案",
    "execution_time": 10.5,
    "token_usage": 150,
    "tools_used": ["tool1", "tool2"],
    "success": true,
    "timestamp": "2025-07-17T16:25:35.190250",
    "embedding": [0.1, 0.2, ...],  // embedding向量
    "metadata": {
        "iterations": 3,
        "model": "gpt-4",
        "provider": "openai"
    }
}
```

## Few-shot示例格式

系统会自动将相似经验格式化为few-shot示例：

```
## Similar Query Examples (Few-shot Learning)
Here are some similar successful queries for your reference:

### Example 1 (Similarity: 0.94)
**Query**: 在192.168.163.209上有多少条链路？

**Tools Used**:
- stats_mcp_server_query_links
- stats_mcp_server_get_link_info

**Final Answer**: 在 192.168.163.209 上有 3 条链路，分别是：链路一 (id: 1)、链路二 (id: 2) 和回放链路 (id: 10)。

Please learn from these examples and apply similar approaches when appropriate.
```

## 性能优化建议

1. **合理设置相似度阈值**：过低会返回不相关经验，过高会错过有用经验
2. **控制经验数量**：定期清理旧经验，保持合理的数据量
3. **优化embedding服务**：使用高质量的embedding模型和稳定的API服务
4. **监控保存条件**：根据实际使用情况调整最小执行时间和token使用量

## 故障排除

### 常见问题

1. **经验不保存**
   - 检查`enabled`配置是否为true
   - 检查是否满足`min_execution_time`和`min_token_usage`条件
   - 检查查询是否标记为成功

2. **相似度检索不准确**
   - 调整`similarity_threshold`阈值
   - 检查embedding服务是否正常
   - 考虑使用文本相似度作为备选

3. **性能问题**
   - 减少`max_experiences`数量
   - 优化embedding服务响应时间
   - 考虑异步处理

### 日志查看

经验管理器会记录详细的日志信息：

```bash
# 查看经验管理器日志
tail -f experience_manager.log

# 查看mcpReAct策略中的经验相关日志
grep "\[EXPERIENCE\]" your_log_file.log
```

## 测试

运行测试脚本验证功能：

```bash
python test_experience_manager.py
```

这将测试经验保存、检索和各种边界条件。
