#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM智能MCP调用链生成演示
展示完全基于LLM的动态调用链生成，去除所有硬编码
"""

import asyncio
import json
from datetime import datetime
from packet_drop_hypergraph_rag import PacketDropHyperGraphRAG
from autogen_mcp_executor import get_llm_generated_mcp_chain, get_minimal_fallback_chain

def print_separator(title):
    """打印分隔符"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_chain_features(chain_data):
    """打印调用链特性"""
    meta = chain_data["meta"]
    steps = chain_data["steps"]
    branches = chain_data["branches"]
    
    print(f"\n📊 调用链特性:")
    print(f"  • 生成策略: {meta.get('diagnostic_strategy', 'unknown')}")
    print(f"  • 总步骤数: {meta.get('total_steps', len(steps))}")
    print(f"  • 分支数量: {meta.get('total_branches', len(branches))}")
    print(f"  • 预估耗时: {meta.get('estimated_total_duration', 'N/A')}")
    print(f"  • 包含分支: {'是' if meta.get('has_branches', False) else '否'}")
    
    print(f"\n🔧 顺序执行步骤:")
    for step in steps:
        step_id = step.get("step_id", "unknown")
        tool = step.get("tool", "unknown")
        purpose = step.get("purpose", "unknown")
        print(f"  步骤{step_id}: {tool} - {purpose}")
        
        # 显示决策点
        if "decision_point" in step:
            decision = step["decision_point"]
            print(f"    🌿 决策点: {decision.get('condition', 'unknown')}")
            print(f"       分支选项: {decision.get('branches', [])}")
    
    if branches:
        print(f"\n🌿 条件分支:")
        for branch_name, branch_info in branches.items():
            print(f"  分支 [{branch_name}]:")
            print(f"    条件: {branch_info.get('condition', 'unknown')}")
            print(f"    描述: {branch_info.get('description', 'unknown')}")
            print(f"    步骤数: {len(branch_info.get('steps', []))}")
            
            for step in branch_info.get("steps", []):
                step_id = step.get("step_id", "unknown")
                tool = step.get("tool", "unknown")
                purpose = step.get("purpose", "unknown")
                print(f"      {step_id}: {tool} - {purpose}")

async def demo_llm_vs_hardcoded():
    """演示LLM生成 vs 硬编码对比"""
    print_separator("LLM智能生成 vs 硬编码对比")
    
    alert_info = {
        "server_ip": "*************",
        "link_id": 2,
        "trigger_time": "2025-06-16 14:00:00",
        "alert_name": "TCP重传率100%警报",
        "condition": "TCP重传率为100% 大于 90%"
    }
    
    print(f"\n📋 测试警报信息:")
    for key, value in alert_info.items():
        print(f"  {key}: {value}")
    
    print(f"\n🧠 方案1: LLM智能生成")
    try:
        llm_chain = await get_llm_generated_mcp_chain(alert_info)
        print(f"✅ LLM生成成功")
        print_chain_features(llm_chain)
        
        # 保存LLM生成的调用链
        with open("mcp_chain_llm_generated.json", "w", encoding="utf-8") as f:
            json.dump(llm_chain, f, indent=2, ensure_ascii=False)
        print(f"\n💾 LLM生成的调用链已保存到: mcp_chain_llm_generated.json")
        
    except Exception as e:
        print(f"❌ LLM生成失败: {e}")
        llm_chain = None
    
    print(f"\n🔧 方案2: 硬编码备用方案")
    hardcoded_chain = get_minimal_fallback_chain(alert_info)
    print(f"✅ 硬编码生成成功")
    print_chain_features(hardcoded_chain)
    
    # 对比分析
    print_separator("对比分析")
    
    if llm_chain:
        llm_meta = llm_chain["meta"]
        hardcoded_meta = hardcoded_chain["meta"]
        
        print(f"\n📊 定量对比:")
        print(f"  步骤数量:")
        print(f"    LLM生成: {llm_meta.get('total_steps', 0)} 步")
        print(f"    硬编码: {hardcoded_meta.get('total_steps', 0)} 步")
        
        print(f"  分支复杂度:")
        print(f"    LLM生成: {llm_meta.get('total_branches', 0)} 个分支")
        print(f"    硬编码: {hardcoded_meta.get('total_branches', 0)} 个分支")
        
        print(f"  诊断策略:")
        print(f"    LLM生成: {llm_meta.get('diagnostic_strategy', 'unknown')}")
        print(f"    硬编码: {hardcoded_meta.get('diagnostic_strategy', 'unknown')}")
        
        print(f"\n🎯 定性对比:")
        print(f"  LLM生成优势:")
        print(f"    • 动态适配警报信息")
        print(f"    • 智能选择诊断策略")
        print(f"    • 灵活的参数配置")
        print(f"    • 可扩展新工具和知识")
        
        print(f"  硬编码方案:")
        print(f"    • 固定逻辑，可靠稳定")
        print(f"    • 不依赖外部LLM服务")
        print(f"    • 执行速度快")
        print(f"    • 适合紧急备用")

async def demo_different_scenarios():
    """演示不同场景的LLM生成效果"""
    print_separator("不同场景LLM生成效果展示")
    
    scenarios = [
        {
            "name": "高重传率短期会话",
            "alert": {
                "server_ip": "*************", 
                "link_id": 2,
                "trigger_time": "2025-06-16 14:00:00",
                "alert_name": "短期TCP重传警报",
                "condition": "TCP重传率为100%，会话持续2秒",
                "session_duration": 2,
                "retransmission_rate": 100
            },
            "description": "典型的序列号跳变导致的跳包场景"
        },
        {
            "name": "长期连接间歇重传",
            "alert": {
                "server_ip": "***********",
                "link_id": 1,
                "trigger_time": "2025-06-16 15:30:00", 
                "alert_name": "长期连接重传警报",
                "condition": "长期连接出现间歇性100%重传",
                "session_duration": 1800,
                "retransmission_rate": 100
            },
            "description": "需要时间桶分析的长期会话跳包"
        },
        {
            "name": "多链路并发异常",
            "alert": {
                "server_ip": "***********",
                "link_id": 3,
                "trigger_time": "2025-06-16 16:45:00",
                "alert_name": "多链路异常警报", 
                "condition": "多个链路同时出现重传异常",
                "affected_links": [1, 2, 3],
                "retransmission_rate": 85
            },
            "description": "复杂的多链路网络异常场景"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n🎯 场景 {i}: {scenario['name']}")
        print(f"📝 描述: {scenario['description']}")
        
        alert = scenario["alert"]
        print(f"\n📋 警报详情:")
        for key, value in alert.items():
            print(f"  {key}: {value}")
        
        try:
            print(f"\n🧠 LLM分析中...")
            chain = await get_llm_generated_mcp_chain(alert)
            print(f"✅ LLM生成成功")
            
            print_chain_features(chain)
            
            # 保存场景特定的调用链
            filename = f"mcp_chain_scenario_{i}_{scenario['name'].replace(' ', '_')}.json"
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(chain, f, indent=2, ensure_ascii=False)
            print(f"💾 调用链已保存到: {filename}")
            
        except Exception as e:
            print(f"❌ LLM生成失败: {e}")
            print(f"📦 使用备用方案...")
            chain = get_minimal_fallback_chain(alert)
            print_chain_features(chain)

async def demo_hypergraphrag_integration():
    """演示HyperGraphRAG集成效果"""
    print_separator("HyperGraphRAG + LLM集成演示")
    
    alert_info = {
        "server_ip": "*************",
        "link_id": 2,
        "trigger_time": "2025-06-16 14:00:00",
        "alert_name": "TCP重传率100%警报",
        "condition": "TCP重传率为100% 大于 90%"
    }
    
    print(f"\n🚀 初始化HyperGraphRAG知识库...")
    try:
        rag_system = PacketDropHyperGraphRAG()
        await rag_system.insert_packet_drop_knowledge()
        print(f"✅ 知识库初始化成功")
        
        print(f"\n🧠 基于HyperGraphRAG + LLM生成调用链...")
        hypergraph_chain = await rag_system.get_diagnostic_mcp_chain(alert_info)
        print(f"✅ HyperGraphRAG + LLM生成成功")
        
        print_chain_features(hypergraph_chain)
        
        # 保存HyperGraphRAG生成的调用链
        with open("mcp_chain_hypergraphrag_llm.json", "w", encoding="utf-8") as f:
            json.dump(hypergraph_chain, f, indent=2, ensure_ascii=False)
        print(f"\n💾 HyperGraphRAG+LLM调用链已保存到: mcp_chain_hypergraphrag_llm.json")
        
    except Exception as e:
        print(f"❌ HyperGraphRAG初始化失败: {e}")
        print(f"📦 降级到纯LLM生成...")
        
        try:
            llm_only_chain = await get_llm_generated_mcp_chain(alert_info)
            print(f"✅ 纯LLM生成成功")
            print_chain_features(llm_only_chain)
        except Exception as e2:
            print(f"❌ 纯LLM也失败: {e2}")
            print(f"📦 使用最终备用方案...")
            fallback_chain = get_minimal_fallback_chain(alert_info)
            print_chain_features(fallback_chain)

async def main():
    """主演示函数"""
    print("🎉 LLM智能MCP调用链生成系统演示")
    print("=" * 60)
    print("本演示展示以下核心功能:")
    print("1. 🧠 完全基于LLM的动态调用链生成")
    print("2. 🔧 去除所有硬编码逻辑")
    print("3. 🌿 智能分支和条件逻辑")
    print("4. 📊 自适应参数配置")
    print("5. 🎯 场景特化的诊断策略")
    print("6. 🚀 HyperGraphRAG知识增强")
    
    # 演示LLM vs 硬编码对比
    await demo_llm_vs_hardcoded()
    
    # 演示不同场景
    await demo_different_scenarios()
    
    # 演示HyperGraphRAG集成
    await demo_hypergraphrag_integration()
    
    print_separator("演示总结")
    print("✨ LLM智能生成系统的核心优势:")
    print("  • 🎯 零硬编码：完全动态生成，适应性强")
    print("  • 🧠 智能分析：基于实际警报信息制定策略") 
    print("  • 🌿 灵活分支：根据场景自动生成条件逻辑")
    print("  • 📈 可扩展：轻松添加新工具和诊断知识")
    print("  • 🔄 自适应：根据反馈持续优化生成质量")
    print("  • 🚀 知识增强：集成HyperGraphRAG专业知识")
    
    print(f"\n📁 生成的文件:")
    print(f"  • mcp_chain_llm_generated.json - LLM生成的调用链")
    print(f"  • mcp_chain_scenario_*.json - 各场景特化调用链")
    print(f"  • mcp_chain_hypergraphrag_llm.json - 知识增强调用链")

if __name__ == "__main__":
    asyncio.run(main()) 