#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM步骤总结器 - 使用大模型智能总结查询执行步骤
"""

import json
import asyncio
import requests
from typing import List, Dict, Any, Optional
from experience_manager import ExperienceManager, QueryExperience


class LLMStepSummarizer:
    """LLM步骤总结器"""
    
    def __init__(self, config_path: str = "experience_config.json"):
        """初始化总结器
        
        Args:
            config_path: 配置文件路径
        """
        self.experience_manager = ExperienceManager(config_path)
        self.config = self.experience_manager.config
        self.embedding_config = self.config["embedding_config"]
    
    async def summarize_experience_steps(self, experience: QueryExperience) -> List[Dict[str, Any]]:
        """总结经验中的执行步骤
        
        Args:
            experience: 查询经验对象
            
        Returns:
            步骤总结列表
        """
        prompt = self._build_step_summary_prompt(experience)
        return await self._call_llm_for_step_summary(prompt)
    
    def _build_step_summary_prompt(self, experience: QueryExperience) -> str:
        """构建用于LLM总结步骤的提示词"""
        prompt = f"""请分析以下网络统计查询的执行过程，为每个步骤生成简洁准确的中文描述。

用户查询: {experience.query}

执行步骤详情:
"""
        
        step_num = 1
        for iteration in experience.iterations:
            if iteration.action and iteration.tool_executions:
                for tool_exec in iteration.tool_executions:
                    if tool_exec.success:
                        prompt += f"\n=== 步骤 {step_num} ===\n"
                        prompt += f"工具名称: {tool_exec.tool_name}\n"
                        prompt += f"输入参数: {json.dumps(tool_exec.parameters, ensure_ascii=False)}\n"
                        prompt += f"AI思考: {iteration.thought}\n"
                        prompt += f"执行结果: {tool_exec.response[:300]}...\n"
                        prompt += f"观察总结: {iteration.observation}\n"
                        step_num += 1
        
        prompt += """

请为每个步骤生成:
1. 一句话的中文描述，准确说明这个步骤的目的和作用
2. 对应的完整action JSON格式

要求:
- 描述要准确反映步骤的实际作用，不要使用通用描述
- 根据参数内容推断具体操作（如查询特定表、获取特定配置等）
- 保持原有的action和action_input格式不变
- 按执行顺序排列

输出格式（严格按照此JSON格式）:
```json
[
  {
    "step": 1,
    "description": "登录到***************的API服务器",
    "action_json": "{'action': 'stats-query-mcp_mcp_tool_setup_api_connection', 'action_input': {'url': 'https://***************:8080/'}}"
  },
  {
    "step": 2,
    "description": "查询netlink配置获取链路一的ID信息",
    "action_json": "{'action': 'stats-query-mcp_mcp_tool_get_config', 'action_input': {'config_type': 'netlink', 'netlink_id': 64}}"
  }
]
```
"""
        return prompt
    
    async def _call_llm_for_step_summary(self, prompt: str) -> List[Dict[str, Any]]:
        """调用LLM生成步骤总结"""
        try:
            headers = {
                "Authorization": f"Bearer {self.embedding_config['api_key']}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "glm-4",  # 使用智谱AI的模型
                "messages": [
                    {
                        "role": "system", 
                        "content": "你是一个专业的网络统计分析专家，擅长总结技术操作步骤。请准确分析每个步骤的具体作用。"
                    },
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.1,
                "max_tokens": 2000
            }
            
            # 使用智谱AI的API端点
            api_url = self.embedding_config.get("chat_api_url", "https://open.bigmodel.cn/api/paas/v4/chat/completions")
            
            response = requests.post(
                api_url,
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                
                # 解析JSON响应
                import re
                json_match = re.search(r'```json\n(.*?)\n```', content, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                    return json.loads(json_str)
                else:
                    # 尝试直接解析
                    try:
                        return json.loads(content)
                    except:
                        print(f"LLM响应解析失败: {content}")
                        return []
            else:
                print(f"LLM API调用失败: {response.status_code}, {response.text}")
                return []
                
        except Exception as e:
            print(f"调用LLM失败: {e}")
            return []
    
    async def generate_improved_few_shot_example(self, experience: QueryExperience, similarity: float) -> str:
        """生成改进的few-shot示例"""
        step_summaries = await self.summarize_experience_steps(experience)
        
        if not step_summaries:
            # 回退到传统方法
            return self.experience_manager.generate_detailed_few_shot_example(experience, similarity)
        
        example = f"## Example\n"
        example += f"Example 1：{experience.query}\n"
        example += f"Step:\n"
        
        # 使用LLM生成的步骤总结
        for summary in step_summaries:
            example += f"{summary['step']}. {summary['description']}\n"
            example += f"{summary['action_json']}\n"
        
        # 添加预期结果
        final_answer = experience.final_answer
        if len(final_answer) > 200:
            final_answer = self._extract_key_result(final_answer)
        
        example += f"\n**Expected Result**: {final_answer}\n\n"
        example += f"**Execution Tips**: This query took {experience.execution_time:.1f}s with {experience.total_iterations} iterations.\n\n"
        
        return example
    
    def _extract_key_result(self, final_answer: str) -> str:
        """提取关键结果信息"""
        try:
            if '"success": true' in final_answer:
                import re
                data_match = re.search(r'"data":\s*"([^"]*)"', final_answer)
                if data_match:
                    data_content = data_match.group(1)
                    if len(data_content) > 100:
                        data_content = data_content[:100] + "..."
                    return f'{{"success": true, "error_code": 0, "message": "查询成功", "data": "{data_content}"}}'
            
            return final_answer[:200] + "..."
            
        except Exception:
            return final_answer[:200] + "..."
    
    async def batch_improve_experiences(self, output_file: str = "improved_experiences.json"):
        """批量改进现有经验的few-shot示例"""
        improved_experiences = []
        
        for experience in self.experience_manager.experiences:
            if experience.success and experience.iterations:
                print(f"正在改进经验: {experience.query[:50]}...")
                
                improved_example = await self.generate_improved_few_shot_example(experience, 1.0)
                
                improved_experiences.append({
                    "original_query": experience.query,
                    "improved_few_shot": improved_example,
                    "execution_time": experience.execution_time,
                    "total_iterations": experience.total_iterations,
                    "tools_used": experience.tools_used
                })
        
        # 保存改进的示例
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(improved_experiences, f, ensure_ascii=False, indent=2)
        
        print(f"已改进 {len(improved_experiences)} 个经验，保存到 {output_file}")
        return improved_experiences


async def main():
    """主函数"""
    import sys
    
    summarizer = LLMStepSummarizer()
    
    if len(sys.argv) < 2:
        print("用法:")
        print("  python llm_step_summarizer.py improve     # 改进所有经验")
        print("  python llm_step_summarizer.py test        # 测试单个经验")
        return
    
    command = sys.argv[1]
    
    if command == "improve":
        await summarizer.batch_improve_experiences()
    elif command == "test":
        # 测试第一个经验
        if summarizer.experience_manager.experiences:
            exp = summarizer.experience_manager.experiences[0]
            improved = await summarizer.generate_improved_few_shot_example(exp, 1.0)
            print("改进的Few-shot示例:")
            print("=" * 60)
            print(improved)
        else:
            print("没有找到经验数据")
    else:
        print("无效的命令")


if __name__ == "__main__":
    asyncio.run(main())
