# LLM步骤总结功能使用说明

## 概述

新的LLM步骤总结功能解决了之前硬编码工具描述映射的问题，现在系统会：

1. **在保存经验时自动使用LLM分析执行步骤**
2. **将LLM生成的步骤总结存储在经验数据中**
3. **在生成few-shot示例时直接使用存储的总结**

## 工作流程

### 1. 保存经验时的LLM总结

当查询执行完成后，系统会：

```python
# 在 experience_manager.save_experience() 中
if (iterations and self.experience_config.get("use_llm_for_step_summary", False)):
    # 调用LLM分析执行步骤
    step_summaries = await self._generate_llm_step_summaries(query, iterations)
    
    # 将总结存储在metadata中
    if step_summaries:
        final_metadata["llm_step_summaries"] = step_summaries
```

### 2. LLM分析过程

LLM会分析每个执行步骤的：
- **工具名称**: 使用的具体工具
- **输入参数**: 传递给工具的参数
- **AI思考**: 执行前的思考过程
- **执行结果**: 工具返回的结果
- **观察总结**: 对结果的观察

然后生成准确的中文描述，例如：
- 不是通用的"登录API"
- 而是具体的"登录到***************的API服务器"

### 3. 生成few-shot示例

在生成few-shot示例时：

```python
# 在 generate_detailed_few_shot_example() 中
llm_summaries = experience.metadata.get("llm_step_summaries", [])

if llm_summaries:
    # 直接使用存储的LLM总结
    for summary in llm_summaries:
        example += f"{summary['step']}. {summary['description']}\n"
        example += f"{summary['action_json']}\n"
else:
    # 回退到传统方法
    # ...
```

## 配置方法

### 启用LLM步骤总结

在 `experience_config.json` 中设置：

```json
{
  "experience_manager": {
    "use_llm_for_step_summary": true
  },
  "embedding_config": {
    "chat_api_url": "https://open.bigmodel.cn/api/paas/v4/chat/completions",
    "api_key": "your_api_key_here"
  }
}
```

### API配置

系统使用智谱AI的GLM-4模型进行步骤总结：
- **模型**: glm-4
- **温度**: 0.1 (确保输出稳定)
- **最大tokens**: 2000

## 使用方法

### 1. 测试存储时LLM总结

```bash
python test_llm_summarizer.py storage
```

这会：
- 创建测试经验数据
- 启用LLM总结功能
- 保存经验（自动生成LLM总结）
- 显示生成的few-shot示例

### 2. 对比传统方法和LLM方法

```bash
python test_llm_summarizer.py compare
```

### 3. 分析现有经验

```bash
python experience_analyzer.py summary
```

## 输出示例

### LLM生成的步骤总结

```json
[
  {
    "step": 1,
    "description": "登录到***************的API服务器",
    "action_json": "{'action': 'stats-query-mcp_mcp_tool_setup_api_connection', 'action_input': {'url': 'https://***************:8080/'}}"
  },
  {
    "step": 2,
    "description": "查询netlink配置获取链路一的ID信息",
    "action_json": "{'action': 'stats-query-mcp_mcp_tool_get_config', 'action_input': {'config_type': 'netlink', 'netlink_id': 64}}"
  },
  {
    "step": 3,
    "description": "查询summary表获取指定时间段的统计数据",
    "action_json": "{'action': 'stats-query-mcp_mcp_tool_query_statistics_table', 'action_input': {'table': 'summary', 'begintime': '2025-07-17 00:00:00', 'endtime': '2025-07-17 23:59:59', 'fields': 'total_byte,total_packet,total_bitps', 'keys': 'time', 'netlink': 1}}"
  }
]
```

### 生成的Few-shot示例

```
## Example
Example 1：查询*************** 上链路名称为链路一的2025-07-17一天的概要统计
Step:
1. 登录到***************的API服务器
{'action': 'stats-query-mcp_mcp_tool_setup_api_connection', 'action_input': {'url': 'https://***************:8080/'}}
2. 查询netlink配置获取链路一的ID信息
{'action': 'stats-query-mcp_mcp_tool_get_config', 'action_input': {'config_type': 'netlink', 'netlink_id': 64}}
3. 查询summary表获取指定时间段的统计数据
{'action': 'stats-query-mcp_mcp_tool_query_statistics_table', 'action_input': {'table': 'summary', 'begintime': '2025-07-17 00:00:00', 'endtime': '2025-07-17 23:59:59', 'fields': 'total_byte,total_packet,total_bitps', 'keys': 'time', 'netlink': 1}}

**Expected Result**: {"success": true, "error_code": 0, "message": "查询成功", "data": "total_byte,total_packet,total_bitps..."}

**Execution Tips**: This query took 21.3s with 3 iterations.
```

## 优势

### 1. 准确性
- 基于实际执行上下文生成描述
- 不依赖硬编码映射
- 能够理解参数的具体含义

### 2. 效率
- 在保存时一次性生成总结
- 检索时直接使用存储的总结
- 避免重复的LLM调用

### 3. 一致性
- 所有相同查询使用相同的步骤描述
- 保证few-shot示例的质量

### 4. 可扩展性
- 支持新的工具和参数
- 不需要手动维护映射表

## 故障排除

### 1. LLM总结生成失败
- 检查API密钥配置
- 确认网络连接
- 查看日志中的错误信息

### 2. 回退机制
如果LLM总结失败，系统会自动回退到传统方法：
```
[EXPERIENCE] LLM步骤总结生成失败: API调用超时
[EXPERIENCE] 使用传统方法生成步骤描述
```

### 3. 调试信息
系统会输出详细的调试信息：
```
[EXPERIENCE] 成功生成LLM步骤总结: 3个步骤
[EXPERIENCE] 使用存储的LLM步骤总结: 3个步骤
```

## 注意事项

1. **API成本**: LLM调用会产生API费用，建议合理控制调用频率
2. **网络依赖**: 需要稳定的网络连接访问LLM API
3. **存储空间**: LLM总结会增加经验数据的存储大小
4. **兼容性**: 旧的经验数据会自动回退到传统方法

## 未来改进

1. **本地LLM支持**: 支持本地部署的LLM模型
2. **缓存机制**: 对相似步骤的总结进行缓存
3. **质量评估**: 添加总结质量的自动评估
4. **多语言支持**: 支持其他语言的步骤描述
