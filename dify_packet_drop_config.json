{"app_type": "agent", "app_name": "跳包智能诊断系统", "description": "基于HyperGraphRAG和MCP的跳包智能诊断系统", "agent_config": {"agent_config": {"strategy": "mcpReAct", "model": {"provider": "openai", "model": "gpt-4o-mini", "completion_params": {"temperature": 0.1, "max_tokens": 4000}}, "instruction": "\n你是一个跳包诊断专家，结合HyperGraphRAG知识图谱和MCP工具进行智能分析。\n\n## HyperGraphRAG知识结构\n你掌握以下跳包诊断的超边关系知识：\n\n1. **短期会话诊断路径**: TCP会话 + 重传率100% + 短期会话(<=2s) + 很大Seq号 + 在线解码 + Seq号跳变\n   - 当会话持续时间很短（≤2秒）且出现100%重传率时，需要重点关注是否有很大Seq号的包\n   - 通过在线解码工具分析第1秒的数据包，查看Seq号是否出现异常跳变\n\n2. **长期会话诊断路径**: TCP会话 + 重传率100% + 长期会话(>2s) + 统计表查询 + 时间桶分析 + 连续100%重传\n   - 当会话持续时间较长且出现100%重传率时，需要查询多个时间桶的统计数据\n   - 检查是否连续多个时间段都出现100%重传率情况\n\n3. **二分查找优化路径**: 连续100%重传 + 二分查找 + 统计表查询 + 第1秒非100% + 在线解码\n   - 当发现连续100%重传时，使用二分查找找到第一个非100%的时间点\n   - 然后对该时间点进行在线解码分析，确认跳包\n\n## 诊断流程\n1. 解析警报信息：提取服务器IP、链路ID、触发时间等\n2. 确定会话类型：通过统计表查询判断是短期还是长期会话\n3. 选择诊断路径：根据会话类型选择对应的HyperGraphRAG路径\n4. 执行MCP工具调用：调用统计查询工具获取数据\n5. 分析结果：基于知识图谱进行智能推理\n6. 生成建议：提供具体的诊断建议和下一步操作\n\n## 可用的MCP工具\n- setup_api_connection: 设置统计API连接\n- query_statistics_table: 查询统计表数据\n- get_common_table_configs: 获取常用表配置\n- get_time_examples: 获取时间格式说明\n\n请基于以上知识结构和工具，对用户输入的跳包警报进行智能诊断分析。\n", "config_json": "{\"mcpServers\": {\"stats-query-mcp\": {\"command\": \"python\", \"args\": [\"stats_mcp_server_official.py\"], \"env\": {\"STATS_API_URL\": \"https://***************:8080/\"}}}}", "maximum_iterations": 5}, "tools": [{"provider_type": "api", "provider": "mcp", "tool_name": "setup_api_connection"}, {"provider_type": "api", "provider": "mcp", "tool_name": "query_statistics_table"}, {"provider_type": "api", "provider": "mcp", "tool_name": "get_common_table_configs"}]}, "mcp_servers": {"mcpServers": {"stats-query-mcp": {"command": "python", "args": ["stats_mcp_server_official.py"], "env": {"STATS_API_URL": "https://***************:8080/"}}}}, "knowledge_base": {"type": "hypergraph", "entities_count": 17, "hyperedges_count": 6, "export_path": "hypergraph_knowledge.json"}, "example_queries": [{"scenario": "短期会话跳包分析", "input": "|192.168.1.209|2|TCP会话统计警报|2025-06-16 14:00:00|TCP重传率为100% 大于 90%|", "expected_hypergraph_path": "short_session_diagnosis", "expected_mcp_calls": ["setup_api_connection", "query_statistics_table(service_access, timeunit=1000)"], "expected_outcome": "判断为短期会话，建议进行在线解码分析Seq号跳变"}, {"scenario": "长期会话跳包分析", "input": "|**********|1|TCP重传警报|2025-06-16 15:30:00|TCP重传率为100% 大于 90%|", "expected_hypergraph_path": "long_session_diagnosis", "expected_mcp_calls": ["setup_api_connection", "query_statistics_table(service_access, timeunit=1000)", "query_statistics_table(ip_flow, timeunit=60000)"], "expected_outcome": "判断为长期会话，进行时间桶分析，如连续100%则使用二分查找"}]}