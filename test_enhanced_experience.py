#!/usr/bin/env python3
"""
测试增强的经验管理功能
"""

import asyncio
import json
import time
from datetime import datetime
from experience_manager import ExperienceManager, IterationStep, ToolExecution


async def test_enhanced_experience_manager():
    """测试增强的经验管理器功能"""
    print("=== 测试增强的经验管理器 ===")
    
    # 初始化经验管理器
    try:
        manager = ExperienceManager()
        print("✓ 经验管理器初始化成功")
    except Exception as e:
        print(f"✗ 经验管理器初始化失败: {e}")
        return
    
    # 创建模拟的详细执行过程
    print("\n--- 创建模拟执行过程 ---")
    
    # 第一次迭代
    tool_exec_1 = ToolExecution(
        tool_name="stats-query-mcp_mcp_tool_list_statistics_tables",
        parameters={"connection_id": "conn_123"},
        response='{"success": true, "tables": ["summary", "detail", "flow"]}',
        execution_time=0.5,
        success=True,
        timestamp=datetime.now().isoformat()
    )
    
    iteration_1 = IterationStep(
        iteration=1,
        thought="我需要先查看有哪些统计表可用",
        action="stats-query-mcp_mcp_tool_list_statistics_tables",
        action_input={"connection_id": "conn_123"},
        observation="找到了3个统计表：summary, detail, flow",
        tool_executions=[tool_exec_1],
        duration=2.1
    )
    
    # 第二次迭代
    tool_exec_2 = ToolExecution(
        tool_name="stats-query-mcp_mcp_tool_query_statistics_table",
        parameters={
            "table": "summary",
            "time_range": "2025-07-17 00:00:00,2025-07-17 23:59:59",
            "fields": ["total_byte", "total_packet", "total_bitps"]
        },
        response='{"success": true, "data": "total_byte,total_packet,total_bitps\\n615721309429,900069199,57011892.214400"}',
        execution_time=1.2,
        success=True,
        timestamp=datetime.now().isoformat()
    )
    
    iteration_2 = IterationStep(
        iteration=2,
        thought="现在查询summary表获取统计数据",
        action="stats-query-mcp_mcp_tool_query_statistics_table",
        action_input={
            "table": "summary",
            "time_range": "2025-07-17 00:00:00,2025-07-17 23:59:59",
            "fields": ["total_byte", "total_packet", "total_bitps"]
        },
        observation="成功获取了统计数据",
        tool_executions=[tool_exec_2],
        duration=3.2
    )
    
    iterations = [iteration_1, iteration_2]
    
    # 测试保存详细经验
    print("\n--- 测试保存详细经验 ---")
    test_query = "查询192.168.163.209上链路一的2025-07-17一天的概要统计"
    test_answer = '{"success": true, "data": "total_byte,total_packet,total_bitps\\n615721309429,900069199,57011892.214400"}'
    test_tools = ["stats-query-mcp_mcp_tool_list_statistics_tables", "stats-query-mcp_mcp_tool_query_statistics_table"]
    
    success = await manager.save_experience(
        query=test_query,
        final_answer=test_answer,
        execution_time=18.16,
        token_usage=0,
        tools_used=test_tools,
        success=True,
        iterations=iterations,
        metadata={
            "iterations": 2,
            "model": "gpt-4",
            "provider": "openai"
        }
    )
    
    if success:
        print("✓ 详细经验保存成功")
    else:
        print("✗ 详细经验保存失败")
    
    # 测试查找相似经验
    print("\n--- 测试查找相似经验 ---")
    similar_query = "查询192.168.163.209的统计信息"
    similar_experiences = await manager.find_similar_experiences(similar_query)
    
    print(f"找到 {len(similar_experiences)} 个相似经验:")
    for i, (exp, similarity) in enumerate(similar_experiences):
        print(f"  {i+1}. 相似度: {similarity:.3f}")
        print(f"     查询: {exp.query}")
        print(f"     迭代数: {exp.total_iterations}")
        print(f"     工具数: {len(exp.tools_used)}")
        print()
    
    # 测试生成详细few-shot示例
    print("--- 测试生成详细few-shot示例 ---")
    if similar_experiences:
        exp, similarity = similar_experiences[0]
        detailed_example = manager.generate_detailed_few_shot_example(exp, similarity)
        print("生成的详细few-shot示例:")
        print("=" * 80)
        print(detailed_example)
        print("=" * 80)
    
    # 检查经验文件内容
    print("\n--- 检查经验文件内容 ---")
    if manager.experiences:
        latest_exp = manager.experiences[-1]
        print(f"最新经验包含 {len(latest_exp.iterations)} 个迭代步骤:")
        
        for i, iteration in enumerate(latest_exp.iterations, 1):
            print(f"  迭代 {i}:")
            print(f"    思考: {iteration.thought[:50]}...")
            print(f"    动作: {iteration.action}")
            print(f"    工具执行数: {len(iteration.tool_executions)}")
            
            for j, tool_exec in enumerate(iteration.tool_executions):
                print(f"      工具 {j+1}: {tool_exec.tool_name}")
                print(f"        参数: {str(tool_exec.parameters)[:50]}...")
                print(f"        响应: {tool_exec.response[:50]}...")
                print(f"        成功: {tool_exec.success}")
                print(f"        耗时: {tool_exec.execution_time:.3f}s")
            print()
    
    # 测试经验统计
    print("--- 经验统计 ---")
    summary = manager.get_experience_summary()
    print(f"总经验数: {summary['total_experiences']}")
    print(f"成功经验数: {summary['successful_experiences']}")
    print(f"成功率: {summary['success_rate']:.2%}")
    print(f"平均执行时间: {summary['avg_execution_time']:.2f}s")
    print(f"平均token使用: {summary['avg_token_usage']:.0f}")
    print(f"最常用工具: {summary['most_used_tools'][:3]}")


async def test_experience_file_structure():
    """测试经验文件结构"""
    print("\n=== 测试经验文件结构 ===")
    
    manager = ExperienceManager()
    
    if manager.experiences:
        # 导出一个经验样本用于查看结构
        sample_exp = manager.experiences[-1]
        # 检查iterations是否为IterationStep对象列表
        if sample_exp.iterations and hasattr(sample_exp.iterations[0], 'iteration'):
            # 是IterationStep对象
            iterations_data = [
                {
                    "iteration": iter_step.iteration,
                    "thought": iter_step.thought[:100] + "..." if len(iter_step.thought) > 100 else iter_step.thought,
                    "action": iter_step.action,
                    "action_input": iter_step.action_input,
                    "tool_executions": [
                        {
                            "tool_name": tool_exec.tool_name,
                            "parameters": tool_exec.parameters,
                            "response": tool_exec.response[:100] + "..." if len(tool_exec.response) > 100 else tool_exec.response,
                            "success": tool_exec.success,
                            "execution_time": tool_exec.execution_time
                        }
                        for tool_exec in iter_step.tool_executions
                    ],
                    "duration": iter_step.duration
                }
                for iter_step in sample_exp.iterations
            ]
        else:
            # 可能是从文件加载的字典数据
            iterations_data = sample_exp.iterations

        sample_data = {
            "query": sample_exp.query,
            "total_iterations": sample_exp.total_iterations,
            "iterations": iterations_data,
            "final_answer": sample_exp.final_answer[:200] + "..." if len(sample_exp.final_answer) > 200 else sample_exp.final_answer,
            "execution_time": sample_exp.execution_time,
            "tools_used": sample_exp.tools_used
        }
        
        print("经验数据结构样本:")
        print(json.dumps(sample_data, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    asyncio.run(test_enhanced_experience_manager())
    asyncio.run(test_experience_file_structure())
