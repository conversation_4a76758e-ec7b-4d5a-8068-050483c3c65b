#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新添加的MCP工具功能
"""

import json

def test_new_features():
    """测试新添加的MCP工具功能的示例代码"""
    
    print("=== 新增MCP工具功能测试示例 ===\n")
    
    # 1. 配置获取示例
    print("1. 获取配置信息示例:")
    print("   get_config(config_type='application', netlink_id=1)")
    print("   - 获取链路1的应用配置")
    print("   get_config(config_type='adapter', netlink_id=64)")
    print("   - 获取系统接口配置\n")
    
    # 2. 配置设置示例
    print("2. 设置配置信息示例:")
    print("   set_config(")
    print("       config_type='application',")
    print("       xml_config='<Element handle=\"application\" version=\"1\" netlink_id=\"1\">...</Element>',")
    print("       netlink_id=1")
    print("   )")
    print("   - 设置链路1的应用配置\n")
    
    # 3. 枚举统计表示例
    print("3. 枚举统计表示例:")
    print("   list_statistics_tables()")
    print("   - 返回所有可用的统计表列表\n")
    
    # 4. 枚举统计表字段示例
    print("4. 枚举统计表字段示例:")
    print("   list_table_fields(table_name='tcp_flow')")
    print("   - 返回tcp_flow表的所有字段信息\n")
    
    # 5. 下载数据包示例
    print("5. 下载数据包示例:")
    print("   download_packets(")
    print("       netlink_id=1,")
    print("       begin_time='2025-01-15 10:00:00',")
    print("       end_time='2025-01-15 10:05:00',")
    print("       filter_condition='ip_addr=*************'")
    print("   )")
    print("   - 下载指定时间段内特定IP的数据包\n")
    
    # 6. 数据包概要解码示例
    print("6. 数据包概要解码示例:")
    print("   get_packet_summary_decode(")
    print("       netlink_id=1,")
    print("       begin_time='2025-01-15 10:00:00',")
    print("       end_time='2025-01-15 10:05:00',")
    print("       filter_condition='port=80',")
    print("       max_packets=50")
    print("   )")
    print("   - 获取指定时间段内端口80的数据包概要解码信息\n")
    
    # 7. 辅助工具示例
    print("7. 辅助工具示例:")
    print("   get_config_types()")
    print("   - 获取所有支持的配置类型说明")
    print("   get_supported_filters()")
    print("   - 获取支持的过滤器对象和使用说明\n")
    
    # 8. 完整使用流程示例
    print("=== 完整使用流程示例 ===")
    print("# Step 1: 连接API")
    print("setup_api_connection(url='https://***************:8080/')")
    print()
    print("# Step 2: 枚举可用的统计表")
    print("list_statistics_tables()")
    print()
    print("# Step 3: 查看特定表的字段")
    print("list_table_fields(table_name='service_access')")
    print()
    print("# Step 4: 查询统计数据")
    print("query_statistics_table(")
    print("    table='service_access',")
    print("    begintime='2025-01-15 10:00:00',")
    print("    endtime='2025-01-15 11:00:00',")
    print("    fields=['server_ip_addr', 'server_port', 'total_byte'],")
    print("    keys=['server_ip_addr', 'server_port']")
    print(")")
    print()
    print("# Step 5: 下载数据包")
    print("download_packets(")
    print("    netlink_id=1,")
    print("    begin_time='2025-01-15 10:00:00',")
    print("    end_time='2025-01-15 10:05:00',")
    print("    filter_condition='server_port=443'")
    print(")")
    print()
    print("# Step 6: 断开连接")
    print("disconnect_api()")

if __name__ == '__main__':
    test_new_features() 