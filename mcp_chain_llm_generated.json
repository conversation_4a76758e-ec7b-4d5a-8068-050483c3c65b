{"meta": {"generated_at": "2025-06-18T13:28:18.887926", "alert_info": {"server_ip": "*************", "link_id": 2, "trigger_time": "2025-06-16 14:00:00", "alert_name": "TCP重传率100%警报", "condition": "TCP重传率为100% 大于 90%"}, "total_steps": 3, "has_branches": false, "diagnostic_strategy": "emergency_fallback", "total_branches": 0, "estimated_total_duration": "15-25秒"}, "steps": [{"step_id": 1, "step_type": "sequential", "tool": "setup_api_connection", "purpose": "建立API连接", "parameters": {"url": "https://***************:8080/"}, "reasoning": "建立基础连接", "required": true, "estimated_duration": "2-5秒"}, {"step_id": 2, "step_type": "sequential", "tool": "query_statistics_table", "purpose": "基础统计查询", "parameters": {"table": "service_access", "begintime": "2025-06-16 13:55:00", "endtime": "2025-06-16 14:00:00", "fields": ["server_ip_addr", "total_packet"], "keys": ["server_ip_addr"], "timeunit": 1000, "filter_condition": "server_ip_addr=*************", "netlink": 2}, "reasoning": "获取基础统计", "required": true, "estimated_duration": "3-8秒"}, {"step_id": 3, "step_type": "sequential", "tool": "get_detailed_packet_decode", "purpose": "基础包解码", "parameters": {"begin_time": "2025-06-16 13:59:50", "end_time": "2025-06-16 14:00:00", "server_ip": "*************", "protocol": "TCP", "decode_options": "basic"}, "reasoning": "基础包分析", "required": true, "estimated_duration": "10-15秒"}], "branches": {}}