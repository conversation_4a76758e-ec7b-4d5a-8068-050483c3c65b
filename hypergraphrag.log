2025-06-18 10:32:46,348 - hypergraphrag - INFO - <PERSON><PERSON> initialized for working directory: packet_drop_rag_cache
2025-06-18 10:32:46,348 - hypergraphrag - INFO - Creating working directory packet_drop_rag_cache
2025-06-18 10:32:46,348 - hypergraphrag - INFO - Load KV llm_response_cache with 0 data
2025-06-18 10:32:46,349 - hypergraphrag - INFO - Load KV full_docs with 0 data
2025-06-18 10:32:46,349 - hypergraphrag - INFO - Load KV text_chunks with 0 data
2025-06-18 10:32:46,350 - hypergraphrag - INFO - [New Docs] inserting 3 docs
2025-06-18 10:32:46,772 - hypergraphrag - INFO - [New Chunks] inserting 3 chunks
2025-06-18 10:32:46,772 - hypergraphrag - INFO - Inserting 3 vectors to chunks
2025-06-18 10:32:46,774 - hypergraphrag - INFO - Writing graph with 0 nodes, 0 edges
2025-06-18 10:33:53,777 - hypergraphrag - INFO - <PERSON><PERSON> initialized for working directory: packet_drop_rag_cache
2025-06-18 10:33:53,777 - hypergraphrag - INFO - Load KV llm_response_cache with 0 data
2025-06-18 10:33:53,778 - hypergraphrag - INFO - Load KV full_docs with 0 data
2025-06-18 10:33:53,778 - hypergraphrag - INFO - Load KV text_chunks with 0 data
2025-06-18 10:33:53,779 - hypergraphrag - INFO - Loaded graph from packet_drop_rag_cache/graph_chunk_entity_relation.graphml with 0 nodes, 0 edges
2025-06-18 10:33:53,781 - hypergraphrag - INFO - [New Docs] inserting 3 docs
2025-06-18 10:33:54,202 - hypergraphrag - INFO - [New Chunks] inserting 3 chunks
2025-06-18 10:33:54,202 - hypergraphrag - INFO - Inserting 3 vectors to chunks
2025-06-18 10:33:55,871 - hypergraphrag - INFO - Writing graph with 0 nodes, 0 edges
2025-06-18 10:34:28,149 - hypergraphrag - INFO - Logger initialized for working directory: packet_drop_rag_cache
2025-06-18 10:34:28,150 - hypergraphrag - INFO - Creating working directory packet_drop_rag_cache
2025-06-18 10:34:28,150 - hypergraphrag - INFO - Load KV llm_response_cache with 0 data
2025-06-18 10:34:28,150 - hypergraphrag - INFO - Load KV full_docs with 0 data
2025-06-18 10:34:28,150 - hypergraphrag - INFO - Load KV text_chunks with 0 data
2025-06-18 10:34:28,151 - hypergraphrag - INFO - [New Docs] inserting 3 docs
2025-06-18 10:34:28,602 - hypergraphrag - INFO - [New Chunks] inserting 3 chunks
2025-06-18 10:34:28,602 - hypergraphrag - INFO - Inserting 3 vectors to chunks
2025-06-18 10:34:30,808 - hypergraphrag - INFO - Writing graph with 0 nodes, 0 edges
2025-06-18 10:35:28,093 - hypergraphrag - INFO - Logger initialized for working directory: packet_drop_rag_cache
2025-06-18 10:35:28,093 - hypergraphrag - INFO - Creating working directory packet_drop_rag_cache
2025-06-18 10:35:28,093 - hypergraphrag - INFO - Load KV llm_response_cache with 0 data
2025-06-18 10:35:28,093 - hypergraphrag - INFO - Load KV full_docs with 0 data
2025-06-18 10:35:28,093 - hypergraphrag - INFO - Load KV text_chunks with 0 data
2025-06-18 10:35:28,094 - hypergraphrag - INFO - [New Docs] inserting 3 docs
2025-06-18 10:35:28,532 - hypergraphrag - INFO - [New Chunks] inserting 3 chunks
2025-06-18 10:35:28,532 - hypergraphrag - INFO - Inserting 3 vectors to chunks
2025-06-18 10:35:29,682 - hypergraphrag - INFO - [Entity Extraction]...
2025-06-18 10:35:29,685 - hypergraphrag - INFO - Writing graph with 0 nodes, 0 edges
2025-06-18 10:36:35,642 - hypergraphrag - INFO - Logger initialized for working directory: packet_drop_rag_cache
2025-06-18 10:36:35,643 - hypergraphrag - INFO - Load KV llm_response_cache with 0 data
2025-06-18 10:36:35,643 - hypergraphrag - INFO - Load KV full_docs with 0 data
2025-06-18 10:36:35,643 - hypergraphrag - INFO - Load KV text_chunks with 0 data
2025-06-18 10:36:35,645 - hypergraphrag - INFO - Loaded graph from packet_drop_rag_cache/graph_chunk_entity_relation.graphml with 0 nodes, 0 edges
2025-06-18 10:36:35,646 - hypergraphrag - INFO - [New Docs] inserting 3 docs
2025-06-18 10:36:36,066 - hypergraphrag - INFO - [New Chunks] inserting 3 chunks
2025-06-18 10:36:36,067 - hypergraphrag - INFO - Inserting 3 vectors to chunks
2025-06-18 10:36:36,172 - hypergraphrag - INFO - Writing graph with 0 nodes, 0 edges
2025-06-18 10:38:10,377 - hypergraphrag - INFO - Logger initialized for working directory: packet_drop_rag_cache
2025-06-18 10:38:10,377 - hypergraphrag - INFO - Load KV llm_response_cache with 0 data
2025-06-18 10:38:10,378 - hypergraphrag - INFO - Load KV full_docs with 0 data
2025-06-18 10:38:10,378 - hypergraphrag - INFO - Load KV text_chunks with 0 data
2025-06-18 10:38:10,379 - hypergraphrag - INFO - Loaded graph from packet_drop_rag_cache/graph_chunk_entity_relation.graphml with 0 nodes, 0 edges
2025-06-18 10:38:10,381 - hypergraphrag - INFO - [New Docs] inserting 3 docs
2025-06-18 10:38:10,809 - hypergraphrag - INFO - [New Chunks] inserting 3 chunks
2025-06-18 10:38:10,809 - hypergraphrag - INFO - Inserting 3 vectors to chunks
2025-06-18 10:38:14,060 - hypergraphrag - INFO - [Entity Extraction]...
2025-06-18 10:43:57,841 - hypergraphrag - INFO - Writing graph with 0 nodes, 0 edges
2025-06-18 10:44:48,889 - hypergraphrag - INFO - Logger initialized for working directory: packet_drop_rag_cache
2025-06-18 10:44:48,889 - hypergraphrag - INFO - Load KV llm_response_cache with 0 data
2025-06-18 10:44:48,889 - hypergraphrag - INFO - Load KV full_docs with 0 data
2025-06-18 10:44:48,890 - hypergraphrag - INFO - Load KV text_chunks with 0 data
2025-06-18 10:44:48,891 - hypergraphrag - INFO - Loaded graph from packet_drop_rag_cache/graph_chunk_entity_relation.graphml with 0 nodes, 0 edges
2025-06-18 10:44:48,893 - hypergraphrag - INFO - [New Docs] inserting 3 docs
2025-06-18 10:44:49,319 - hypergraphrag - INFO - [New Chunks] inserting 3 chunks
2025-06-18 10:44:49,319 - hypergraphrag - INFO - Inserting 3 vectors to chunks
2025-06-18 10:44:50,647 - hypergraphrag - INFO - [Entity Extraction]...
2025-06-18 10:51:39,047 - hypergraphrag - INFO - Inserting hyperedges into storage...
2025-06-18 10:51:39,048 - hypergraphrag - INFO - Inserting entities into storage...
2025-06-18 10:51:39,056 - hypergraphrag - INFO - Inserting relationships into storage...
2025-06-18 10:51:39,059 - hypergraphrag - INFO - Inserting 32 vectors to hyperedges
2025-06-18 10:51:44,615 - hypergraphrag - INFO - Inserting 52 vectors to entities
2025-06-18 10:51:55,670 - hypergraphrag - INFO - Writing graph with 84 nodes, 110 edges
2025-06-18 10:52:20,468 - hypergraphrag - INFO - kw_prompt result:
2025-06-18 10:52:21,198 - hypergraphrag - INFO - Local query uses 20 entites, 44 relations, 3 text units
2025-06-18 10:52:22,876 - hypergraphrag - INFO - Global query uses 45 entites, 20 relations, 3 text units
2025-06-18 10:53:49,881 - hypergraphrag - INFO - kw_prompt result:
2025-06-18 10:53:50,686 - hypergraphrag - INFO - Local query uses 20 entites, 39 relations, 3 text units
2025-06-18 10:53:51,548 - hypergraphrag - INFO - Global query uses 40 entites, 20 relations, 3 text units
2025-06-18 10:55:57,135 - hypergraphrag - INFO - kw_prompt result:
2025-06-18 10:55:58,062 - hypergraphrag - INFO - Local query uses 20 entites, 33 relations, 3 text units
2025-06-18 10:55:58,779 - hypergraphrag - INFO - Global query uses 42 entites, 20 relations, 3 text units
2025-06-18 10:57:12,450 - hypergraphrag - INFO - kw_prompt result:
2025-06-18 10:57:13,184 - hypergraphrag - INFO - Local query uses 20 entites, 47 relations, 3 text units
2025-06-18 10:57:14,041 - hypergraphrag - INFO - Global query uses 47 entites, 20 relations, 3 text units
2025-06-18 10:59:23,339 - hypergraphrag - INFO - kw_prompt result:
2025-06-18 10:59:24,060 - hypergraphrag - INFO - Local query uses 20 entites, 45 relations, 3 text units
2025-06-18 10:59:24,862 - hypergraphrag - INFO - Global query uses 38 entites, 20 relations, 3 text units
2025-06-18 11:37:18,952 - hypergraphrag - INFO - Logger initialized for working directory: packet_drop_rag_cache
2025-06-18 11:37:18,953 - hypergraphrag - INFO - Load KV llm_response_cache with 1 data
2025-06-18 11:37:18,953 - hypergraphrag - INFO - Load KV full_docs with 3 data
2025-06-18 11:37:18,954 - hypergraphrag - INFO - Load KV text_chunks with 3 data
2025-06-18 11:37:18,959 - hypergraphrag - INFO - Loaded graph from packet_drop_rag_cache/graph_chunk_entity_relation.graphml with 84 nodes, 110 edges
2025-06-18 11:37:18,968 - hypergraphrag - WARNING - All docs are already in the storage
2025-06-18 11:38:58,437 - hypergraphrag - INFO - kw_prompt result:
2025-06-18 11:38:59,996 - hypergraphrag - INFO - Local query uses 20 entites, 46 relations, 3 text units
2025-06-18 11:39:00,429 - hypergraphrag - INFO - Global query uses 37 entites, 20 relations, 3 text units
2025-06-18 13:48:46,807 - hypergraphrag - INFO - Logger initialized for working directory: packet_drop_rag_cache
2025-06-18 13:48:46,808 - hypergraphrag - INFO - Load KV llm_response_cache with 1 data
2025-06-18 13:48:46,809 - hypergraphrag - INFO - Load KV full_docs with 3 data
2025-06-18 13:48:46,809 - hypergraphrag - INFO - Load KV text_chunks with 3 data
2025-06-18 13:48:46,815 - hypergraphrag - INFO - Loaded graph from packet_drop_rag_cache/graph_chunk_entity_relation.graphml with 84 nodes, 110 edges
2025-06-18 13:48:46,824 - hypergraphrag - WARNING - All docs are already in the storage
2025-06-18 13:49:46,365 - hypergraphrag - INFO - kw_prompt result:
2025-06-18 13:49:47,811 - hypergraphrag - INFO - Local query uses 20 entites, 39 relations, 3 text units
2025-06-18 13:49:49,152 - hypergraphrag - INFO - Global query uses 38 entites, 20 relations, 3 text units
2025-06-18 13:54:50,843 - hypergraphrag - INFO - Logger initialized for working directory: packet_drop_rag_cache
2025-06-18 13:54:50,845 - hypergraphrag - INFO - Load KV llm_response_cache with 1 data
2025-06-18 13:54:50,845 - hypergraphrag - INFO - Load KV full_docs with 3 data
2025-06-18 13:54:50,845 - hypergraphrag - INFO - Load KV text_chunks with 3 data
2025-06-18 13:54:50,852 - hypergraphrag - INFO - Loaded graph from packet_drop_rag_cache/graph_chunk_entity_relation.graphml with 84 nodes, 110 edges
2025-06-18 13:54:50,860 - hypergraphrag - WARNING - All docs are already in the storage
2025-06-18 13:55:36,897 - hypergraphrag - INFO - Logger initialized for working directory: packet_drop_rag_cache
2025-06-18 13:55:36,899 - hypergraphrag - INFO - Load KV llm_response_cache with 1 data
2025-06-18 13:55:36,899 - hypergraphrag - INFO - Load KV full_docs with 3 data
2025-06-18 13:55:36,899 - hypergraphrag - INFO - Load KV text_chunks with 3 data
2025-06-18 13:55:36,906 - hypergraphrag - INFO - Loaded graph from packet_drop_rag_cache/graph_chunk_entity_relation.graphml with 84 nodes, 110 edges
2025-06-18 13:55:36,914 - hypergraphrag - WARNING - All docs are already in the storage
2025-06-18 14:17:45,724 - hypergraphrag - INFO - Logger initialized for working directory: packet_drop_rag_cache
2025-06-18 14:17:45,725 - hypergraphrag - INFO - Load KV llm_response_cache with 1 data
2025-06-18 14:17:45,725 - hypergraphrag - INFO - Load KV full_docs with 3 data
2025-06-18 14:17:45,726 - hypergraphrag - INFO - Load KV text_chunks with 3 data
2025-06-18 14:17:45,732 - hypergraphrag - INFO - Loaded graph from packet_drop_rag_cache/graph_chunk_entity_relation.graphml with 84 nodes, 110 edges
2025-06-18 14:17:45,741 - hypergraphrag - WARNING - All docs are already in the storage
2025-06-18 14:19:17,522 - hypergraphrag - INFO - kw_prompt result:
2025-06-18 14:19:18,588 - hypergraphrag - INFO - Local query uses 20 entites, 46 relations, 3 text units
2025-06-18 14:19:27,752 - hypergraphrag - INFO - Global query uses 38 entites, 20 relations, 3 text units
2025-06-18 14:29:02,466 - hypergraphrag - INFO - Logger initialized for working directory: packet_drop_rag_cache
2025-06-18 14:29:02,466 - hypergraphrag - INFO - Load KV llm_response_cache with 1 data
2025-06-18 14:29:02,467 - hypergraphrag - INFO - Load KV full_docs with 3 data
2025-06-18 14:29:02,467 - hypergraphrag - INFO - Load KV text_chunks with 3 data
2025-06-18 14:29:02,474 - hypergraphrag - INFO - Loaded graph from packet_drop_rag_cache/graph_chunk_entity_relation.graphml with 84 nodes, 110 edges
2025-06-18 14:29:02,483 - hypergraphrag - WARNING - All docs are already in the storage
2025-06-18 14:55:01,920 - hypergraphrag - INFO - Logger initialized for working directory: packet_drop_rag_cache
2025-06-18 14:55:01,921 - hypergraphrag - INFO - Load KV llm_response_cache with 1 data
2025-06-18 14:55:01,922 - hypergraphrag - INFO - Load KV full_docs with 3 data
2025-06-18 14:55:01,922 - hypergraphrag - INFO - Load KV text_chunks with 3 data
2025-06-18 14:55:01,928 - hypergraphrag - INFO - Loaded graph from packet_drop_rag_cache/graph_chunk_entity_relation.graphml with 84 nodes, 110 edges
2025-06-18 14:55:01,937 - hypergraphrag - WARNING - All docs are already in the storage
2025-06-18 14:56:02,565 - hypergraphrag - INFO - kw_prompt result:
2025-06-18 14:56:04,397 - hypergraphrag - INFO - Local query uses 20 entites, 43 relations, 3 text units
2025-06-18 14:56:05,066 - hypergraphrag - INFO - Global query uses 46 entites, 20 relations, 3 text units
2025-06-20 09:53:16,292 - hypergraphrag - INFO - Logger initialized for working directory: packet_drop_rag_cache
2025-06-20 09:53:16,293 - hypergraphrag - INFO - Load KV llm_response_cache with 1 data
2025-06-20 09:53:16,293 - hypergraphrag - INFO - Load KV full_docs with 3 data
2025-06-20 09:53:16,293 - hypergraphrag - INFO - Load KV text_chunks with 3 data
2025-06-20 09:53:16,300 - hypergraphrag - INFO - Loaded graph from packet_drop_rag_cache/graph_chunk_entity_relation.graphml with 84 nodes, 110 edges
2025-06-20 09:53:16,308 - hypergraphrag - WARNING - All docs are already in the storage
