#!/usr/bin/env python3
"""
测试脚本：验证基于key字段的过滤功能
"""

import asyncio
import json
from packet_drop_hypergraph_rag import PacketDropHyperGraphRAG
from autogen_mcp_executor import get_llm_generated_mcp_chain, get_minimal_fallback_chain


async def test_key_filtering():
    """测试key字段过滤功能"""
    
    print("=== 测试基于key字段的过滤功能 ===\n")
    
    # 模拟告警信息 - TCP会话跳包
    test_alert_info = {
        "machine_ip": "***************",        # API连接用
        "server_ip_addr": "********00",         # 数据库查询用
        "client_ip_addr": "************",       # 数据库查询用
        "server_port": "80",                     # 服务器端口
        "client_port": "12345",                  # 客户端端口
        "protocol": "TCP",
        "duration": 1.5,                         # 短期会话
        "trigger_time": "2025-06-18 14:30:00"
    }
    
    print("1. 测试告警信息:")
    print(json.dumps(test_alert_info, indent=2, ensure_ascii=False))
    print()
    
    # 测试1：备用MCP链的key过滤
    print("2. 测试备用MCP链的key字段过滤:")
    fallback_chain = get_minimal_fallback_chain(test_alert_info)
    
    # 验证TCP 4元组过滤
    tcp_flow_step = None
    tcp_server_port_step = None
    ip_flow_step = None
    
    for step in fallback_chain.get('steps', []):
        if step.get('tool') == 'query_statistics_table':
            table_name = step.get('parameters', {}).get('table_name', '')
            if table_name == 'tcp_flow':
                tcp_flow_step = step
            elif table_name == 'tcp_server_port':
                tcp_server_port_step = step
            elif table_name == 'ip_flow':
                ip_flow_step = step
    
    print("TCP Flow 4元组过滤条件:")
    if tcp_flow_step:
        filters = tcp_flow_step.get('parameters', {}).get('filters', '')
        print(f"  过滤条件: {filters}")
        
        # 验证是否包含4元组字段
        expected_parts = [
            "client_ip_addr=************",
            "server_ip_addr=********00", 
            "client_port=12345",
            "server_port=80"
        ]
        
        for part in expected_parts:
            if part in filters:
                print(f"  ✓ 包含: {part}")
            else:
                print(f"  ✗ 缺失: {part}")
    
    print("\nTCP Server Port过滤条件:")
    if tcp_server_port_step:
        filters = tcp_server_port_step.get('parameters', {}).get('filters', '')
        print(f"  过滤条件: {filters}")
        
        expected_parts = [
            "server_ip_addr=********00",
            "server_port=80"
        ]
        
        for part in expected_parts:
            if part in filters:
                print(f"  ✓ 包含: {part}")
            else:
                print(f"  ✗ 缺失: {part}")
    
    print("\nIP Flow过滤条件:")
    if ip_flow_step:
        filters = ip_flow_step.get('parameters', {}).get('filters', '')
        print(f"  过滤条件: {filters}")
        
        expected_parts = [
            "ip_endpoint1=************",
            "ip_endpoint2=********00"
        ]
        
        for part in expected_parts:
            if part in filters:
                print(f"  ✓ 包含: {part}")
            else:
                print(f"  ✗ 缺失: {part}")
    
    print("\n" + "="*60)
    
    # 测试2：LLM生成的MCP链（如果可用）
    try:
        print("\n3. 测试LLM生成MCP链的key字段过滤:")
        llm_chain = await get_llm_generated_mcp_chain(test_alert_info)
        
        print("LLM生成的链中的过滤条件:")
        for i, step in enumerate(llm_chain.get('steps', []), 1):
            if step.get('tool') == 'query_statistics_table':
                table_name = step.get('parameters', {}).get('table_name', '')
                filters = step.get('parameters', {}).get('filters', '')
                print(f"  步骤{i} - {table_name}: {filters}")
        
    except Exception as e:
        print(f"LLM测试跳过 (原因: {e})")
    
    print("\n" + "="*60)
    
    # 测试3：验证过滤条件的正确性
    print("\n4. 过滤条件语法验证:")
    
    test_filters = [
        "client_ip_addr=************&&server_ip_addr=********00&&client_port=12345&&server_port=80",
        "server_ip_addr=********00&&server_port=80",
        "ip_endpoint1=************&&ip_endpoint2=********00",
        "ip_addr=************"
    ]
    
    for filter_condition in test_filters:
        print(f"  过滤条件: {filter_condition}")
        
        # 检查是否包含正确的操作符
        if "&&" in filter_condition:
            print(f"    ✓ 使用了正确的AND操作符 (&&)")
        
        # 检查是否包含等号
        if "=" in filter_condition:
            print(f"    ✓ 使用了等号操作符")
        
        # 检查是否有IP地址
        if any(part.startswith(prefix) for part in filter_condition.split("&&") 
               for prefix in ["client_ip_addr=", "server_ip_addr=", "ip_addr=", "ip_endpoint1=", "ip_endpoint2="]):
            print(f"    ✓ 包含IP地址过滤")
        
        print()
    
    print("=== 测试完成 ===")


def test_filter_construction():
    """测试过滤条件构建"""
    print("\n=== 过滤条件构建测试 ===")
    
    # 测试数据
    test_cases = [
        {
            "name": "完整4元组",
            "client_ip": "*************", 
            "server_ip": "********",
            "client_port": "12345",
            "server_port": "80"
        },
        {
            "name": "只有IP地址",
            "client_ip": "*************",
            "server_ip": "********", 
            "client_port": "",
            "server_port": ""
        },
        {
            "name": "包含服务器端口",
            "client_ip": "*************",
            "server_ip": "********",
            "client_port": "",
            "server_port": "443"
        }
    ]
    
    for case in test_cases:
        print(f"\n测试案例: {case['name']}")
        
        # TCP Flow 4元组过滤
        tcp_filter = ""
        if case["client_ip"] and case["server_ip"]:
            tcp_filter = f"client_ip_addr={case['client_ip']}&&server_ip_addr={case['server_ip']}"
            if case["client_port"]:
                tcp_filter += f"&&client_port={case['client_port']}"
            if case["server_port"]:
                tcp_filter += f"&&server_port={case['server_port']}"
        
        print(f"  TCP Flow过滤: {tcp_filter}")
        
        # TCP Server Port过滤
        server_filter = ""
        if case["server_ip"]:
            server_filter = f"server_ip_addr={case['server_ip']}"
            if case["server_port"]:
                server_filter += f"&&server_port={case['server_port']}"
        
        print(f"  Server Port过滤: {server_filter}")
        
        # IP Flow过滤
        ip_filter = ""
        if case["client_ip"] and case["server_ip"]:
            ip_filter = f"ip_endpoint1={case['client_ip']}&&ip_endpoint2={case['server_ip']}"
        
        print(f"  IP Flow过滤: {ip_filter}")


if __name__ == "__main__":
    # 运行异步测试
    asyncio.run(test_key_filtering())
    
    # 运行同步测试
    test_filter_construction() 