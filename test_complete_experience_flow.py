#!/usr/bin/env python3
"""
测试完整的经验管理流程
"""

import asyncio
import json
from experience_manager import ExperienceManager, IterationStep, ToolExecution
from datetime import datetime


async def create_sample_experience():
    """创建示例经验数据"""
    manager = ExperienceManager()
    
    # 创建第一个经验：查询链路信息
    tool_exec_1 = ToolExecution(
        tool_name="stats-query-mcp_mcp_tool_setup_api_connection",
        parameters={"host": "***************", "port": 8080},
        response='{"success": true, "connection_id": "conn_123"}',
        execution_time=0.5,
        success=True,
        timestamp=datetime.now().isoformat()
    )
    
    tool_exec_2 = ToolExecution(
        tool_name="stats-query-mcp_mcp_tool_query_statistics_table",
        parameters={
            "table": "summary",
            "time_range": "2025-07-17 00:00:00,2025-07-17 23:59:59",
            "fields": ["total_byte", "total_packet", "total_bitps"]
        },
        response='{"success": true, "data": "total_byte,total_packet,total_bitps\\n615721309429,900069199,57011892.214400"}',
        execution_time=1.2,
        success=True,
        timestamp=datetime.now().isoformat()
    )
    
    iteration_1 = IterationStep(
        iteration=1,
        thought="需要先建立API连接",
        action="stats-query-mcp_mcp_tool_setup_api_connection",
        action_input={"host": "***************", "port": 8080},
        observation="连接建立成功",
        tool_executions=[tool_exec_1],
        duration=1.0
    )
    
    iteration_2 = IterationStep(
        iteration=2,
        thought="现在查询统计表获取数据",
        action="stats-query-mcp_mcp_tool_query_statistics_table",
        action_input={
            "table": "summary",
            "time_range": "2025-07-17 00:00:00,2025-07-17 23:59:59",
            "fields": ["total_byte", "total_packet", "total_bitps"]
        },
        observation="成功获取统计数据",
        tool_executions=[tool_exec_2],
        duration=2.0
    )
    
    # 保存第一个经验
    await manager.save_experience(
        query="查询***************上链路一的2025-07-17统计数据",
        final_answer='{"success": true, "data": "total_byte,total_packet,total_bitps\\n615721309429,900069199,57011892.214400"}',
        execution_time=15.5,
        token_usage=120,
        tools_used=["stats-query-mcp_mcp_tool_setup_api_connection", "stats-query-mcp_mcp_tool_query_statistics_table"],
        success=True,
        iterations=[iteration_1, iteration_2]
    )
    
    print("✓ 创建了第一个经验")
    
    # 创建第二个经验：类似查询
    await manager.save_experience(
        query="获取***************的网络流量统计",
        final_answer='{"success": true, "message": "查询成功", "data": "flow_count,byte_count\\n110328351,615721309429"}',
        execution_time=12.3,
        token_usage=95,
        tools_used=["stats-query-mcp_mcp_tool_setup_api_connection", "stats-query-mcp_mcp_tool_query_statistics_table"],
        success=True,
        iterations=[]  # 简化，不包含详细迭代
    )
    
    print("✓ 创建了第二个经验")
    return manager


async def test_experience_retrieval():
    """测试经验检索"""
    print("\n=== 测试经验检索 ===")
    
    manager = await create_sample_experience()
    
    # 测试相似查询
    test_queries = [
        "查询***************的统计信息",
        "获取链路一的流量数据",
        "***************网络统计",
        "完全不相关的查询内容"
    ]
    
    for query in test_queries:
        print(f"\n查询: {query}")
        similar_experiences = await manager.find_similar_experiences(query, top_k=2)
        
        if similar_experiences:
            print(f"找到 {len(similar_experiences)} 个相似经验:")
            for i, (exp, similarity) in enumerate(similar_experiences, 1):
                print(f"  {i}. 相似度: {similarity:.3f}")
                print(f"     原查询: {exp.query}")
                print(f"     工具数: {len(exp.tools_used)}")
                print(f"     迭代数: {exp.total_iterations}")
        else:
            print("  未找到相似经验")


async def test_few_shot_generation():
    """测试few-shot示例生成"""
    print("\n=== 测试Few-shot示例生成 ===")
    
    manager = await create_sample_experience()
    
    # 模拟查询
    test_query = "查询***************的网络统计数据"
    similar_experiences = await manager.find_similar_experiences(test_query, top_k=1)
    
    if similar_experiences:
        exp, similarity = similar_experiences[0]
        few_shot_example = manager.generate_detailed_few_shot_example(exp, similarity)
        
        print("生成的Few-shot示例:")
        print("-" * 50)
        print(few_shot_example)
        print("-" * 50)
        
        # 验证示例质量
        print("示例质量检查:")
        print(f"✓ 长度适中: {len(few_shot_example)} 字符")
        print(f"✓ 包含工具序列: {'Tool sequence' in few_shot_example}")
        print(f"✓ 包含结果: {'Result:' in few_shot_example}")
        print(f"✓ 格式简洁: {few_shot_example.count('```') == 0}")


async def test_experience_statistics():
    """测试经验统计"""
    print("\n=== 测试经验统计 ===")
    
    manager = await create_sample_experience()
    summary = manager.get_experience_summary()
    
    print("经验统计:")
    print(f"- 总经验数: {summary['total_experiences']}")
    print(f"- 成功经验数: {summary['successful_experiences']}")
    print(f"- 成功率: {summary['success_rate']:.1%}")
    print(f"- 平均执行时间: {summary['avg_execution_time']:.2f}s")
    print(f"- 平均token使用: {summary['avg_token_usage']:.0f}")
    print(f"- 最常用工具: {summary['most_used_tools'][:3]}")


async def test_experience_file_persistence():
    """测试经验文件持久化"""
    print("\n=== 测试经验文件持久化 ===")
    
    # 创建经验
    manager1 = await create_sample_experience()
    print(f"第一个管理器: {len(manager1.experiences)} 条经验")
    
    # 创建新的管理器实例，应该能加载之前的经验
    manager2 = ExperienceManager()
    print(f"第二个管理器: {len(manager2.experiences)} 条经验")
    
    # 验证数据一致性
    if len(manager1.experiences) == len(manager2.experiences):
        print("✓ 经验数据持久化成功")
        
        # 检查最新经验的详细信息
        if manager2.experiences:
            latest_exp = manager2.experiences[-1]
            print(f"✓ 最新经验查询: {latest_exp.query}")
            print(f"✓ 迭代数: {latest_exp.total_iterations}")
            print(f"✓ 工具数: {len(latest_exp.tools_used)}")
            
            # 检查迭代信息是否正确加载
            if latest_exp.iterations:
                print(f"✓ 迭代详情加载成功: {len(latest_exp.iterations)} 个迭代")
                for i, iteration in enumerate(latest_exp.iterations, 1):
                    print(f"  迭代 {i}: {iteration.action}, {len(iteration.tool_executions)} 个工具执行")
            else:
                print("! 迭代详情为空（可能是简化的经验）")
    else:
        print("✗ 经验数据持久化失败")


async def main():
    """主测试函数"""
    print("开始完整的经验管理流程测试...\n")
    
    await test_experience_retrieval()
    await test_few_shot_generation()
    await test_experience_statistics()
    await test_experience_file_persistence()
    
    print("\n测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
