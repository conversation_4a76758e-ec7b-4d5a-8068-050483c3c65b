#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Few-shot示例改进工具 - 基于成功经验优化few-shot示例生成
"""

import json
import asyncio
from typing import Dict, List, Any, Tuple
from experience_manager import ExperienceManager, QueryExperience


class FewShotImprover:
    """Few-shot示例改进器"""
    
    def __init__(self, config_path: str = "experience_config.json"):
        """初始化改进器
        
        Args:
            config_path: 配置文件路径
        """
        self.experience_manager = ExperienceManager(config_path)
    
    def analyze_successful_patterns(self) -> Dict[str, Any]:
        """分析成功的查询模式"""
        successful_experiences = [exp for exp in self.experience_manager.experiences if exp.success]
        
        if not successful_experiences:
            return {"message": "No successful experiences found"}
        
        # 分析查询类型模式
        query_types = self._categorize_queries(successful_experiences)
        
        # 分析工具使用模式
        tool_patterns = self._analyze_tool_patterns(successful_experiences)
        
        # 分析参数模式
        parameter_patterns = self._analyze_parameter_patterns(successful_experiences)
        
        return {
            "query_types": query_types,
            "tool_patterns": tool_patterns,
            "parameter_patterns": parameter_patterns,
            "total_successful": len(successful_experiences)
        }
    
    def _categorize_queries(self, experiences: List[QueryExperience]) -> Dict[str, List[str]]:
        """将查询按类型分类"""
        categories = {
            "统计查询": [],
            "配置查询": [],
            "连接操作": [],
            "表字段查询": [],
            "其他": []
        }
        
        for exp in experiences:
            query = exp.query.lower()
            if any(keyword in query for keyword in ["统计", "查询", "数据", "概要"]):
                categories["统计查询"].append(exp.query)
            elif any(keyword in query for keyword in ["配置", "链路", "xml"]):
                categories["配置查询"].append(exp.query)
            elif any(keyword in query for keyword in ["连接", "登录", "api"]):
                categories["连接操作"].append(exp.query)
            elif any(keyword in query for keyword in ["字段", "表", "field", "table"]):
                categories["表字段查询"].append(exp.query)
            else:
                categories["其他"].append(exp.query)
        
        return categories
    
    def _analyze_tool_patterns(self, experiences: List[QueryExperience]) -> Dict[str, Any]:
        """分析工具使用模式"""
        # 统计工具序列
        sequences = []
        for exp in experiences:
            if exp.iterations:
                sequence = []
                for iteration in exp.iterations:
                    if iteration.action and iteration.tool_executions:
                        for tool_exec in iteration.tool_executions:
                            if tool_exec.success:
                                sequence.append(tool_exec.tool_name)
                if sequence:
                    sequences.append(sequence)
        
        # 找出最常见的工具序列模式
        from collections import Counter
        sequence_counter = Counter(tuple(seq) for seq in sequences)
        common_sequences = sequence_counter.most_common(10)
        
        # 分析工具成功率
        tool_success_rates = {}
        for exp in experiences:
            for iteration in exp.iterations:
                if iteration.tool_executions:
                    for tool_exec in iteration.tool_executions:
                        tool_name = tool_exec.tool_name
                        if tool_name not in tool_success_rates:
                            tool_success_rates[tool_name] = {"success": 0, "total": 0}
                        
                        tool_success_rates[tool_name]["total"] += 1
                        if tool_exec.success:
                            tool_success_rates[tool_name]["success"] += 1
        
        # 计算成功率
        for tool_name in tool_success_rates:
            stats = tool_success_rates[tool_name]
            stats["success_rate"] = stats["success"] / stats["total"] if stats["total"] > 0 else 0
        
        return {
            "common_sequences": [
                {"sequence": list(seq), "count": count} 
                for seq, count in common_sequences
            ],
            "tool_success_rates": tool_success_rates
        }
    
    def _analyze_parameter_patterns(self, experiences: List[QueryExperience]) -> Dict[str, Any]:
        """分析参数使用模式"""
        parameter_usage = {}
        
        for exp in experiences:
            for iteration in exp.iterations:
                if iteration.tool_executions:
                    for tool_exec in iteration.tool_executions:
                        if tool_exec.success and tool_exec.parameters:
                            tool_name = tool_exec.tool_name
                            if tool_name not in parameter_usage:
                                parameter_usage[tool_name] = {}
                            
                            for param_name, param_value in tool_exec.parameters.items():
                                if param_name not in parameter_usage[tool_name]:
                                    parameter_usage[tool_name][param_name] = []
                                
                                # 记录参数值（限制长度）
                                value_str = str(param_value)
                                if len(value_str) > 100:
                                    value_str = value_str[:100] + "..."
                                parameter_usage[tool_name][param_name].append(value_str)
        
        # 统计最常用的参数值
        common_parameters = {}
        for tool_name, params in parameter_usage.items():
            common_parameters[tool_name] = {}
            for param_name, values in params.items():
                from collections import Counter
                value_counter = Counter(values)
                common_parameters[tool_name][param_name] = value_counter.most_common(5)
        
        return common_parameters
    
    def generate_improved_few_shot_template(self, query_type: str = "统计查询") -> str:
        """生成改进的few-shot模板"""
        patterns = self.analyze_successful_patterns()
        
        if query_type not in patterns["query_types"]:
            return f"No patterns found for query type: {query_type}"
        
        # 找到该类型查询的最佳工具序列
        tool_patterns = patterns["tool_patterns"]
        best_sequence = None
        if tool_patterns["common_sequences"]:
            best_sequence = tool_patterns["common_sequences"][0]["sequence"]
        
        template = f"## Improved Few-shot Template for {query_type}\n\n"
        
        if best_sequence:
            template += "### Recommended Tool Execution Pattern:\n"
            for i, tool in enumerate(best_sequence, 1):
                tool_desc = self.experience_manager._get_tool_description(tool)
                template += f"{i}. {tool_desc}\n"
                template += f"   Tool: `{tool}`\n"
                
                # 添加常用参数示例
                if tool in patterns["parameter_patterns"]:
                    template += "   Common parameters:\n"
                    for param_name, values in patterns["parameter_patterns"][tool].items():
                        if values:
                            most_common = values[0][0]  # 最常用的值
                            template += f"     - {param_name}: {most_common}\n"
                template += "\n"
        
        # 添加成功率信息
        template += "### Tool Success Rates:\n"
        for tool_name, stats in tool_patterns["tool_success_rates"].items():
            if stats["success_rate"] > 0.8:  # 只显示高成功率的工具
                template += f"- {self.experience_manager._get_tool_description(tool_name)}: {stats['success_rate']:.1%}\n"
        
        return template
    
    def export_improved_templates(self, output_file: str = "improved_few_shot_templates.json"):
        """导出改进的模板"""
        patterns = self.analyze_successful_patterns()
        
        templates = {}
        for query_type in patterns["query_types"].keys():
            if patterns["query_types"][query_type]:  # 如果该类型有查询
                templates[query_type] = self.generate_improved_few_shot_template(query_type)
        
        output_data = {
            "generated_at": "2025-07-17",
            "analysis_summary": patterns,
            "improved_templates": templates,
            "usage_instructions": {
                "description": "Use these templates to generate better few-shot examples",
                "integration": "Integrate with experience_manager.generate_detailed_few_shot_example()",
                "customization": "Customize based on specific query patterns"
            }
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"Improved templates exported to: {output_file}")
        return output_data


def main():
    """主函数"""
    import sys
    
    improver = FewShotImprover()
    
    if len(sys.argv) < 2:
        print("用法:")
        print("  python improve_few_shot.py analyze     # 分析成功模式")
        print("  python improve_few_shot.py template    # 生成改进模板")
        print("  python improve_few_shot.py export      # 导出改进模板")
        return
    
    command = sys.argv[1]
    
    if command == "analyze":
        patterns = improver.analyze_successful_patterns()
        print(json.dumps(patterns, ensure_ascii=False, indent=2))
    elif command == "template":
        template = improver.generate_improved_few_shot_template()
        print(template)
    elif command == "export":
        improver.export_improved_templates()
    else:
        print("无效的命令")


if __name__ == "__main__":
    main()
